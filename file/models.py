from django.db import models


class Attachment(models.Model):
    name = models.CharField(max_length=100, null=True)
    size = models.BigIntegerField(null=True)
    type = models.CharField(max_length=100, null=True)
    file = models.FileField(null=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)
