import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from django.core.management import call_command
from planning.models import *
from user.factories import UserFactory, ProfileFactory
from address.factories import DzongkhagFactory
from django.core.files.uploadedfile import SimpleUploadedFile

from user.models import Role
from .factories import AttachmentFactory


class FileUploadTest(APITestCase):
    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        dzo = DzongkhagFactory()

        self.user = UserFactory.create(
            username="user",
            roles=Role.objects.filter(name__in=["thrmwsf"]).values_list(
                "id", flat=True
            ),
        )
        ProfileFactory(user=self.user, dzongkhag=dzo)

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": self.user.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.data = {
            "file": SimpleUploadedFile(
                "test.pdf", b"test file.", content_type="application/pdf"
            )
        }

    def test_file_upload(self):
        url = reverse("attachments")
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.post(url, self.data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue("data" in response_data)

    def test_file_detail(self):
        attachment = AttachmentFactory()
        url = reverse("attachments", kwargs={"pk": attachment.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.get(url, self.data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)

    def test_file_update(self):
        attachment = AttachmentFactory()
        url = reverse("attachments", kwargs={"pk": attachment.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, self.data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)

    def test_file_delete(self):
        attachment = AttachmentFactory()
        url = reverse("attachments", kwargs={"pk": attachment.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.delete(url, self.data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
