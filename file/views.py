from rest_framework import generics
from .models import Attachment
from .serializers import AttachmentSerializer
from rest_framework.exceptions import ValidationError
import os
import mimetypes


class AttachmentView(generics.ListCreateAPIView):
    queryset = Attachment.objects.all()
    serializer_class = AttachmentSerializer

    def perform_create(self, serializer):
        file = serializer.validated_data["file"]
        mime_type, encoding = mimetypes.guess_type(file.name)
        serializer.validated_data["name"] = file.name
        serializer.validated_data["size"] = file.size
        serializer.validated_data["type"] = mime_type
        super().perform_create(serializer)


class AttachmentPKView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Attachment.objects.all()
    serializer_class = AttachmentSerializer
