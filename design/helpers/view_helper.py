import time
from django.db import transaction
from design.models import Permit


def perform_save(self, serializer):
    permit_id = self.kwargs.get("pk")
    permit = Permit.objects.get(id=permit_id) if permit_id else None
    application = serializer.validated_data.get("application") or permit.application
    if not permit and application:
        serializer.validated_data["region_id"] = application.dzongkhag.region_id
        serializer.validated_data["dzongkhag_id"] = application.dzongkhag_id
        serializer.validated_data["thromde_id"] = application.thromde_id
        serializer.validated_data["gewog_id"] = application.gewog_id
        serializer.validated_data["village_id"] = application.village_id
        serializer.validated_data["nature"] = application.nature
    if not getattr(permit, "serial_no", None):
        serializer.validated_data["serial_no"] = f"CASD{int(time.time() * 1000)}"
    if not getattr(permit, "user_id", None):
        serializer.validated_data["user_id"] = self.request.user.id
