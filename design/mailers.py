from django.template.loader import render_to_string
from cas_api.celery import app
from design.models import Permit
import os
from cas_api.services.mailer_service import send_mail
from user.models import User
from django.shortcuts import get_object_or_404
from django_fsm_log.models import StateLog


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_requested(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/design-approval/{permit_id}"
        html_content = render_to_string(
            "mailers/designs/notify_requested.html",
            {"name": user.name, "url": url, "serial_no": permit.serial_no},
        )
        send_mail("New Task Assigned", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_requested")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_rejected(self, permit_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        logs = StateLog.objects.for_(permit)
        url = f"{os.getenv('HOST_URL', '')}/services/design-approval/{permit_id}"
        html_content = render_to_string(
            "mailers/designs/notify_rejected.html",
            {"name": permit.user.name, "reason": getattr(logs.last(), "description", ""), "url": url},
        )
        send_mail("Design permit is rejected", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_rejected")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_closed(self, permit_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/design-approval/{permit_id}"
        logs = StateLog.objects.for_(permit)
        html_content = render_to_string(
            "mailers/designs/notify_closed.html",
            {"name": permit.user.name, "reason": getattr(logs.last(), "description", ""), "url": url},
        )
        send_mail("Design permit is closed", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_closed")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_approved(self, permit_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/design-approval/{permit_id}"
        html_content = render_to_string("mailers/designs/notify_approved.html", {"name": permit.user.name, "url": url})
        send_mail("Design permit is approved", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_approved")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forwarded_to_dhs(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/design-approval/{permit_id}"
        html_content = render_to_string(
            "mailers/designs/notify_forwarded_to_dhs.html",
            {"name": user.name, "reference": permit.serial_no, "url": url},
        )
        send_mail("Design permit forwarded to Dzongkhag for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_forwarded_to_dhs")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forwarded_to_roid(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/design-approval/{permit_id}"
        html_content = render_to_string(
            "mailers/designs/notify_forwarded_to_roid.html",
            {"name": user.name, "reference": permit.serial_no, "url": url},
        )
        send_mail("Design permit forwarded to ROID for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_forwarded_to_roid")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_architect_assigned(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/design-approval/{permit_id}"
        html_content = render_to_string(
            "mailers/designs/notify_architect_assigned.html",
            {"name": user.name, "reference": permit.serial_no, "url": url},
        )
        send_mail("Design permit is assigned to you for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_architect_assigned")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_resubmitted(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/design-approval/{permit.id}"
        html_content = render_to_string(
            "mailers/designs/notify_resubmitted.html",
            {"name": user.name, "reference": permit.serial_no, "url": url},
        )
        send_mail("Design permit is resubmitted for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_resubmitted")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_change(self, permit_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/design-approval/{permit.id}"
        html_content = render_to_string(
            "mailers/designs/notify_change.html",
            {"name": permit.user.name, "reference": permit.serial_no, "url": url},
        )
        send_mail("Design permit is changed for approval", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_change")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_payment(self, permit_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/design-approval/{permit.id}"
        html_content = render_to_string(
            "mailers/designs/notify_payment.html",
            {"name": permit.user.name, "reference": permit.serial_no, "url": url},
        )
        send_mail("Design permit payment is pending", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_payment")
