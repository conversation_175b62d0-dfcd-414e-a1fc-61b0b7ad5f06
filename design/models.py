from django.db import models
from django_fsm import <PERSON><PERSON><PERSON>ield
from django.contrib.contenttypes.fields import GenericRelation
from common.enums import LandNature
from design.enums import PermitState
from design.transitions import Transition
from design.helpers.permit import Helper
from django.utils.translation import gettext_lazy as _


class Permit(models.Model, Helper, Transition):
    application = models.ForeignKey("planning.Application", on_delete=models.SET_NULL, null=True, related_name="design_permit")
    user = models.ForeignKey("user.User", on_delete=models.SET_NULL, null=True, related_name="design_permits")
    serial_no = models.CharField(max_length=100, null=True, blank=True)
    construction_id = models.CharField(max_length=100, null=True, blank=True, help_text="Unique identifier for tracking construction across all modules")
    no_of_floors = models.IntegerField(null=True, blank=True)
    fee = models.FloatField(null=True, blank=True)
    land_pooling = models.BooleanField(default=False)
    construction_type = models.ForeignKey("common.ConstructionType", on_delete=models.SET_NULL, null=True, related_name="design_permit")
    building_type = models.ForeignKey("common.BuildingType", on_delete=models.SET_NULL, null=True, related_name="design_permit")
    use = models.ForeignKey("common.Use", on_delete=models.SET_NULL, null=True, related_name="design_permit")
    proposal_type = models.ForeignKey("common.ProposalType", on_delete=models.SET_NULL, null=True, related_name="design_permit")
    state = FSMField(max_length=50, null=False, choices=PermitState.choices, default=PermitState.INITIATED)
    region = models.ForeignKey("address.Region", null=True, on_delete=models.SET_NULL, blank=True, related_name="design_permit")
    dzongkhag = models.ForeignKey("address.Dzongkhag", null=True, on_delete=models.SET_NULL, blank=True, related_name="design_permit")
    thromde = models.ForeignKey("address.Thromde", null=True, on_delete=models.SET_NULL, blank=True, related_name="design_permit")
    gewog = models.ForeignKey("address.Gewog", null=True, on_delete=models.SET_NULL, blank=True, related_name="design_permit")
    village = models.ForeignKey("address.Village", null=True, on_delete=models.SET_NULL, blank=True, related_name="design_permit")
    task_pools = GenericRelation("common.TaskPool", related_query_name="design_permit")
    design_teams = GenericRelation("common.DesignTeam", related_query_name="design_permit")
    payments = GenericRelation("payment.Payment", related_query_name="design_permit")
    slabs = GenericRelation("common.Slab", related_query_name="design_permit")
    nature = models.CharField(max_length=50, null=True, choices=LandNature.choices, default=LandNature.RURAL)
    sustainable = models.BooleanField(default=False)
    sustainable_remarks = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        verbose_name = _("permit")
        verbose_name_plural = _("permits")
        indexes = [
            models.Index(fields=["created_at"]),
            models.Index(fields=["state"]),
            models.Index(fields=["dzongkhag"]),
            models.Index(fields=["gewog"]),
            models.Index(fields=["construction_id"]),
        ]
