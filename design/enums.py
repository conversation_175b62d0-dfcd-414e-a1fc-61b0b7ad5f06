from django.db import models
from django.utils.translation import gettext_lazy as _


class PermitState(models.TextChoices):
    DRAFT = "draft", _("Draft")
    INITIATED = "initiated", _("Initiated")
    REQUESTED = "requested", _("Requested")
    ARCHITECT_ASSIGNED = "architect_assigned", _("Architect Assigned")
    FORWARDED_TO_ROID = "forwarded_to_roid", _("Forwarded to ROID")
    FORWARDED_TO_DHS = "forwarded_to_dhs", _("Forwarded to DHS")
    APPROVED = "approved", _("Approved")
    REJECTED = "rejected", _("Rejected")
    PENDING_CHANGE = "pending_change", _("Pending Change")
    RESUBMITTED = "resubmitted", _("Re-submitted")
    PENDING_PAYMENT = "pending_payment", _("Pending Payment")
    CLOSED = "closed", _("Closed")
    CANCELLED = "cancelled", _("Cancelled")
