import factory
from factory.django import DjangoModelFactory
from design.models import Permit
from user.factories import UserFactory
from address.factories import DzongkhagFactory, GewogFactory, ThromdeFactory, VillageFactory
from planning.factories import ApplicationFactory


class PermitFactory(DjangoModelFactory):
    class Meta:
        model = Permit

    user = factory.SubFactory(UserFactory)
    application = factory.SubFactory(ApplicationFactory)
    dzongkhag = factory.SubFactory(DzongkhagFactory)
    gewog = factory.SubFactory(GewogFactory)
    thromde = factory.SubFactory(ThromdeFactory)
    village = factory.SubFactory(VillageFactory)
    nature = "rural"
    serial_no = factory.Sequence(lambda n: f"DESIGN-{n}")
    data = {"remarks": "Test remarks"}
    checklist = {"items": []}
