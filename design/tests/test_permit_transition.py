import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase
from django.core.management import call_command
from user.factories import UserFactory, ProfileFactory
from address.factories import DzongkhagFactory, GewogFactory, ThromdeFactory, VillageFactory
from django.urls import path, include
from user.models import Role
from design.models import Permit
from design.factories import PermitFactory
from planning.factories import ApplicationFactory, ApplicantFactory


class PermitTransitionTest(APITestCase, URLPatternsTestCase):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("design.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.dzo = DzongkhagFactory(name="Mongar")
        self.gewog = GewogFactory(name="Mongar", dzongkhag=self.dzo)
        self.thromde = ThromdeFactory(name="Mongar", dzongkhag=self.dzo)
        self.village = VillageFactory(name="Mongar", gewog=self.gewog)

        # Create regular user
        self.user = UserFactory(username="user", roles=Role.objects.filter(name__in=["user"]).values_list("id", flat=True))
        ProfileFactory(user=self.user, dzongkhag=self.dzo)

        # Create dzongkhag architect
        self.dar = UserFactory(username="dar", roles=Role.objects.filter(name__in=["dar"]).values_list("id", flat=True))
        ProfileFactory(user=self.dar, dzongkhag=self.dzo)

        # Create dzongkhag chief engineer
        self.dce = UserFactory(username="dce", roles=Role.objects.filter(name__in=["dce"]).values_list("id", flat=True))
        ProfileFactory(user=self.dce, dzongkhag=self.dzo)

        # Create thromde architect
        self.tar = UserFactory(username="tar", roles=Role.objects.filter(name__in=["tar"]).values_list("id", flat=True))
        ProfileFactory(user=self.tar, dzongkhag=self.dzo, thromde=self.thromde)

        # Create thromde chief engineer
        self.tce = UserFactory(username="tce", roles=Role.objects.filter(name__in=["tce"]).values_list("id", flat=True))
        ProfileFactory(user=self.tce, dzongkhag=self.dzo, thromde=self.thromde)

        # Get tokens
        res = self.client.post(reverse("token_obtain_pair"), {"username": self.dar.username, "password": "Dcpl@123"})
        self.dar_token = json.loads(res.content)["data"]["access"]

        res = self.client.post(reverse("token_obtain_pair"), {"username": self.dce.username, "password": "Dcpl@123"})
        self.dce_token = json.loads(res.content)["data"]["access"]

        res = self.client.post(reverse("token_obtain_pair"), {"username": self.tar.username, "password": "Dcpl@123"})
        self.tar_token = json.loads(res.content)["data"]["access"]

        res = self.client.post(reverse("token_obtain_pair"), {"username": self.tce.username, "password": "Dcpl@123"})
        self.tce_token = json.loads(res.content)["data"]["access"]

        # Create application and permit for rural area
        self.rural_application = ApplicationFactory(
            user=self.user,
            dzongkhag=self.dzo,
            gewog=self.gewog,
            village=self.village,
            nature="rural"
        )
        self.rural_applicant = ApplicantFactory(application=self.rural_application)
        self.rural_permit = PermitFactory(
            user=self.user,
            application=self.rural_application,
            dzongkhag=self.dzo,
            gewog=self.gewog,
            village=self.village,
            nature="rural"
        )

        # Create application and permit for urban area
        self.urban_application = ApplicationFactory(
            user=self.user,
            dzongkhag=self.dzo,
            thromde=self.thromde,
            nature="urban"
        )
        self.urban_applicant = ApplicantFactory(application=self.urban_application)
        self.urban_permit = PermitFactory(
            user=self.user,
            application=self.urban_application,
            dzongkhag=self.dzo,
            thromde=self.thromde,
            nature="urban"
        )

    def test_invalid_transition(self):
        """Test that invalid transitions return appropriate error"""
        data = {"action": "invalid_action"}
        url = reverse("design-transitions", kwargs={"pk": self.rural_permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.dar_token}"
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertEqual(json.loads(response.content)["error"], "Invalid transition")

    def test_rural_permit_flow(self):
        """Test complete flow for rural permit"""
        url = reverse("design-transitions", kwargs={"pk": self.rural_permit.id})

        # Test architect review
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.dar_token}"
        data = {
            "action": "payment",
            "remarks": "Design reviewed"
        }
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "pending_payment")

        # Test chief engineer approval
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.dce_token}"
        data = {
            "action": "approve",
            "remarks": "Design approved"
        }
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "approved")

    def test_urban_permit_flow(self):
        """Test complete flow for urban permit"""
        url = reverse("design-transitions", kwargs={"pk": self.urban_permit.id})

        # Test architect review
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.tar_token}"
        data = {
            "action": "payment",
            "remarks": "Design reviewed"
        }
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "pending_payment")

        # Test chief engineer approval
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.tce_token}"
        data = {
            "action": "approve",
            "remarks": "Design approved"
        }
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "approved")

    def test_rejection_flow(self):
        """Test rejection flow"""
        url = reverse("design-transitions", kwargs={"pk": self.rural_permit.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.dar_token}"

        # Test rejection without remarks
        data = {"action": "reject"}
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test rejection with remarks
        data = {
            "action": "reject",
            "reject_remarks": "Design does not meet standards"
        }
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "rejected")
