from common.serializers import DesignTeamSearializer
from payment.serializers import PaymentSerializer
from .models import Permit
from drf_writable_nested.serializers import WritableNestedModelSerializer
from rest_framework import serializers
from .models import *
from common.models import ConstructionType, BuildingType, Use, ProposalType
from planning.models import Application
from common.serializers import SlabSerializer


class PermitSerializer(WritableNestedModelSerializer):
    design_teams = DesignTeamSearializer(many=True)
    payments = PaymentSerializer(many=True, read_only=True)
    construction_type_id = serializers.PrimaryKeyRelatedField(queryset=ConstructionType.objects.all(), source="construction_type", required=True)
    building_type_id = serializers.PrimaryKeyRelatedField(queryset=BuildingType.objects.all(), source="building_type", required=True)
    use_id = serializers.PrimaryKeyRelatedField(queryset=Use.objects.all(), source="use", required=True)
    proposal_type_id = serializers.PrimaryKeyRelatedField(queryset=ProposalType.objects.all(), source="proposal_type", required=True)
    construction_type_file = serializers.FileField(source="construction_type.file", read_only=True)
    building_type_file = serializers.FileField(source="building_type.file", read_only=True)
    use_file = serializers.FileField(source="use.file", read_only=True)
    proposal_type_file = serializers.FileField(source="proposal_type.file", read_only=True)
    application_id = serializers.PrimaryKeyRelatedField(queryset=Application.objects.all(), source="application", required=True)
    slabs = SlabSerializer(many=True, required=False, allow_empty=True, read_only=True)
    state_display = serializers.CharField(source="get_state_display", read_only=True)
    inquiry_id = serializers.IntegerField(read_only=True, source="application.inquiry_id")

    class Meta:
        model = Permit
        ref_name = "DesignPermit"
        fields = [
            "id",
            "user",
            "application_id",
            "inquiry_id",
            "region_id",
            "dzongkhag_id",
            "thromde_id",
            "gewog_id",
            "village_id",
            "serial_no",
            "construction_id",
            "construction_type_id",
            "building_type_id",
            "use_id",
            "proposal_type_id",
            "construction_type_file",
            "building_type_file",
            "use_file",
            "proposal_type_file",
            "state",
            "state_display",
            "design_teams",
            "payments",
            "slabs",
            "nature",
            "sustainable",
            "sustainable_remarks",
            "no_of_floors",
            "land_pooling",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["created_at", "updated_at", "id"]
