# Generated by Django 4.1.7 on 2025-03-27 22:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('file', '0002_attachment_name_attachment_size_attachment_type'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('address', '0010_region_dzongkhag_created_at_dzongkhag_updated_at_and_more'),
        ('planning', '0059_alter_application_state'),
    ]

    operations = [
        migrations.CreateModel(
            name='BuildingType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ConstructionType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Use',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProposalType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Permit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_no', models.CharField(max_length=100)),
                ('state', django_fsm.FSMField(choices=[('draft', 'Draft'), ('initiated', 'Initiated'), ('requested', 'Requested'), ('reviewed', 'Reviewed'), ('scheduled', 'Scheduled'), ('visited', 'Visited'), ('forwarded', 'Forwarded'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('pending_change', 'Pending Change'), ('resubmitted', 'Re-submitted'), ('pending_payment', 'Pending Payment'), ('closed', 'Closed')], default='initiated', max_length=20)),
                ('nature', models.CharField(choices=[('urban', 'Urban'), ('rural', 'Rural')], default='rural', max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('application', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permit', to='planning.application')),
                ('architectural_drawing', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permit', to='file.attachment')),
                ('building_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='design.buildingtype')),
                ('construction_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='design.constructiontype')),
                ('dzongkhag', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dzongkhag', to='address.dzongkhag')),
                ('gewog', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='gewog', to='address.gewog')),
                ('proposal_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='design.proposaltype')),
                ('thromde', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='thromde', to='address.thromde')),
                ('use', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='design.use')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('village', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='village', to='address.village')),
            ],
        ),
    ]
