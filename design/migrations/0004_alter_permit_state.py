# Generated by Django 4.1.7 on 2025-04-01 19:45

from django.db import migrations
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('design', '0003_alter_permit_state_alter_permit_user'),
    ]

    operations = [
        migrations.AlterField(
            model_name='permit',
            name='state',
            field=django_fsm.FSMField(choices=[('draft', 'Draft'), ('initiated', 'Initiated'), ('requested', 'Requested'), ('architect_assigned', 'Architect Assigned'), ('forwarded_to_roid', 'Forwarded to ROID'), ('forwarded_to_dhs', 'Forwarded to DHS'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('pending_change', 'Pending Change'), ('resubmitted', 'Re-submitted'), ('pending_payment', 'Pending Payment'), ('closed', 'Closed'), ('cancelled', 'Cancelled')], default='initiated', max_length=50),
        ),
    ]
