# Generated by Django 4.1.7 on 2025-03-29 18:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('design', '0002_remove_permit_architectural_drawing_permit_region_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='permit',
            name='state',
            field=django_fsm.FSMField(choices=[('draft', 'Draft'), ('initiated', 'Initiated'), ('requested', 'Requested'), ('forwarded_to_architect', 'Forwarded to Architect'), ('forwarded_to_roid', 'Forwarded to ROID'), ('forwarded_to_dhs', 'Forwarded to DHS'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('pending_change', 'Pending Change'), ('resubmitted', 'Re-submitted'), ('pending_payment', 'Pending Payment'), ('closed', 'Closed'), ('cancelled', 'Cancelled')], default='initiated', max_length=50),
        ),
        migrations.AlterField(
            model_name='permit',
            name='user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permits', to=settings.AUTH_USER_MODEL),
        ),
    ]
