# Generated by Django 4.1.7 on 2025-04-04 07:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0007_use_proposaltype_constructiontype_buildingtype'),
        ('design', '0004_alter_permit_state'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='constructiontype',
            name='user',
        ),
        migrations.RemoveField(
            model_name='proposaltype',
            name='user',
        ),
        migrations.RemoveField(
            model_name='use',
            name='user',
        ),
        migrations.AlterField(
            model_name='permit',
            name='building_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permit', to='common.buildingtype'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='construction_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permit', to='common.constructiontype'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='proposal_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permit', to='common.proposaltype'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='use',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permit', to='common.use'),
        ),
        migrations.DeleteModel(
            name='BuildingType',
        ),
        migrations.DeleteModel(
            name='ConstructionType',
        ),
        migrations.DeleteModel(
            name='ProposalType',
        ),
        migrations.DeleteModel(
            name='Use',
        ),
    ]
