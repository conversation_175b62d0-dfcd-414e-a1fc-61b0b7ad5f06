# Generated by Django 4.1.7 on 2025-04-06 10:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('design', '0005_remove_constructiontype_user_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='permit',
            options={'verbose_name': 'permit', 'verbose_name_plural': 'permits'},
        ),
        migrations.AddField(
            model_name='permit',
            name='fee',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='permit',
            name='land_pooling',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='permit',
            name='no_of_floors',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
