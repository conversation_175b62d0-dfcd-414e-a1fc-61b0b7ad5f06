# Generated by Django 4.1.7 on 2025-03-28 11:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('address', '0010_region_dzongkhag_created_at_dzongkhag_updated_at_and_more'),
        ('design', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='permit',
            name='architectural_drawing',
        ),
        migrations.AddField(
            model_name='permit',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permit', to='address.region'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='dzongkhag',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permit', to='address.dzongkhag'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='gewog',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permit', to='address.gewog'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='serial_no',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='permit',
            name='thromde',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permit', to='address.thromde'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='village',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='design_permit', to='address.village'),
        ),
    ]
