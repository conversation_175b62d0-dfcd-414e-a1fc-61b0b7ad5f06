import os
import sys
import django
import json
import requests
from datetime import datetime, timedelta
from unittest.mock import patch

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cas_api.settings")
django.setup()

# Mock Celery tasks
with patch("celery.app.task.Task.delay"):
    from django.utils import timezone
    from occupancy.models import Certificate
    from occupancy.enums import CertificateStatus, CertificateType
    from user.models import User
    from building.models import Permit


# Find a certificate eligible for renewal or create one
def find_or_create_eligible_certificate():
    # Try to find an existing eligible certificate
    today = timezone.now().date()
    eligible_certs = Certificate.objects.filter(state__in=[CertificateStatus.APPROVED, CertificateStatus.CLOSED], valid_until__lte=today + timedelta(days=90))

    if eligible_certs.exists():
        cert = eligible_certs.first()
        print(f"Found eligible certificate: ID={cert.id}, State={cert.state}, Valid until={cert.valid_until}")
        return cert

    # If no eligible certificate exists, create one

    # Find a user with the 'user' role
    user = User.objects.filter(roles__name="user").first()
    if not user:
        print("No user found with 'user' role.")
        return None

    # Let's find an existing permit instead of creating one
    permits = Permit.objects.all()
    if permits.exists():
        permit = permits.first()
        print(f"Found existing permit: ID={permit.id}, State={permit.state}")
    else:
        print("No permits found. Cannot create a certificate.")
        return None

    # Create a certificate that's eligible for renewal
    with patch("occupancy.signals.notify_requested.delay"):
        cert = Certificate.objects.create(permit=permit, user=user, state=CertificateStatus.INITIATED, certificate_type=CertificateType.NEW)  # Start with initiated state

    # Now manually update to approved state to bypass transitions
    Certificate.objects.filter(id=cert.id).update(
        state=CertificateStatus.APPROVED,
        valid_from=today - timedelta(days=365 * 5 - 60),  # Almost 5 years old
        valid_until=today + timedelta(days=60),  # Expires in 2 months
        certificate_number=f"CERT-{timezone.now().strftime('%Y%m%d%H%M%S')}",
        building_number=f"BLD-{timezone.now().strftime('%Y%m%d%H%M%S')}",
        building_qr_code="qr-code-data",
        certificate_date=today - timedelta(days=365 * 5 - 60),
    )

    # Refresh from database
    cert.refresh_from_db()

    print(f"Created eligible certificate: ID={cert.id}, State={cert.state}, Valid until={cert.valid_until}")
    return cert


# Test the renewal process
def test_renewal_process(certificate):
    # Mock Celery tasks
    with patch("occupancy.mailers.notify_renewal.delay"), patch("occupancy.mailers.notify_renewal_requested.delay"):
        # Get DRO user
        dro_user = User.objects.filter(roles__name="dadm").first()
        if not dro_user:
            print("No DRO user found.")
            return

        # Get token for DRO
        token_response = requests.post("http://localhost:8000/api/v1/token/", json={"username": dro_user.email, "password": "Dcpl@123"})

        if token_response.status_code != 200:
            print(f"Failed to get DRO token: {token_response.text}")
            return

        dro_token = token_response.json()["data"]["access"]

        # Step 1: Notify renewal
        notify_response = requests.post(
            f"http://localhost:8000/api/v1/occupancies/certificates/{certificate.id}/notify_renewal/",
            headers={"Authorization": f"Bearer {dro_token}"},
            json={"remarks": "Your certificate is expiring soon. Please renew."},
        )

        print(f"Notify renewal response: {notify_response.status_code}")
        print(json.dumps(notify_response.json(), indent=2))

        if notify_response.status_code != 200:
            print("Notify renewal failed.")
            return

        # Get token for certificate owner
        owner_response = requests.post("http://localhost:8000/api/v1/token/", json={"username": certificate.user.email, "password": "Dcpl@123"})

        if owner_response.status_code != 200:
            print(f"Failed to get owner token: {owner_response.text}")
            return

        owner_token = owner_response.json()["data"]["access"]

        # Step 2: Request renewal
        renewal_response = requests.post(
            "http://localhost:8000/api/v1/occupancies/certificate_renewals/",
            headers={"Authorization": f"Bearer {owner_token}"},
            json={"certificate_id": certificate.id, "remarks": "Requesting renewal of my occupancy certificate"},
        )

        print(f"Request renewal response: {renewal_response.status_code}")

        # Print response content regardless of status code
        print(f"Response content: {renewal_response.text}")

        # Try to parse JSON if possible
        try:
            if renewal_response.text:
                response_data = renewal_response.json()
                print(json.dumps(response_data, indent=2))
        except Exception as e:
            print(f"Error parsing JSON: {str(e)}")

        if renewal_response.status_code != 201:
            print("Request renewal failed.")
            return

        # Handle different response formats
        response_data = renewal_response.json()
        if "data" in response_data and "data" in response_data["data"]:
            # Double nested format
            renewal_id = response_data["data"]["data"]["id"]
        elif "data" in response_data:
            # Single nested format
            renewal_id = response_data["data"]["id"]
        else:
            # Direct format
            renewal_id = response_data["id"]

        print(f"Renewal certificate created with ID: {renewal_id}")

        # Verify the renewal certificate
        renewal_cert = Certificate.objects.get(id=renewal_id)
        print(f"Renewal certificate: ID={renewal_cert.id}, State={renewal_cert.state}, Type={renewal_cert.certificate_type}")
        print(f"Parent certificate ID: {renewal_cert.parent_certificate_id}")


if __name__ == "__main__":
    certificate = find_or_create_eligible_certificate()
    if certificate:
        test_renewal_process(certificate)
