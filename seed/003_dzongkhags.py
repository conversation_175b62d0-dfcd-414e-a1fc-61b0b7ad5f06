import os
import sys
import requests
import json
from pathlib import Path
import django
from django.core.management.base import BaseCommand


BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(BASE_DIR))

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cas_api.settings")
django.setup()

from address.models import Dzongkhag
from cas_api.services.govtech_service import fetch_access_token
from cas_api.utils.external_service import SecureExternalService


url = f"{os.getenv('DATAHUB_URL')}/dcrc_parmanentaddressdetailsapi/1.0.0/dzongkhagdetails"

try:
    service = SecureExternalService()
    response = service.request("GET", url, headers={"Authorization": fetch_access_token()})
    response.raise_for_status()

    data = response.json()

    for item in data["dzongkhagdetails"]["dzongkhagdetail"]:
        try:
            Dzongkhag.objects.get_or_create(id=item["dzongkhagId"], defaults={"name": item["dzongkhagName"]})
        except:
            continue
    sys.stdout.write("Successfully populated dzongkhag data.")
except Exception as e:
    sys.stderr.write(f"Error populating data: {e}")
