import os, sys, requests, json, django
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(BASE_DIR))

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cas_api.settings")
django.setup()

from address.models import *

thromdes = [
    {
        "id": 1,
        "name": "Thimphu Thromde",
        "dzongkhag_id": 14,
        "tenant_code": "1001",
        "counter_code": "MMT3692",
        "service_code": "100004",
        "username": "castt",
        "password": "CAStt@2024",
    },
    {
        "id": 2,
        "name": "Phuntsholing Thromde",
        "dzongkhag_id": 2,
        "tenant_code": "1002",
        "counter_code": "PPT5271",
        "service_code": "100007",
        "username": "caspt",
        "password": "CASpt@2024",
    },
    {
        "id": 3,
        "name": "<PERSON><PERSON>ph<PERSON> Throm",
        "dzongkhag_id": 13,
        "tenant_code": "1005",
        "counter_code": "GGT3513",
        "service_code": "100009",
        "username": "casgt",
        "password": "CASgt@2024",
    },
    {
        "id": 4,
        "name": "Samdrupjongkhar Thromde",
        "dzongkhag_id": 11,
        "tenant_code": "1006",
        "counter_code": "SSJ7921",
        "service_code": "100010",
        "username": "cassjt",
        "password": "CASsjt@2024",
    },
]

try:
    for thromde in thromdes:
        try:
            uid = thromde.pop("id")
            Thromde.objects.get_or_create(id=uid, defaults=thromde)
        except:
            continue
    sys.stdout.write("Successfully populated thromde data.")
except requests.exceptions.RequestException as e:
    sys.stderr.write(f"Error fetching data: {e}")
except (KeyError, json.JSONDecodeError) as e:
    sys.stderr.write(f"Error parsing data: {e}")
except Exception as e:
    sys.stderr.write(f"Error populating data: {e}")
