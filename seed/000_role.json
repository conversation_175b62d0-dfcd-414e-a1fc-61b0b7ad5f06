[{"model": "user.role", "pk": 1, "fields": {"name": "admin", "description": "Administrator", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 2, "fields": {"name": "dhsadm", "description": "DHS Admin", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 3, "fields": {"name": "dhsce", "description": "DHS Chief Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 4, "fields": {"name": "dhsar", "description": "DHS Architect", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 5, "fields": {"name": "dhsee", "description": "DHS Electrical Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 6, "fields": {"name": "dhsse", "description": "DHS Structural Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 7, "fields": {"name": "dhswse", "description": "DHS Water & Sanitation Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 8, "fields": {"name": "roidadm", "description": "ROID Admin", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 9, "fields": {"name": "roidce", "description": "ROID Chief Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 10, "fields": {"name": "roidar", "description": "ROID Architect", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 11, "fields": {"name": "roidee", "description": "ROID Electrical Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 12, "fields": {"name": "roidse", "description": "ROID Structural Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 13, "fields": {"name": "r<PERSON><PERSON><PERSON>", "description": "ROID Water & Sanitation Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 14, "fields": {"name": "dadm", "description": "<PERSON><PERSON><PERSON><PERSON>", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 15, "fields": {"name": "dce", "description": "Chief <PERSON><PERSON><PERSON><PERSON> Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 16, "fields": {"name": "dar", "description": "Dzongkhag Architect", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 17, "fields": {"name": "dee", "description": "Dzongkhag Electrical Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 18, "fields": {"name": "dse", "description": "Dzongkhag Structural Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 19, "fields": {"name": "dwse", "description": "Dzongkhag Water & Sanitation Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 21, "fields": {"name": "dcom", "description": "Dzongkhag Committee", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 22, "fields": {"name": "tadm", "description": "<PERSON><PERSON><PERSON><PERSON>", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 23, "fields": {"name": "tce", "description": "<PERSON><PERSON><PERSON>de Chief Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 24, "fields": {"name": "tar", "description": "Thromde Architect", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 25, "fields": {"name": "tee", "description": "Thromde Electrical Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 26, "fields": {"name": "tse", "description": "Thromde Structural Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 27, "fields": {"name": "twse", "description": "Thromde Water & Sanitation Engineer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 28, "fields": {"name": "cup", "description": "Chief Urban Planner", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 29, "fields": {"name": "up", "description": "Urban Planner", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 30, "fields": {"name": "hoi", "description": "Head of Inspection", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 31, "fields": {"name": "dbi", "description": "Dzongkhag Building Inspector", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 32, "fields": {"name": "bpc", "description": "Bhutan Power Cooperation", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 33, "fields": {"name": "tcb", "description": "Tourism Counsil of Bhutan", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 34, "fields": {"name": "nlcs", "description": "National Land Commission Secretariat", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 35, "fields": {"name": "user", "description": "User", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 36, "fields": {"name": "birms", "description": "BIRMS", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 37, "fields": {"name": "dro", "description": "Development Regulatory Officer", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 38, "fields": {"name": "dzongda", "description": "Dzongda", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 39, "fields": {"name": "thrompon", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 40, "fields": {"name": "gup", "description": "<PERSON><PERSON>", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 41, "fields": {"name": "tbi", "description": "Thromde Building Inspector", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 42, "fields": {"name": "rbp", "description": "Royal Bhutan Police", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 43, "fields": {"name": "rba", "description": "Royal Bhutan Army", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}, {"model": "user.role", "pk": 44, "fields": {"name": "rgob", "description": "Royal Govenment of Bhutan", "created_at": "2019-01-01 06:00:00+06", "updated_at": "2019-01-01 06:00:00+06"}}]