import os, sys, requests, json, django
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(BASE_DIR))

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cas_api.settings")
django.setup()

from common.models import *

services = [
    # Dzongkhags BIRMS service
    {"id": 1, "code": "100311", "service_type": "pp", "name": "Planning Permit", "description": "Planning Permit"},
    {"id": 2, "code": "100312", "service_type": "dp", "name": "Design Permit", "description": "Design Permit"},
    {"id": 3, "code": "100313", "service_type": "bp", "name": "Building Permit", "description": "Building Permit"},
    {"id": 4, "code": "100314", "service_type": "oc", "name": "Occupancy certificate", "description": "Occupancy certificate"},
    {"id": 5, "code": "100317", "service_type": "mob", "name": "Modification of building", "description": "Modification of building"},
    {"id": 6, "code": "100318", "service_type": "tc", "name": "Technical clearance", "description": "Technical clearance"},
    {"id": 7, "code": "100319", "service_type": "ocr", "name": "Occupancy certificate renewal", "description": "Occupancy certificate renewal"},
    {"id": 8, "code": "100320", "service_type": "ip", "name": "Building inspection penalty", "description": "Building inspection penalty"},
    # Thimphu Thromde
    {"id": 9, "code": "100037", "service_type": "1001_pp", "name": "Planning Permit", "description": "Planning Permit"},
    {"id": 10, "code": "100038", "service_type": "1001_dp", "name": "Design Permit", "description": "Design Permit"},
    {"id": 11, "code": "100039", "service_type": "1001_bp", "name": "Building Permit", "description": "Building Permit"},
    {"id": 12, "code": "100040", "service_type": "1001_oc", "name": "Occupancy certificate", "description": "Occupancy certificate"},
    {"id": 13, "code": "100041", "service_type": "1001_mob", "name": "Modification of building", "description": "Modification of building"},
    {"id": 14, "code": "100042", "service_type": "1001_tc", "name": "Technical clearance", "description": "Technical clearance"},
    {"id": 15, "code": "100042", "service_type": "1001_ocr", "name": "Occupancy certificate renewal", "description": "Occupancy certificate renewal"},
    {"id": 16, "code": "100042", "service_type": "1001_ip", "name": "Building inspection penalty", "description": "Building inspection penalty"},
    # Phuntsholing Thromde
    {"id": 17, "code": "100043", "service_type": "1002_pp", "name": "Planning Permit", "description": "Planning Permit"},
    {"id": 18, "code": "100044", "service_type": "1002_dp", "name": "Design Permit", "description": "Design Permit"},
    {"id": 19, "code": "100045", "service_type": "1002_bp", "name": "Building Permit", "description": "Building Permit"},
    {"id": 20, "code": "100046", "service_type": "1002_oc", "name": "Occupancy certificate", "description": "Occupancy certificate"},
    {"id": 21, "code": "100047", "service_type": "1002_mob", "name": "Modification of building", "description": "Modification of building"},
    {"id": 22, "code": "100048", "service_type": "1002_tc", "name": "Technical clearance", "description": "Technical clearance"},
    {"id": 23, "code": "100048", "service_type": "1002_ocr", "name": "Occupancy certificate renewal", "description": "Occupancy certificate renewal"},
    {"id": 24, "code": "100048", "service_type": "1002_ip", "name": "Building inspection penalty", "description": "Building inspection penalty"},
    # Gelephu Thromde
    {"id": 25, "code": "100049", "service_type": "1005_pp", "name": "Planning Permit", "description": "Planning Permit"},
    {"id": 26, "code": "100050", "service_type": "1005_dp", "name": "Design Permit", "description": "Design Permit"},
    {"id": 27, "code": "100051", "service_type": "1005_bp", "name": "Building Permit", "description": "Building Permit"},
    {"id": 28, "code": "100052", "service_type": "1005_oc", "name": "Occupancy certificate", "description": "Occupancy certificate"},
    {"id": 29, "code": "100053", "service_type": "1005_mob", "name": "Modification of building", "description": "Modification of building"},
    {"id": 30, "code": "100054", "service_type": "1005_tc", "name": "Technical clearance", "description": "Technical clearance"},
    {"id": 31, "code": "100054", "service_type": "1005_ocr", "name": "Occupancy certificate renewal", "description": "Occupancy certificate renewal"},
    {"id": 32, "code": "100054", "service_type": "1005_ip", "name": "Building inspection penalty", "description": "Building inspection penalty"},
    # Samdrup Jongkhar Thromde
    {"id": 33, "code": "100055", "service_type": "pp", "name": "Planning Permit", "description": "Planning Permit"},
    {"id": 34, "code": "100056", "service_type": "dp", "name": "Design Permit", "description": "Design Permit"},
    {"id": 35, "code": "100057", "service_type": "bp", "name": "Building Permit", "description": "Building Permit"},
    {"id": 36, "code": "100058", "service_type": "oc", "name": "Occupancy certificate", "description": "Occupancy certificate"},
    {"id": 37, "code": "100059", "service_type": "mob", "name": "Modification of building", "description": "Modification of building"},
    {"id": 38, "code": "100060", "service_type": "tc", "name": "Technical clearance", "description": "Technical clearance"},
    {"id": 39, "code": "100060", "service_type": "ocr", "name": "Occupancy certificate renewal", "description": "Occupancy certificate renewal"},
    {"id": 40, "code": "100060", "service_type": "ip", "name": "Building inspection penalty", "description": "Building inspection penalty"},
]

try:
    for service in services:
        try:
            uid = service.pop("id")
            Service.objects.update_or_create(id=uid, defaults=service)
        except:
            continue
    sys.stdout.write("Successfully populated service data.")
except Exception as e:
    sys.stderr.write(f"Error populating data: {e}")
