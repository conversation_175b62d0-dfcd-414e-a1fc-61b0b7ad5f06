import os
import sys
import requests
import json
from pathlib import Path
import django
from django.core.management.base import BaseCommand

BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(BASE_DIR))

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cas_api.settings")
django.setup()

from address.models import Dzongkhag, Gewog
from cas_api.services.govtech_service import fetch_access_token
from cas_api.utils.external_service import SecureExternalService

url = f"{os.getenv('DATAHUB_URL')}/dcrc_parmanentaddressdetailsapi/1.0.0//gewogdetails"

try:
    service = SecureExternalService()
    for dzo in Dzongkhag.objects.all():
        response = service.request("GET", f"{url}/{dzo.id}", headers={"Authorization": fetch_access_token()})
        data = response.json()
        for item in data["gewogdetails"]["gewogdetail"]:
            if item["gewogName"] == "Unknown":
                continue
            try:
                Gewog.objects.get_or_create(
                    id=item["gewogId"],
                    dzongkhag_id=dzo.id,
                    defaults={"name": item["gewogName"]},
                )
            except:
                continue
    sys.stdout.write("Successfully populated gewog data.")
except Exception as e:
    sys.stderr.write(f"Error populating data: {e}")
