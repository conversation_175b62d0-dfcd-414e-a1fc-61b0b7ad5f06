import os, sys, requests, json, django
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(BASE_DIR))

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cas_api.settings")
django.setup()

from user.models import *

users = [
    {"id": 1, "email": "<EMAIL>", "password": "Dcpl@123", "username": "admin", "active": True, "first_name": "Super Admin", "role_ids": [1]},
    {"id": 2, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dhsadm", "active": True, "first_name": "DHS Admin", "role_ids": [2]},
    {"id": 3, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dhsce", "active": True, "first_name": "DHS Chief Engineer", "role_ids": [3]},
    {"id": 4, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dhsar", "active": True, "first_name": "DHS Architect", "role_ids": [4]},
    {"id": 5, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dhsee", "active": True, "first_name": "DHS Electrical Engineer", "role_ids": [5]},
    {"id": 6, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dhsse", "active": True, "first_name": "DHS Structural Engineer", "role_ids": [6]},
    {"id": 7, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dhswse", "active": True, "first_name": "DHS Water & Sanitation Engineer", "role_ids": [7]},
    {"id": 8, "email": "<EMAIL>", "password": "Dcpl@123", "username": "roidadm", "active": True, "first_name": "ROID Admin", "role_ids": [8]},
    {"id": 9, "email": "<EMAIL>", "password": "Dcpl@123", "username": "roidce", "active": True, "first_name": "ROID Chief Engineer", "role_ids": [9]},
    {"id": 10, "email": "<EMAIL>", "password": "Dcpl@123", "username": "roidar", "active": True, "first_name": "ROID Architect", "role_ids": [10]},
    {"id": 11, "email": "<EMAIL>", "password": "Dcpl@123", "username": "roidee", "active": True, "first_name": "ROID Electrical Engineer", "role_ids": [11]},
    {"id": 12, "email": "<EMAIL>", "password": "Dcpl@123", "username": "roidse", "active": True, "first_name": "ROID Structural Engineer", "role_ids": [12]},
    {"id": 13, "email": "<EMAIL>", "password": "Dcpl@123", "username": "roidwse", "active": True, "first_name": "ROID Water & Sanitation Engineer", "role_ids": [13]},
    {"id": 14, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dadm", "active": True, "first_name": "Dzongkhag Admin", "role_ids": [14]},
    {"id": 15, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dce", "active": True, "first_name": "Dzongkhag Chief Engineer", "role_ids": [15]},
    {"id": 16, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dar", "active": True, "first_name": "Dzongkhag Architect", "role_ids": [16]},
    {"id": 17, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dee", "active": True, "first_name": "Dzongkhag Electrical Engineer", "role_ids": [17]},
    {"id": 18, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dse", "active": True, "first_name": "Dzongkhag Structural Engineer", "role_ids": [18]},
    {"id": 19, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dwse", "active": True, "first_name": "Dzongkhag Water & Sanitation Engineer", "role_ids": [19]},
    {"id": 21, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dcom", "active": True, "first_name": "Dzongkhag Committee", "role_ids": [21]},
    {"id": 22, "email": "<EMAIL>", "password": "Dcpl@123", "username": "tadm", "active": True, "first_name": "Thromde Admin", "role_ids": [22]},
    {"id": 23, "email": "<EMAIL>", "password": "Dcpl@123", "username": "tce", "active": True, "first_name": "Thromde Chief Engineer", "role_ids": [23]},
    {"id": 24, "email": "<EMAIL>", "password": "Dcpl@123", "username": "tar", "active": True, "first_name": "Thromde Architect", "role_ids": [24]},
    {"id": 25, "email": "<EMAIL>", "password": "Dcpl@123", "username": "tee", "active": True, "first_name": "Thromde Electrical Engineer", "role_ids": [25]},
    {"id": 26, "email": "<EMAIL>", "password": "Dcpl@123", "username": "tse", "active": True, "first_name": "Thromde Structural Engineer", "role_ids": [26]},
    {"id": 27, "email": "<EMAIL>", "password": "Dcpl@123", "username": "twse", "active": True, "first_name": "Thromde Water & Sanitation Engineer", "role_ids": [27]},
    {"id": 28, "email": "<EMAIL>", "password": "Dcpl@123", "username": "cup", "active": True, "first_name": "Chief Urban Planner", "role_ids": [28]},
    {"id": 29, "email": "<EMAIL>", "password": "Dcpl@123", "username": "up", "active": True, "first_name": "Urban Planner", "role_ids": [29]},
    {"id": 30, "email": "<EMAIL>", "password": "Dcpl@123", "username": "binsp", "active": True, "first_name": "Building Inspector", "role_ids": [30]},
    {"id": 31, "email": "<EMAIL>", "password": "Dcpl@123", "username": "gadm", "active": True, "first_name": "Gewog Admin", "role_ids": [31]},
    {"id": 32, "email": "<EMAIL>", "password": "Dcpl@123", "username": "bpc", "active": True, "first_name": "Bhutan Power Corporation", "role_ids": [32]},
    {"id": 33, "email": "<EMAIL>", "password": "Dcpl@123", "username": "tcb", "active": True, "first_name": "Tourism Counsil of Bhutan", "role_ids": [33]},
    {"id": 34, "email": "<EMAIL>", "password": "Dcpl@123", "username": "nlcs", "active": True, "first_name": "National Land Commission Secretariat", "role_ids": [34]},
    {"id": 35, "email": "<EMAIL>", "password": "Dcpl@123", "username": "user", "active": True, "first_name": "User", "role_ids": [35]},
    {"id": 36, "email": "<EMAIL>", "password": "Dcpl@123", "username": "birms", "active": True, "first_name": "BIRMS", "role_ids": [36]},
    {"id": 37, "email": "<EMAIL>", "password": "Dcpl@123", "username": "dro", "active": True, "first_name": "Dzongkhag Regional Office", "role_ids": [37]},
]

try:
    for user in users:
        try:
            role_ids = user.pop("role_ids")
            uid = user.pop("id")
            password = user.pop("password")
            user_obj, created = User.objects.update_or_create(id=uid, defaults=user)
            user_obj.roles.set(role_ids)
            user_obj.set_password(password)
            user_obj.current_role_id = role_ids[0]
            user_obj.save()
            Profile.objects.get_or_create(user_id=user_obj.id, defaults={"dzongkhag_id": 7, "gewog_id": 709})
        except:
            continue
    sys.stdout.write("Successfully populated user data.")
except requests.exceptions.RequestException as e:
    sys.stderr.write(f"Error fetching data: {e}")
except (KeyError, json.JSONDecodeError) as e:
    sys.stderr.write(f"Error parsing data: {e}")
except Exception as e:
    sys.stderr.write(f"Error populating data: {e}")
