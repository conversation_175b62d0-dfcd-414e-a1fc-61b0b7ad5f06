import os
import sys
import requests
import json
from pathlib import Path
import django
from django.core.management.base import BaseCommand


BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(BASE_DIR))

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cas_api.settings")
django.setup()

from address.models import Village, Gewog
from cas_api.services.govtech_service import fetch_access_token
from cas_api.utils.external_service import SecureExternalService


url = f"{os.getenv('DATAHUB_URL')}//dcrc_parmanentaddressdetailsapi/1.0.0/villagedetails"

try:
    service = SecureExternalService()
    for gewog in Gewog.objects.all():
        response = service.request("GET", f"{url}/{gewog.id}", headers={"Authorization": fetch_access_token()})
        response.raise_for_status()

        data = response.json()
        for item in data["villagedetails"]["villagedetail"]:
            if item["villageName"] == "Unknown":
                continue
            try:
                Village.objects.get_or_create(
                    id=item["villageId"],
                    gewog_id=gewog.id,
                    defaults={"name": item["villageName"]},
                )
            except:
                continue
    sys.stdout.write("Successfully populated village data.")
except Exception as e:
    sys.stderr.write(f"Error populating data: {e}")
