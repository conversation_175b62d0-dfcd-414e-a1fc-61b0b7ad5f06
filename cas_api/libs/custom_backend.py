from django.db.models import Q
from user.models import User
from django.contrib.auth.backends import ModelBackend
from rest_framework.exceptions import ValidationError


class AuthBackend(ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        if username is None:
            username = kwargs.get(User.USERNAME_FIELD)
        if username is None or password is None:
            return
        try:
            user = User.objects.get(Q(username=username) | Q(email=username) | Q(cid=username))
        except User.DoesNotExist:
            User().set_password(password)
        else:
            if user.check_password(password) and self.user_can_authenticate(user):
                return user
            else:
                if not self.user_can_authenticate(user):
                    raise ValidationError({"error": "Your account is not activated yet."})
                else:
                    raise ValidationError({"error": "Invalid username or password combination."})
