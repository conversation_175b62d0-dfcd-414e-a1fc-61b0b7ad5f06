from rest_framework import pagination
from rest_framework.response import Response


class Pagination(pagination.PageNumberPagination):
    page_size_query_param = "per_page"

    def get_paginated_response(self, data, **kwargs):
        meta = kwargs.get("meta", {})
        return Response(
            {
                "data": data,
                "meta": {
                    **meta,
                    "next_page": self.get_next_link(),
                    "previous": self.get_previous_link(),
                    "total": self.page.paginator.count,
                },
            }
        )
