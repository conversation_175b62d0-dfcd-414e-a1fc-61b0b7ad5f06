from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.utils import json
import re


class JSONResponse<PERSON>enderer(JSONRenderer):
    charset = "utf-8"

    def render(self, data, accepted_media_type=None, renderer_context=None):
        if renderer_context.get("response").status_code not in [200, 201, 204]:
            full_error = self.get_full_validation_error(data)
            strip_list = [item for item in full_error if item]
            errors = list(set(strip_list))
            return super().render({"error": ", ".join(errors)}, accepted_media_type, renderer_context)
        else:
            if data is None:
                return json.dumps({"message": "Deleted successfully"})
            if "data" in data and "meta" in data:
                response_dict = {"data": data["data"], "meta": data["meta"]}
            else:
                response_dict = {"data": data}
            return json.dumps(response_dict)

    def get_full_validation_error(self, errors, parent_key=""):
        full_error = []
        for key, value in errors.items():
            if isinstance(value, list):
                for error in value:
                    if isinstance(error, dict):
                        nested_error = self.get_full_validation_error(error, key + " ")
                        full_error.append(", ".join(nested_error))
                    else:
                        msg = re.sub(r"_", " ", parent_key).title() + re.sub(r"_", " ", key).title()
                        full_error.append(str(error).replace("This field", msg).replace(".", ""))
            elif isinstance(value, dict):
                nested_error = self.get_full_validation_error(value, key + " ")
                full_error.append(", ".join(nested_error))
            else:
                full_error.append(str(value))
        return full_error
