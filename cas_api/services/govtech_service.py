import os
from rest_framework import exceptions
from cas_api.utils.external_service import SecureExternalService
from django.core.cache import cache
from rest_framework.exceptions import AuthenticationFailed
from cas_api.utils.external_service import SecureExternalService


def land_by_plot_no(plot_no):
    return call(f"{os.getenv('DATAHUB_URL')}{os.getenv('LAND_BY_PLOT_NO_PATH')}{plot_no}")


def land_by_cid(cid):
    return call(f"{os.getenv('DATAHUB_URL')}{os.getenv('LAND_BY_CID_PATH')}{cid}")


def bcta_by_cid(cid):
    return call(f"{os.getenv('DATAHUB_URL')}{os.getenv('BCTA_BY_CID_PATH')}{cid}")


def bcta_by_arn(arn):
    return call(f"{os.getenv('DATAHUB_URL')}{os.getenv('BCTA_BY_ARN_PATH')}{arn}")


def fetch_citizen_detail(cid):
    return call(f"{os.getenv('DATAHUB_URL')}{os.getenv('CITIZEN_DETAIL_PATH')}{cid}")


def fetch_plot_coordinates(plot_no):
    import requests

    try:
        response = requests.get(
            f"{os.getenv('NLCS_BASE_URL')}{os.getenv('NLCS_COORDINATES_PATH')}?where=PlotID='{plot_no}'&outFields=*&returnGeometry=true&f=geojson", verify=False
        )
        response.raise_for_status()
        json = response.json()
        json["status"] = response.status_code
        return json
    except Exception as e:
        raise exceptions.ValidationError({"error": f"Something went wrong. {str(e)}"})


def call(url, method="GET", **kwargs):
    service = SecureExternalService()
    try:
        response = service.request(method, url, headers={"Authorization": fetch_access_token()}, **kwargs)
        json = response.json()
        json["status"] = response.status_code
        return json
    except Exception as e:
        raise exceptions.ValidationError({"error": f"Something went wrong. {str(e)}"})


def fetch_access_token():
    # Check if the access token is already cached and not expired
    access_token = cache.get("access_token")
    if access_token:
        return access_token

    service = SecureExternalService()
    try:
        response = service.request(
            "POST",
            f"{os.getenv('SSO_TOKEN_ENDPOINT')}",
            data={"client_id": os.getenv("SSO_CLIENT_ID"), "client_secret": os.getenv("SSO_CLIENT_SECRET"), "grant_type": "client_credentials"},
        )
        if response.status_code == 200:
            access_token = response.json().get("access_token")
            cache.set("access_token", f"Bearer {access_token}", timeout=1800)
            return f"Bearer {access_token}"
        else:
            raise AuthenticationFailed(f"Failed to fetch access token from the SSO server. Status code: {response.status_code}")
    except Exception as e:
        raise AuthenticationFailed(f"Something went wrong: {str(e)}")
