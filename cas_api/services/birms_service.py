from datetime import datetime
from rest_framework.exceptions import ValidationError, AuthenticationFailed
from django.core.cache import cache
import requests, os
from payment.models import Payment
from cas_api.celery import app
from django.shortcuts import get_object_or_404
from celery.exceptions import MaxRetriesExceededError
from cas_api.utils.external_service import SecureExternalService
import logging

logger = logging.getLogger(__name__)


def fetch_birms_access_token():
    access_token = cache.get("birms_access_token")
    if access_token:
        return access_token

    service = SecureExternalService()
    try:
        response = service.request(
            "POST", f"{os.getenv('BIRMS_BASE_URL')}{os.getenv('BIRMS_LOGIN_PATH')}", json={"username": os.getenv("BIRMS_USERNAME"), "password": os.getenv("BIRMS_PASSOWRD")}
        )
        if not response.status_code == 200 or not response.json().get("status") == "OK":
            raise AuthenticationFailed(f"Authentication failed! {response.json().get('message')}")
        access_token = response.json().get("content", {}).get("tokenDto", {}).get("accessToken")
        auth_token = f"Bearer {access_token}"
        cache.set("birms_access_token", auth_token, timeout=1800)
        return auth_token
    except Exception as e:
        raise ValidationError({"error": f"Something went wrong: {str(e)}"})


@app.task(bind=True, max_retries=5, default_retry_delay=60)
def generate_payment_advice(self, payment_id):
    payment = get_object_or_404(Payment, pk=payment_id)
    try:
        response = requests.post(
            f"{os.getenv('BIRMS_BASE_URL')}{os.getenv('BIRMS_PAYMENT_ADVICE_PATH')}",
            headers={"Authorization": fetch_birms_access_token()},
            json=payment_advice_payload(payment),
        )
        if not response.status_code == 200:
            logger.error("Payment advice generation failed! {response.text}")
            raise ValidationError({"error": f"Payment advice generation failed! {response.text}"})
        res = response.json()
        if not res.get("statusCode") == 201:
            msg = f"Payment advice generation failed! {res.get('message', None)}"
            logger.error(msg)
            raise ValidationError({"error": msg})
        update_payment(payment, res)
    except ValidationError as e:
        logger.error(f"Validation error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except MaxRetriesExceededError:
            logger.error("Max retries exceeded for task.")


def payment_advice_payload(payment):
    return {
        "platform": "Construction Approval System",
        "platformCode": "CAS",
        "refNo": payment.reference_no,
        "taxPayerNo": payment.payer_cid,
        "taxPayerDocumentNo": payment.payer_cid,
        "paymentRequestDate": datetime.now().strftime("%Y-%m-%d"),
        "agencyCode": payment.agency_code,
        "payerEmail": payment.payer_email,
        "mobileNo": payment.payer_phone,
        "totalPayableAmount": str(payment.amount) or 0,
        "paymentDueDate": None,
        "taxPayerName": payment.payer_name,
        "paymentLists": [
            {
                "serviceCode": payment.service_code,
                "description": payment.service_name,
                "payableAmount": str(payment.amount) or 0,
            }
        ],
    }


def update_payment(payment, res):
    payment.transaction_no = res.get("content", {}).get("paymentAdviceNo", None)
    payment.payment_date = res.get("content", {}).get("paymentAdviceDate", None)
    payment.redirect_url = res.get("content", {}).get("redirectUrl", None)
    payment.save()


def generate_receipt(receipt_id):
    service = SecureExternalService()
    try:
        response = service.request("GET", f"{os.getenv('BIRMS_BASE_URL')}{os.getenv('BIRMS_RECEIPT_PATH')}/{receipt_id}", headers={"Authorization": fetch_birms_access_token()})
        if not response.status_code == 200:
            raise ValidationError({"error": "Failed to generate receipt in BIRMS!"})
        return response.json()
    except Exception as e:
        raise ValidationError({"error": f"Something went wrong: {str(e)}"})


def cancel_payment(payment, data, user):
    try:
        response = requests.post(
            f"{os.getenv('BIRMS_BASE_URL')}{os.getenv('BIRMS_CANCEL_PAYMENT_PATH')}",
            headers={"Authorization": fetch_birms_access_token()},
            json={
                "paymentAdviceNumber": payment.transaction_no,
                "reason": data.get("remarks", "Payment cancelled"),
                "cancelledBy": user.name,
            },
        )
        if not response.status_code == 200:
            raise ValidationError({"error": f"Failed to cancel the payment."})
        return response.json()
    except Exception as e:
        raise ValidationError({"error": f"Something went wrong: {str(e)}"})
