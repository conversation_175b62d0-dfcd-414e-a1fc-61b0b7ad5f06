import os, datetime
from cas_api.services.govtech_service import fetch_citizen_detail
from common.helpers import fetch_address
from user.models import Profile, Role, User
from rest_framework.exceptions import ValidationError
from address.models import *
from cas_api.utils.external_service import SecureExternalService
from django.core.cache import cache
from rest_framework.exceptions import AuthenticationFailed
from cas_api.utils.external_service import SecureExternalService


def create_proof_requests(data):
    return call(f"{os.getenv('NDI_PROOF_REQUEST_URL')}", "POST", json=data)


def fetch_proof_request(thread_id):
    return call(f"{os.getenv('NDI_PROOF_REQUEST_URL')}?threadId={thread_id}")


def call(url, method="GET", **kwargs):
    service = SecureExternalService()
    try:
        response = service.request(method, url, headers={"Authorization": fetch_ndi_access_token()}, **kwargs)
        json = response.json()
        json["status"] = response.status_code
        return json
    except Exception as e:
        raise ValidationError({"error": f"NDI service error: {str(e)}"})


def process_ndi_info(data):
    try:
        json = fetch_proof_request(data.get("thread_id"))
        if json.get("data").get("status") == "done":
            user = User.objects.filter(cid=data["cid"]).first()
            if not user:
                try:
                    res = fetch_citizen_detail(data["cid"])
                    claims = res["citizenDetailsResponse"].get("citizenDetail", [{"cid": data["cid"]}])[0]
                except Exception as e:
                    raise ValidationError({"error": f"User verification failed, due to {str(e)}"})
                user = create_user(claims)
            return user
        else:
            raise ValidationError({"error": "There was error validating Proof Request. Please try again by refreshing the page."})
    except Exception as e:
        raise ValidationError({"error": f"NDI service error: {str(e)}"})


def create_user(claims):
    user = User.objects.create(
        cid=claims.get("cid"),
        first_name=claims.get("firstName"),
        last_name=claims.get("lastName"),
        phone=claims.get("mobileNumber"),
    )
    if claims.get("dzongkhagId") and claims.get("gewogId") and claims.get("villageSerialNo"):
        dob = datetime.datetime.strptime(claims.get("dob"), "%d/%m/%Y")
        dzo = fetch_address(Dzongkhag.objects.all(), claims.get("dzongkhagName"))
        gew = fetch_address(Gewog.objects.all(), claims.get("gewogName"))
        vill = fetch_address(Village.objects.all(), claims.get("villageName"))
        Profile.objects.create(
            dob=dob,
            dzongkhag_id=getattr(dzo, "id", None),
            gewog_id=getattr(gew, "id", None),
            village_id=getattr(vill, "id", None),
            user=user,
        )
    if len(user.roles.all()) == 0:
        roles = Role.objects.filter(name__in=["user"]).values_list("id", flat=True)
        user.roles.set(roles)
        user.current_role_id = roles.first()
        user.save()
    return user


def fetch_ndi_access_token():
    # Check if the access token is already cached and not expired
    access_token = cache.get("ndi_access_token")
    if access_token:
        return access_token

    service = SecureExternalService()
    try:
        response = service.request(
            "POST",
            f"{os.getenv('NDI_TOKEN_ENDPOINT')}",
            data={
                "client_id": os.getenv("NDI_CLIENT_ID"),
                "client_secret": os.getenv("NDI_CLIENT_SECRET"),
                "grant_type": "client_credentials",
            },
        )
        access_token = response.json().get("access_token")
        token_type = response.json().get("token_type")
        auth_token = f"{token_type} {access_token}"

        # Store the access token in the cache with a timeout of 2 hours
        cache.set("ndi_access_token", auth_token, timeout=7200)
        return auth_token
    except Exception as e:
        raise AuthenticationFailed(f"Something went wrong: {str(e)}")
