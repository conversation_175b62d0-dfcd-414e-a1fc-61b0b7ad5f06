from datetime import timedel<PERSON>
import os
from rest_framework.throttling import Anon<PERSON>ateThrottle, UserRateThrottle
from dotenv import load_dotenv

load_dotenv()

# JWT Settings
JWT_SETTINGS = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=30),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=1),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "ALGORITHM": "HS256",
    "AUTH_HEADER_TYPES": ("Bearer",),
}

JWT_SETTINGS = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=30),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=1),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "ALGORITHM": "HS256",
    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
    "SLIDING_TOKEN_LIFETIME": timedelta(days=1),
    "SLIDING_TOKEN_REFRESH_LIFETIME": timedelta(days=1),
    "TOKEN_OBTAIN_SERIALIZER": "common.serializers.CustomTokenObtainPairSerializer",
}

# Password validation
PASSWORD_VALIDATION = {
    "MIN_LENGTH": 8,
    "SPECIAL_CHARS_REQUIRED": True,
    "NUMBER_REQUIRED": True,
    "UPPERCASE_REQUIRED": True,
    "LOWERCASE_REQUIRED": True,
}


# Rate limiting classes
class CustomAnonRateThrottle(AnonRateThrottle):
    rate = "100/day"


class CustomUserRateThrottle(UserRateThrottle):
    rate = "1000/day"


class AuthenticationRateThrottle(AnonRateThrottle):
    rate = "5/minute"


# CORS Settings
CORS_SETTINGS = {
    "CORS_ALLOWED_ORIGINS": os.getenv("CORS_ALLOWED_ORIGINS", "http://localhost:3000").split(","),
    "CORS_ALLOW_CREDENTIALS": False,
    "CORS_ALLOW_METHODS": ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
}

# External Service Settings
EXTERNAL_SERVICE_SETTINGS = {
    "TIMEOUT": 10,  # seconds
    "MAX_RETRIES": 3,
    "BACKOFF_FACTOR": 0.3,
}
