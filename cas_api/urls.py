"""cas_api URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework import permissions

app_name = "cas_api"

schema_view = get_schema_view(
    openapi.Info(
        title="Construction Approval System API",
        default_version="v1",
        description="API documentation for Construction Approval System",
        terms_of_service="",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="Copyright DCPL"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
    url=settings.API_URL if hasattr(settings, "API_URL") else None,
)

urlpatterns = (
    [
        path("admin/", admin.site.urls),
        path("_mail", include("django_mail_viewer.urls")),
        re_path(r"^swagger(?P<format>\.json|\.yaml)$", schema_view.without_ui(cache_timeout=0), name="schema-json"),
        path("swagger/", schema_view.with_ui("swagger", cache_timeout=0), name="schema-swagger-ui"),
        path("redoc/", schema_view.with_ui("redoc", cache_timeout=0), name="schema-redoc"),
        path("api/v1/", include("user.urls")),
        path("api/v1/", include("address.urls")),
        path("api/v1/plannings/", include("planning.urls")),
        path("api/v1/buildings/", include("building.urls")),
        path("api/v1/", include("file.urls")),
        path("api/v1/", include("payment.urls")),
        path("api/v1/", include("common.urls")),
        path("api/v1/informations/", include("information.urls")),
        path("api/v1/designs/", include("design.urls")),
        path("api/v1/technicals/", include("technical.urls")),
        path("api/v1/ongoing_constructions/", include("ongoing_construction.urls")),
        path("api/v1/occupancies/", include("occupancy.urls")),
        path("api/v1/modifications/", include("modification.urls")),
        path("api/v1/governments/", include("government.urls")),
        path("api/v1/reports/", include("report.urls")),
    ]
    + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
)
