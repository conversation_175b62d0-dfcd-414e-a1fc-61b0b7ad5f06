import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter
from urllib3.util.retry import Retry
from cas_api.security import <PERSON>XTER<PERSON>L_SERVICE_SETTINGS
from rest_framework.exceptions import APIException


class SecureExternalService:
    def __init__(self):
        self.session = requests.Session()
        retry_strategy = Retry(
            total=EXTERNAL_SERVICE_SETTINGS["MAX_RETRIES"],
            backoff_factor=EXTERNAL_SERVICE_SETTINGS["BACKOFF_FACTOR"],
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("https://", adapter)
        self.session.mount("http://", adapter)

    def request(self, method, url, **kwargs):
        try:
            kwargs["timeout"] = EXTERNAL_SERVICE_SETTINGS["TIMEOUT"]
            kwargs["verify"] = True  # Verify SSL certificates
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.SSLError:
            raise APIException("SSL Certificate verification failed")
        except requests.exceptions.Timeout:
            raise APIException("Request timed out")
        except requests.exceptions.RequestException as e:
            raise APIException(f"External service error: {str(e)}")
