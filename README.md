# CAS API

```zsh
git clone https://gitlab.dcpl.bt/dcpl/cas_api.git
cd cas_api
```

Create a virtual environment to install dependencies in and activate it:
Using `venv`
```zsh
python -m venv env
source env/bin/activate
```

Then run the following command to install the dependencies:
```zsh
pip install -r requirements.txt
```

Run seed file to populate dummy data
```zsh
bash migrate_and_seed.sh
python manage.py runserver
```
To run the test suite:
```zsh
python manage.py test
```