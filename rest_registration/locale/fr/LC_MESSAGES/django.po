# Copyright (C) 2015-2019 <PERSON><PERSON><PERSON>
# This file is distributed under the same license as
# the Django-REST-Registration package.
# <PERSON> <<EMAIL>>, 2019.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-23 12:52-0400\n"
"PO-Revision-Date: 2019-10-23 13:01-0400\n"
"Last-Translator: <PERSON>\n"
"Language-Team: \n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 2.0.6\n"

#: api/serializers.py:159 api/views/change_password.py:40
msgid "Passwords don't match"
msgstr "Les mots de passe ne correspondent pas"

#: api/views/change_password.py:23
msgid "Old password is not correct"
msgstr "L'ancien mot de passe n'est pas valide"

#: api/views/change_password.py:60
msgid "Password changed successfully"
msgstr "Changement du mot de passe réussie"

#: api/views/login.py:40
msgid "Login or password invalid."
msgstr "Identifiant ou mot de passe non valide."

#: api/views/login.py:44
msgid "Login successful"
msgstr "Connexion réussie"

#: api/views/login.py:73
msgid "Cannot remove non-existent token"
msgstr "Ne peut supprimer un jeton non existant"

#: api/views/login.py:75
msgid "Logout successful"
msgstr "Déconnexion réussie"

#: api/views/register.py:85
msgid "User without email cannot be verified"
msgstr "Un usager sans mot de passe ne peut être vérifié"

#: api/views/register.py:130
msgid "User verified successfully"
msgstr "Vérification de l'usager réussi"

#: api/views/register_email.py:77 api/views/register_email.py:131
msgid "This email is already registered."
msgstr "Ce courriel est déjà enregistré."

#: api/views/register_email.py:91
msgid "Register email link email sent"
msgstr "Courriel du lien d'inscription envoyé"

#: api/views/register_email.py:110
msgid "Email verified successfully"
msgstr "Vérification du courriel réussi"

#: api/views/reset_password.py:106
msgid "Reset password successful"
msgstr "Réinitialisation du mot de passe réussi"

#: exceptions.py:7
msgid "Bad Request"
msgstr "Mauvaise requête"

#: exceptions.py:12
msgid "User not found"
msgstr "Usager non trouvé"

#: notifications/email.py:156
msgid "No 'subject' key found"
msgstr "Aucune clé 'sujet' trouvé"

#: utils/verification.py:13
msgid "Signature expired"
msgstr "Signature expirée"

#: utils/verification.py:15
msgid "Invalid signature"
msgstr "Signature invalide"
