upstream cas {
    server 0.0.0.0:8000;
}

server {
    server_name cas.local;
    client_max_body_size 200m;
    gzip on;
    gzip_comp_level 4;
    gzip_min_length 1000;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain application/javascript application/json application/x-javascript text/xml text/css application/xml text/javascript;

    add_header Access-Control-Allow-Origin "$http_origin" always;
    add_header Access-Control-Allow-Credentials "true" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD";
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";

    root /home/<USER>/Documents/nppc/nppc-web/build;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location ~* ^/assets {
        root /cas-api/statics/;
        expires 1y;
        add_header Cache-Control public;
        add_header Last-Modified "";
        add_header ETag "";
        break;
    }

    error_page 500 502 503 504 /500.html;

    location ~* ^/api|rails/ {
        proxy_pass http://cas;
        proxy_pass_header Set-Cookie;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $http_host;
        proxy_redirect off;
    }

    location ~ /.well-known {
        allow all;
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/localhost/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/localhost/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
}


server {
    if($host = cas.local) {
        return 301 https://$host$request_uri;
        } # managed by Certbot

        server_name cas.local;
        listen 80;
        return 404; # managed by Certbot

        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }
    }
