# Generated by Django 4.1.7 on 2025-05-29 17:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('occupancy', '0003_certificate_construction_id'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='certificate',
            index=models.Index(fields=['created_at'], name='occupancy_c_created_d31a70_idx'),
        ),
        migrations.AddIndex(
            model_name='certificate',
            index=models.Index(fields=['state'], name='occupancy_c_state_a8fa36_idx'),
        ),
        migrations.AddIndex(
            model_name='certificate',
            index=models.Index(fields=['permit'], name='occupancy_c_permit__54d73d_idx'),
        ),
        migrations.AddIndex(
            model_name='certificate',
            index=models.Index(fields=['construction_id'], name='occupancy_c_constru_b50874_idx'),
        ),
        migrations.AddIndex(
            model_name='certificate',
            index=models.Index(fields=['certificate_type'], name='occupancy_c_certifi_f48047_idx'),
        ),
    ]
