# Generated by Django 4.1.7 on 2025-05-20 07:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm
import occupancy.helpers.certificate
import occupancy.transitions.certificate


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('file', '0002_attachment_name_attachment_size_attachment_type'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('building', '0039_permit_development_type_permit_purpose'),
    ]

    operations = [
        migrations.CreateModel(
            name='Certificate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('remarks', models.TextField(blank=True, null=True)),
                ('state', django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('requested', 'Requested'), ('inspector_assigned', 'Inspector Assigned'), ('scheduled', 'Scheduled'), ('inspected', 'Inspected'), ('changes_required', 'Changes Required'), ('forwarded_to_chief', 'Forwarded to Chief'), ('pending_payment', 'Pending Payment'), ('pending_penalty_payment', 'Pending Penalty Payment'), ('building_details_update', 'Building Details Update'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('closed', 'Closed')], default='initiated', max_length=50)),
                ('serial_no', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('scheduled_date', models.DateField(blank=True, null=True)),
                ('schedule_remarks', models.TextField(blank=True, null=True)),
                ('changes_required', models.TextField(blank=True, null=True)),
                ('changes_verified', models.BooleanField(default=False)),
                ('penalty_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('penalty_remarks', models.TextField(blank=True, null=True)),
                ('building_number', models.CharField(blank=True, max_length=50, null=True)),
                ('building_qr_code', models.CharField(blank=True, max_length=255, null=True)),
                ('certificate_number', models.CharField(blank=True, max_length=50, null=True)),
                ('certificate_date', models.DateField(blank=True, null=True)),
                ('by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_certificates', to=settings.AUTH_USER_MODEL)),
                ('permit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='certificates', to='building.permit')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='certificates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'occupancy certificate',
                'verbose_name_plural': 'occupancy certificates',
                'ordering': ['-created_at'],
            },
            bases=(models.Model, occupancy.helpers.certificate.CertificateHelper, occupancy.transitions.certificate.CertificateTransition),
        ),
        migrations.CreateModel(
            name='InspectionReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('issue_type', models.CharField(choices=[('no_issue', 'No Issue'), ('minor_issue', 'Minor Issue'), ('major_issue', 'Major Issue')], default='no_issue', max_length=20)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('recommendations', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('site_visit_date', models.DateField(blank=True, null=True)),
                ('site_visit_time', models.TimeField(blank=True, null=True)),
                ('site_visit_location', models.CharField(blank=True, max_length=255, null=True)),
                ('illegal_structures', models.BooleanField(default=False)),
                ('illegal_structures_remarks', models.TextField(blank=True, null=True)),
                ('setback_deviations', models.BooleanField(default=False)),
                ('setback_deviations_remarks', models.TextField(blank=True, null=True)),
                ('architectural_deviations', models.BooleanField(default=False)),
                ('architectural_deviations_remarks', models.TextField(blank=True, null=True)),
                ('basement_parking_issues', models.BooleanField(default=False)),
                ('basement_parking_issues_remarks', models.TextField(blank=True, null=True)),
                ('safety_grills_issues', models.BooleanField(default=False)),
                ('safety_grills_issues_remarks', models.TextField(blank=True, null=True)),
                ('debris_cleared', models.BooleanField(default=True)),
                ('debris_cleared_remarks', models.TextField(blank=True, null=True)),
                ('septic_drainage_issues', models.BooleanField(default=False)),
                ('septic_drainage_issues_remarks', models.TextField(blank=True, null=True)),
                ('property_damages', models.BooleanField(default=False)),
                ('property_damages_remarks', models.TextField(blank=True, null=True)),
                ('certified_work', models.BooleanField(default=True)),
                ('certified_work_remarks', models.TextField(blank=True, null=True)),
                ('other_issues', models.TextField(blank=True, null=True)),
                ('attachments', models.ManyToManyField(blank=True, related_name='occupancy_inspection_report', to='file.attachment')),
                ('certificate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inspection_reports', to='occupancy.certificate')),
                ('inspector', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='occupancy_inspection_reports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'inspection report',
                'verbose_name_plural': 'inspection reports',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BuildingUnit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('unit_number', models.CharField(max_length=50)),
                ('unit_qr_code', models.CharField(blank=True, max_length=255, null=True)),
                ('floor_number', models.IntegerField()),
                ('area_sqft', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('certificate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='building_units', to='occupancy.certificate')),
            ],
            options={
                'verbose_name': 'building unit',
                'verbose_name_plural': 'building units',
                'ordering': ['floor_number', 'unit_number'],
            },
        ),
    ]
