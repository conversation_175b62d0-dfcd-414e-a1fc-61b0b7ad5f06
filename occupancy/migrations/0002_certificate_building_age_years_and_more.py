# Generated by Django 4.1.7 on 2025-05-22 12:33

from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('occupancy', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='certificate',
            name='building_age_years',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='certificate',
            name='certificate_type',
            field=models.CharField(choices=[('new', 'New Certificate'), ('renewal', 'Certificate Renewal')], default='new', max_length=20),
        ),
        migrations.AddField(
            model_name='certificate',
            name='parent_certificate',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='renewals', to='occupancy.certificate'),
        ),
        migrations.AddField(
            model_name='certificate',
            name='valid_from',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='certificate',
            name='valid_until',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='certificate',
            name='state',
            field=django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('requested', 'Requested'), ('inspector_assigned', 'Inspector Assigned'), ('scheduled', 'Scheduled'), ('inspected', 'Inspected'), ('changes_required', 'Changes Required'), ('forwarded_to_chief', 'Forwarded to Chief'), ('pending_payment', 'Pending Payment'), ('pending_penalty_payment', 'Pending Penalty Payment'), ('building_details_update', 'Building Details Update'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('closed', 'Closed'), ('renewal_notification', 'Renewal Notification'), ('renewal_requested', 'Renewal Requested'), ('renewal_inspection_requested', 'Renewal Inspection Requested')], default='initiated', max_length=50),
        ),
    ]
