from django.db import models
from django.contrib.contenttypes.fields import GenericRelation
from django_fsm import FSMField
from django.utils.translation import gettext_lazy as _
from user.models import User
from django.utils.crypto import get_random_string
from django.utils import timezone
from datetime import timedelta

from occupancy.enums import CertificateStatus, InspectionReportStatus, IssueType, CertificateType
from occupancy.transitions.certificate import CertificateTransition
from occupancy.helpers.certificate import CertificateHelper


class Certificate(models.Model, CertificateHelper, CertificateTransition):
    """
    Model for Occupancy Certificate
    """

    permit = models.ForeignKey("building.Permit", on_delete=models.CASCADE, related_name="certificates")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="certificates")
    by = models.ForeignKey(User, related_name="assigned_certificates", null=True, blank=True, on_delete=models.SET_NULL)
    remarks = models.TextField(null=True, blank=True)
    state = FSMField(max_length=50, choices=CertificateStatus.choices, default=CertificateStatus.INITIATED)
    task_pools = GenericRelation("common.TaskPool", related_query_name="certificate")
    payments = GenericRelation("payment.Payment", related_query_name="certificate")
    serial_no = models.CharField(max_length=50, null=True, blank=True)
    construction_id = models.CharField(max_length=100, null=True, blank=True, help_text="Unique identifier for tracking construction across all modules")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Certificate type (new or renewal)
    certificate_type = models.CharField(max_length=20, choices=CertificateType.choices, default=CertificateType.NEW)

    # For renewal certificates, reference to the original certificate
    parent_certificate = models.ForeignKey("self", on_delete=models.SET_NULL, null=True, blank=True, related_name="renewals")

    # Validity period
    valid_from = models.DateField(null=True, blank=True)
    valid_until = models.DateField(null=True, blank=True)

    # Building age (for determining validity period)
    building_age_years = models.PositiveIntegerField(null=True, blank=True)

    # Fields for inspection scheduling
    scheduled_date = models.DateField(null=True, blank=True)
    schedule_remarks = models.TextField(null=True, blank=True)

    # Fields for tracking changes required and verification
    changes_required = models.TextField(null=True, blank=True)
    changes_verified = models.BooleanField(default=False)

    # Fields for penalty
    penalty_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    penalty_remarks = models.TextField(null=True, blank=True)

    # Fields for building details
    building_number = models.CharField(max_length=50, null=True, blank=True)
    building_qr_code = models.CharField(max_length=255, null=True, blank=True)

    # Fields for certificate
    certificate_number = models.CharField(max_length=50, null=True, blank=True)
    certificate_date = models.DateField(null=True, blank=True)

    class Meta:
        verbose_name = _("occupancy certificate")
        verbose_name_plural = _("occupancy certificates")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["created_at"]),
            models.Index(fields=["state"]),
            models.Index(fields=["permit"]),
            models.Index(fields=["construction_id"]),
            models.Index(fields=["certificate_type"]),
        ]

    def __str__(self):
        return f"Occupancy Certificate {self.id} - {self.get_state_display()}"


class InspectionReport(models.Model):
    """
    Model for inspection reports created by Building Inspectors
    """

    certificate = models.ForeignKey(Certificate, on_delete=models.CASCADE, related_name="inspection_reports")
    inspector = models.ForeignKey(User, on_delete=models.CASCADE, related_name="occupancy_inspection_reports")
    issue_type = models.CharField(max_length=20, choices=IssueType.choices, default=IssueType.NO_ISSUE)
    remarks = models.TextField(null=True, blank=True)
    recommendations = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=InspectionReportStatus.choices, default=InspectionReportStatus.PENDING)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    attachments = models.ManyToManyField("file.Attachment", related_name="occupancy_inspection_report", blank=True)

    # Additional fields for site inspection
    site_visit_date = models.DateField(null=True, blank=True)
    site_visit_time = models.TimeField(null=True, blank=True)
    site_visit_location = models.CharField(max_length=255, null=True, blank=True)

    # Inspection checklist fields
    illegal_structures = models.BooleanField(default=False)
    illegal_structures_remarks = models.TextField(null=True, blank=True)

    setback_deviations = models.BooleanField(default=False)
    setback_deviations_remarks = models.TextField(null=True, blank=True)

    architectural_deviations = models.BooleanField(default=False)
    architectural_deviations_remarks = models.TextField(null=True, blank=True)

    basement_parking_issues = models.BooleanField(default=False)
    basement_parking_issues_remarks = models.TextField(null=True, blank=True)

    safety_grills_issues = models.BooleanField(default=False)
    safety_grills_issues_remarks = models.TextField(null=True, blank=True)

    debris_cleared = models.BooleanField(default=True)
    debris_cleared_remarks = models.TextField(null=True, blank=True)

    septic_drainage_issues = models.BooleanField(default=False)
    septic_drainage_issues_remarks = models.TextField(null=True, blank=True)

    property_damages = models.BooleanField(default=False)
    property_damages_remarks = models.TextField(null=True, blank=True)

    certified_work = models.BooleanField(default=True)
    certified_work_remarks = models.TextField(null=True, blank=True)

    other_issues = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = _("inspection report")
        verbose_name_plural = _("inspection reports")
        ordering = ["-created_at"]

    def __str__(self):
        return f"Inspection Report {self.id} - {self.get_issue_type_display()}"


class BuildingUnit(models.Model):
    """
    Model for building units with QR codes
    """

    certificate = models.ForeignKey(Certificate, on_delete=models.CASCADE, related_name="building_units")
    unit_number = models.CharField(max_length=50)
    unit_qr_code = models.CharField(max_length=255, null=True, blank=True)
    floor_number = models.IntegerField()
    area_sqft = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("building unit")
        verbose_name_plural = _("building units")
        ordering = ["floor_number", "unit_number"]

    def __str__(self):
        return f"Unit {self.unit_number} on Floor {self.floor_number}"
