from django.db.models import Q
from django.utils import timezone
from datetime import timedelta
from occupancy.models import Certificate


class CertificatePopulator:
    """
    Populator for Certificate queryset based on user role
    """

    def __init__(self, user, query_params=None):
        self.current_user = user
        self.params = query_params or {}
        self.records = Certificate.objects.all()

    def populate(self):
        state = self.params.get("state")
        active_task = self.params.get("active_task")
        role_name = self.current_user.current_role.name
        dzo_id = self.params.get("dzongkhag_id")
        thrm_id = self.params.get("thromde_id")
        gewog_id = self.params.get("gewog_id")

        if role_name == "admin":
            self.records = self.records
        elif role_name == "user":
            self.records = self.records.filter(user=self.current_user)
        elif role_name in ["dce", "dadm", "dzongda"]:
            self.records = self.records.filter(permit__dzongkhag=self.current_user.dzongkhag)
        elif role_name in ["tce", "tadm", "thrompon"]:
            self.records = self.records.filter(permit__thromde=self.current_user.thromde)
        elif role_name in ["roidce", "roidadm"]:
            self.records = self.records.filter(permit__region=self.current_user.region)
        elif role_name in ["gup"]:
            self.records = self.records.filter(permit__gewog=self.current_user.gewog)
        else:
            self.records = self.records.filter(task_pools__user=self.current_user)

        if active_task == "true":
            self.records = self.records.filter(task_pools__user=self.current_user, task_pools__state__in=["assigned", "in_progress"])
        else:
            self.records = self.records.exclude(id__in=self.records.filter(task_pools__user=self.current_user, task_pools__state__in=["assigned", "in_progress"]))

        if state:
            self.records = self.records.filter(state=state)
        if dzo_id:
            self.records = self.records.filter(dzongkhag_id=dzo_id)
        if thrm_id:
            self.records = self.records.filter(thromde_id=thrm_id)
        if gewog_id:
            self.records = self.records.filter(gewog_id=gewog_id)
        if self.params.get("expiring_soon") == "true":
            today = timezone.now().date()
            three_months_from_now = today + timedelta(days=90)
            self.records = self.records.filter(state__in=["approved", "closed"], valid_until__isnull=False, valid_until__lte=three_months_from_now)
        if self.params.get("eligible_for_renewal") == "true":
            today = timezone.now().date()
            three_months_from_now = today + timedelta(days=90)
            self.records = self.records.filter(state__in=["approved", "closed"], valid_until__isnull=False, valid_until__lte=three_months_from_now)

        return self.records
