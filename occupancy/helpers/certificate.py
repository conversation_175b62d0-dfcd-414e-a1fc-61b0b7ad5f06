from django.utils.crypto import get_random_string
from django.utils import timezone
from datetime import timedelta
from rest_framework.exceptions import ValidationError


class CertificateHelper:
    """
    Helper methods for the Certificate model
    """

    def generate_serial_no(self):
        """
        Generate a unique serial number for the occupancy certificate
        """
        if not self.serial_no:
            prefix = "OCC"
            random_str = get_random_string(length=8, allowed_chars="0123456789")
            self.serial_no = f"{prefix}-{random_str}"
            self.save(update_fields=["serial_no"])
        return self.serial_no

    def generate_certificate_number(self):
        """
        Generate a certificate number
        """
        if not self.certificate_number:
            prefix = "CERT"
            random_str = get_random_string(length=8, allowed_chars="0123456789")
            self.certificate_number = f"{prefix}-{random_str}"
            self.save(update_fields=["certificate_number"])
        return self.certificate_number

    def generate_building_number(self):
        """
        Generate a building number
        """
        if not self.building_number:
            prefix = "BLD"
            random_str = get_random_string(length=8, allowed_chars="0123456789")
            self.building_number = f"{prefix}-{random_str}"
            self.save(update_fields=["building_number"])
        return self.building_number

    def generate_building_qr_code(self):
        """
        Generate a QR code for the building
        """
        if not self.building_qr_code:
            # In a real implementation, this would generate an actual QR code
            # For now, we'll just use a placeholder
            self.building_qr_code = f"QR-{self.building_number}"
            self.save(update_fields=["building_qr_code"])
        return self.building_qr_code

    @property
    def nature(self):
        return self.permit.nature

    @property
    def dzongkhag(self):
        return self.permit.dzongkhag

    @property
    def thromde(self):
        return self.permit.thromde

    @property
    def gewog(self):
        return self.permit.gewog

    def can_request_occupancy_certificate(self, user):
        """
        Check if the user can request an occupancy certificate
        """
        # Only the permit owner can request an occupancy certificate
        if user.id != self.permit.user_id:
            raise ValidationError({"error": "Only the permit owner can request an occupancy certificate."})

        # Check if the permit is in the correct state (closed)
        if self.permit.state != "closed":
            raise ValidationError({"error": "The permit must be closed before requesting an occupancy certificate."})

        return True

    def can_assign_inspector(self, user):
        """
        Check if the user can assign an inspector
        """
        # Only DRO or Chief can assign an inspector
        return user.current_role.name in ["dce", "tce", "dro", "hoi"]

    def can_schedule(self, user):
        """
        Check if the user can schedule an inspection
        """
        # Only building inspector can schedule an inspection
        return user.current_role.name in ["tbi", "dbi", "dro", "dro", "hoi"]

    def can_inspect(self, user):
        """
        Check if the user can schedule an inspection
        """
        # Only building inspector can schedule an inspection
        return user.current_role.name in ["tbi", "dbi", "dro", "dro", "hoi"]

    def can_verify_changes(self, user):
        """
        Check if the user can schedule an inspection
        """
        # Only building inspector can schedule an inspection
        return user.current_role.name in ["tbi", "dbi", "dro", "dro", "hoi"]

    def can_forward_to_chief(self, user):
        """
        Check if the user can schedule an inspection
        """
        # Only building inspector can schedule an inspection
        return user.current_role.name in ["tbi", "dbi", "dro", "dro", "hoi"]

    def can_payment(self, user):
        """
        Check if the user can schedule an inspection
        """
        # Only building inspector can schedule an inspection
        return user.current_role.name in ["tce", "dce"]

    def can_penalty_payment(self, user):
        """
        Check if the user can schedule an inspection
        """
        # Only building inspector can schedule an inspection
        return user.current_role.name in ["tce", "dce"]

    def can_approve(self, user):
        """
        Check if the user can approve the certificate
        """
        # Only chief can approve
        return user.current_role.name in ["dbi", "tbi"]

    def can_reject(self, user):
        """
        Check if the user can reject the certificate
        """
        # Only chief or DRO can reject
        return user.current_role.name in ["dce", "tce", "dro", "hoi"]

    def can_require_changes(self, user):
        """
        Check if the user can require changes
        """
        # Only building inspector, DRO, or chief can require changes
        return True

    def can_update_building_details(self, user):
        """
        Check if the user can update building details
        """
        # Only building inspector can update building details
        return user.current_role.name in ["tbi", "dbi", "dro"]

    def calculate_validity_period(self):
        """
        Calculate the validity period based on building age

        Rules:
        - Buildings up to 20 years old: 5 years validity
        - Buildings 20-30 years old: 3 years validity
        - Buildings over 30 years: Requires certified engineer
        - Alternative rule (BBR): 5 years for first 15 years, 3 years thereafter
        - Buildings over 50 years: Requires report by certified engineer
        """
        if not self.building_age_years:
            # Default to 5 years if building age is not specified
            return 5
        if self.building_age_years <= 20:
            return 5
        elif self.building_age_years <= 30:
            return 3
        else:
            # For buildings over 30 years, default to 3 years
            # but will require certified engineer report
            return 3

    def set_validity_period(self):
        """
        Set the validity period for the certificate
        """
        if not self.valid_from:
            self.valid_from = timezone.now().date()

        validity_years = self.calculate_validity_period()
        self.valid_until = self.valid_from + timedelta(days=365 * validity_years)
        self.save(update_fields=["valid_from", "valid_until"])

    def is_eligible_for_renewal(self):
        """
        Check if the certificate is eligible for renewal
        """
        # Allow certificates in approved, closed, or renewal_notification states
        if self.state != "approved" and self.state != "closed" and self.state != "renewal_notification":
            return False
        if not self.valid_until:
            return False
        today = timezone.now().date()
        three_months_from_now = today + timedelta(days=90)

        return self.valid_until <= three_months_from_now

    def can_request_renewal(self, user):
        """
        Check if the user can request a renewal
        """
        if user.id != self.user_id:
            raise ValidationError({"error": "Only the certificate owner can request a renewal."})
        if not self.is_eligible_for_renewal():
            raise ValidationError({"error": "This certificate is not eligible for renewal yet."})
        return True

    def create_renewal_certificate(self):
        """
        Create a renewal certificate based on this certificate
        """
        from occupancy.enums import CertificateType

        # Calculate building age for renewal
        if self.building_age_years is not None:
            new_building_age = self.building_age_years + self.calculate_validity_period()
        else:
            new_building_age = 5  # Default to 5 years if building_age_years is None

        renewal = type(self).objects.create(
            permit=self.permit,
            user=self.user,
            certificate_type=CertificateType.RENEWAL,
            parent_certificate=self,
            building_age_years=new_building_age,
            building_number=self.building_number,
        )

        return renewal

    def pay(self, payment=None):
        self.update_building_details({}, by=self.user)
        self.save()
