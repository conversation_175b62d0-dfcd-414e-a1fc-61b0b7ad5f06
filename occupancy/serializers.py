from rest_framework import serializers
from drf_writable_nested.serializers import WritableNestedModelSerializer
from building.models import Permit
from file.models import Attachment
from file.serializers import AttachmentSerializer
from occupancy.models import Certificate, InspectionReport, BuildingUnit
from payment.serializers import PaymentSerializer
from common.serializers import TaskPoolSerializer
from user.models import User


class BuildingUnitSerializer(serializers.ModelSerializer):
    """
    Serializer for BuildingUnit model
    """

    certificate_id = serializers.PrimaryKeyRelatedField(queryset=Certificate.objects.all(), required=True)

    class Meta:
        model = BuildingUnit
        fields = [
            "id",
            "certificate_id",
            "unit_number",
            "unit_qr_code",
            "floor_number",
            "area_sqft",
            "created_at",
            "updated_at",
        ]


class InspectionReportSerializer(WritableNestedModelSerializer):
    """
    Serializer for InspectionReport model
    """

    attachments = AttachmentSerializer(many=True, required=False)
    attachment_ids = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="attachments", many=True, required=False, write_only=True)
    issue_type_display = serializers.CharField(source="get_issue_type_display", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    certificate_id = serializers.PrimaryKeyRelatedField(queryset=Certificate.objects.all(), source="certificate", required=True)
    inspector = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), required=False)

    class Meta:
        model = InspectionReport
        fields = [
            "id",
            "certificate_id",
            "issue_type",
            "issue_type_display",
            "remarks",
            "inspector",
            "recommendations",
            "status",
            "status_display",
            "created_at",
            "attachments",
            "site_visit_date",
            "site_visit_time",
            "site_visit_location",
            "illegal_structures",
            "illegal_structures_remarks",
            "setback_deviations",
            "setback_deviations_remarks",
            "architectural_deviations",
            "architectural_deviations_remarks",
            "basement_parking_issues",
            "basement_parking_issues_remarks",
            "safety_grills_issues",
            "safety_grills_issues_remarks",
            "debris_cleared",
            "debris_cleared_remarks",
            "septic_drainage_issues",
            "septic_drainage_issues_remarks",
            "property_damages",
            "property_damages_remarks",
            "certified_work",
            "certified_work_remarks",
            "other_issues",
            "attachment_ids",
        ]


class CertificateSerializer(WritableNestedModelSerializer):
    """
    Serializer for Certificate model
    """

    permit_id = serializers.PrimaryKeyRelatedField(queryset=Permit.objects.all(), source="permit", required=True)
    permit_serial_no = serializers.CharField(source="permit.serial_no", read_only=True)
    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    state_display = serializers.CharField(source="get_state_display", read_only=True)
    certificate_type_display = serializers.CharField(source="get_certificate_type_display", read_only=True)
    payments = PaymentSerializer(many=True, read_only=True)
    parent_certificate_id = serializers.PrimaryKeyRelatedField(queryset=Certificate.objects.all(), source="parent_certificate", required=False, allow_null=True)

    class Meta:
        model = Certificate
        fields = [
            "id",
            "permit_id",
            "permit_serial_no",
            "user_id",
            "user_name",
            "remarks",
            "state",
            "state_display",
            "serial_no",
            "construction_id",
            "created_at",
            "certificate_type",
            "certificate_type_display",
            "parent_certificate_id",
            "valid_from",
            "valid_until",
            "building_age_years",
            "scheduled_date",
            "schedule_remarks",
            "changes_required",
            "changes_verified",
            "penalty_amount",
            "penalty_remarks",
            "building_number",
            "building_qr_code",
            "certificate_number",
            "certificate_date",
            "payments",
        ]


class CertificateListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing Certificate instances
    """

    permit = serializers.PrimaryKeyRelatedField(queryset=Permit.objects.all(), required=False)
    permit_serial_no = serializers.CharField(source="permit.serial_no", read_only=True)
    user = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), required=False)
    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    state_display = serializers.CharField(source="get_state_display", read_only=True)
    certificate_type_display = serializers.CharField(source="get_certificate_type_display", read_only=True)
    parent_certificate = serializers.PrimaryKeyRelatedField(queryset=Certificate.objects.all(), required=False, allow_null=True)

    class Meta:
        model = Certificate
        fields = [
            "id",
            "permit",
            "permit_id",
            "permit_serial_no",
            "user",
            "user_id",
            "user_name",
            "state",
            "state_display",
            "serial_no",
            "construction_id",
            "created_at",
            "updated_at",
            "certificate_type",
            "certificate_type_display",
            "parent_certificate",
            "valid_from",
            "valid_until",
            "scheduled_date",
            "building_number",
            "certificate_number",
            "certificate_date",
        ]
