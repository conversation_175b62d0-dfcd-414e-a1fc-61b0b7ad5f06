from django.contrib import admin
from occupancy.models import Certificate, InspectionReport, BuildingUnit


@admin.register(Certificate)
class CertificateAdmin(admin.ModelAdmin):
    list_display = ("serial_no", "permit", "user", "state", "created_at")
    list_filter = ("state",)
    search_fields = ("serial_no", "user__first_name", "user__last_name", "permit__serial_no")
    date_hierarchy = "created_at"


@admin.register(InspectionReport)
class InspectionReportAdmin(admin.ModelAdmin):
    list_display = ("id", "certificate", "inspector", "issue_type", "status", "created_at")
    list_filter = ("issue_type", "status")
    search_fields = ("certificate__serial_no", "inspector__first_name", "inspector__last_name")
    date_hierarchy = "created_at"


@admin.register(BuildingUnit)
class BuildingUnitAdmin(admin.ModelAdmin):
    list_display = ("unit_number", "floor_number", "certificate", "area_sqft", "created_at")
    list_filter = ("floor_number",)
    search_fields = ("unit_number", "certificate__serial_no")
    date_hierarchy = "created_at"
