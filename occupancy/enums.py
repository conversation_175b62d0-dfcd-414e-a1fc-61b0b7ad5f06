from django.db import models
from django.utils.translation import gettext_lazy as _


class CertificateStatus(models.TextChoices):
    INITIATED = "initiated", _("Initiated")
    REQUESTED = "requested", _("Requested")
    INSPECTOR_ASSIGNED = "inspector_assigned", _("Inspector Assigned")
    SCHEDULED = "scheduled", _("Scheduled")
    INSPECTED = "inspected", _("Inspected")
    CHANGES_REQUIRED = "changes_required", _("Changes Required")
    FORWARDED_TO_CHIEF = "forwarded_to_chief", _("Forwarded to Chief")
    PENDING_PAYMENT = "pending_payment", _("Pending Payment")
    PENDING_PENALTY_PAYMENT = "pending_penalty_payment", _("Pending Penalty Payment")
    BUILDING_DETAILS_UPDATE = "building_details_update", _("Building Details Update")
    APPROVED = "approved", _("Approved")
    REJECTED = "rejected", _("Rejected")
    CLOSED = "closed", _("Closed")

    # Renewal specific states
    RENEWAL_NOTIFICATION = "renewal_notification", _("Renewal Notification")
    RENEWAL_REQUESTED = "renewal_requested", _("Renewal Requested")
    RENEWAL_INSPECTION_REQUESTED = "renewal_inspection_requested", _("Renewal Inspection Requested")


class CertificateType(models.TextChoices):
    NEW = "new", _("New Certificate")
    RENEWAL = "renewal", _("Certificate Renewal")


class InspectionReportStatus(models.TextChoices):
    PENDING = "pending", _("Pending")
    APPROVED = "approved", _("Approved")
    REJECTED = "rejected", _("Rejected")


class IssueType(models.TextChoices):
    NO_ISSUE = "no_issue", _("No Issue")
    MINOR_ISSUE = "minor_issue", _("Minor Issue")
    MAJOR_ISSUE = "major_issue", _("Major Issue")
