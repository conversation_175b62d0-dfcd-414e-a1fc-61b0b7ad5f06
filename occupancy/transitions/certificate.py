from django_fsm import transition
from django_fsm_log.decorators import fsm_log_by, fsm_log_description
from rest_framework.exceptions import ValidationError
from occupancy.enums import CertificateStatus, CertificateType


class CertificateTransition:
    """
    Transitions for the Certificate model
    """

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="initiated", target="requested")
    def initial(self, by=None, description=None):
        """
        Initial transition when a certificate is first created
        """
        self.by = by
        description.set("Occupancy certificate initiated.")

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="requested", target="inspector_assigned")
    def assign_inspector(self, data, by=None, description=None):
        """
        Assign a building inspector to the certificate
        """
        self.by = by
        self.data = data
        description.set(data.get("remarks", "Inspector assigned."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="inspector_assigned", target="scheduled")
    def schedule(self, data, by=None, description=None):
        """
        Schedule an inspection
        """
        self.by = by
        self.scheduled_date = data.get("scheduled_date")
        self.schedule_remarks = data.get("schedule_remarks")
        description.set(data.get("remarks", "Inspection scheduled."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="scheduled", target="inspected")
    def inspect(self, data, by=None, description=None):
        """
        Mark as inspected after site visit
        """
        self.by = by
        description.set(data.get("remarks", "Site inspection completed."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="inspected", target="changes_required")
    def require_changes(self, data, by=None, description=None):
        """
        Require changes after inspection
        """
        self.by = by
        self.changes_required = data.get("changes_required")
        self.penalty_amount = data.get("penalty_amount")
        self.penalty_remarks = data.get("penalty_remarks")
        description.set(data.get("remarks", "Changes required."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="changes_required", target="inspected")
    def verify_changes(self, data, by=None, description=None):
        """
        Verify changes after they have been made
        """
        self.by = by
        self.changes_verified = True
        description.set(data.get("remarks", "Changes verified."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="inspected", target="forwarded_to_chief")
    def forward_to_chief(self, data, by=None, description=None):
        """
        Forward to chief for approval
        """
        self.by = by
        description.set(data.get("remarks", "Forwarded to chief for approval."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="forwarded_to_chief", target="pending_payment")
    def payment(self, data, by=None, description=None):
        """
        Require payment
        """
        self.by = by
        self.data = data
        description.set(data.get("remarks", "Payment required."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="forwarded_to_chief", target="pending_penalty_payment")
    def penalty_payment(self, data, by=None, description=None):
        """
        Require penalty payment
        """
        self.by = by
        self.penalty_amount = data.get("penalty_amount")
        self.penalty_remarks = data.get("penalty_remarks")
        description.set(data.get("remarks", "Penalty payment required."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["pending_payment", "pending_penalty_payment"], target="building_details_update")
    def update_building_details(self, data, by=None, description=None):
        """
        Update building details after payment
        """
        self.by = by
        description.set(data.get("remarks", "Building details update required."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="building_details_update", target="approved")
    def approve(self, data, by=None, description=None):
        """
        Approve the certificate
        """
        self.by = by
        self.data = data
        self.certificate_date = data.get("certificate_date")
        description.set(data.get("remarks", "Occupancy certificate approved."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="rejected")
    def reject(self, data, by=None, description=None):
        """
        Reject the certificate
        """
        self.by = by
        self.data = data
        if not data.get("remarks"):
            raise ValidationError({"error": "You should provide rejection remarks."})
        self.remarks = data.get("remarks")
        description.set(f"Reason for rejection: {data.get('remarks')}")

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="approved", target="closed")
    def close(self, data, by=None, description=None):
        """
        Close the certificate
        """
        self.by = by
        description.set(data.get("remarks", "Occupancy certificate closed."))

    # Renewal-related transitions

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["approved", "closed"], target=CertificateStatus.RENEWAL_NOTIFICATION)
    def notify_renewal(self, data, by=None, description=None):
        """
        Send notification for certificate renewal
        """
        self.by = by
        description.set(data.get("remarks", "Renewal notification sent."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=CertificateStatus.RENEWAL_NOTIFICATION, target=CertificateStatus.RENEWAL_REQUESTED)
    def request_renewal(self, data, by=None, description=None):
        """
        Request certificate renewal
        """
        self.by = by
        description.set(data.get("remarks", "Certificate renewal requested."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=CertificateStatus.RENEWAL_REQUESTED, target=CertificateStatus.RENEWAL_INSPECTION_REQUESTED)
    def request_renewal_inspection(self, data, by=None, description=None):
        """
        Request inspection for certificate renewal
        """
        self.by = by
        description.set(data.get("remarks", "Renewal inspection requested."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=CertificateStatus.RENEWAL_INSPECTION_REQUESTED, target=CertificateStatus.INSPECTOR_ASSIGNED)
    def assign_renewal_inspector(self, data, by=None, description=None):
        """
        Assign inspector for renewal inspection
        """
        self.by = by
        description.set(data.get("remarks", "Renewal inspector assigned."))
