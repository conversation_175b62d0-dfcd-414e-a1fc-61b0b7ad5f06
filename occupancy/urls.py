from django.urls import path
from occupancy import views

urlpatterns = [
    # Certificate endpoints
    path("certificates/", views.CertificateView.as_view(), name="certificates"),
    path("certificates/<int:pk>/", views.CertificateDetailView.as_view(), name="certificate_detail"),
    path("certificates/<int:pk>/transition/", views.CertificateTransitionView.as_view({"put": "transition"}), name="certificate_transition"),
    path("certificates/<int:pk>/activity_logs/", views.CertificateTransitionView.as_view({"get": "activity_logs"}), name="certificate_activity_logs"),
    path("certificates/<int:pk>/pdf/", views.CertificatePdfView.as_view(), name="certificate_pdf"),
    path("certificates/<int:pk>/notify_renewal/", views.CertificateTransitionView.as_view({"post": "notify_renewal"}), name="certificate_notify_renewal"),
    path("certificate_renewals/", views.CertificateRenewalView.as_view(), name="certificate_renewals"),
    # Inspection Report endpoints
    path("inspection_reports/", views.InspectionReportView.as_view(), name="inspection_reports"),
    path("inspection_reports/<int:pk>/", views.InspectionReportDetailView.as_view(), name="inspection_report_detail"),
    # Building Unit endpoints
    path("building_units/", views.BuildingUnitView.as_view(), name="building_units"),
    path("building_units/<int:pk>/", views.BuildingUnitDetailView.as_view(), name="building_unit_detail"),
]
