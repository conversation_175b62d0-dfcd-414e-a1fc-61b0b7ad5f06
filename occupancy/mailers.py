from django.template.loader import render_to_string
from cas_api.celery import app
from occupancy.models import Certificate
from user.models import User
from django.shortcuts import get_object_or_404
import os
from cas_api.services.mailer_service import send_mail


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_requested(self, certificate_id, chief_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        chief = get_object_or_404(User, pk=chief_id)
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "chief": chief,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/requested.html", context)
        send_mail("New Occupancy Certificate Request", html_content, [chief.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_requested")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_inspector_assigned(self, certificate_id, inspector_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        inspector = get_object_or_404(User, pk=inspector_id)
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "inspector": inspector,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/inspector_assigned.html", context)
        send_mail("Occupancy Certificate Inspection Assignment", html_content, [inspector.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_inspector_assigned")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_scheduled(self, certificate_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "scheduled_date": certificate.scheduled_date,
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/scheduled.html", context)
        send_mail("Occupancy Certificate Inspection Scheduled", html_content, [certificate.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_scheduled")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_inspected(self, certificate_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/inspected.html", context)
        send_mail("Occupancy Certificate Inspection Completed", html_content, [certificate.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_inspected")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_changes_required(self, certificate_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "changes_required": certificate.changes_required,
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/changes_required.html", context)
        send_mail("Changes Required for Occupancy Certificate", html_content, [certificate.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_changes_required")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forwarded_to_chief(self, certificate_id, chief_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        chief = get_object_or_404(User, pk=chief_id)
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "chief": chief,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/forwarded_to_chief.html", context)
        send_mail("Occupancy Certificate Forwarded for Approval", html_content, [chief.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_forwarded_to_chief")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_payment_required(self, certificate_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "payment": certificate.payments.last(),
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/payment_required.html", context)
        send_mail("Payment Required for Occupancy Certificate", html_content, [certificate.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_payment_required")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_penalty_payment_required(self, certificate_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "payment": certificate.payments.last(),
            "penalty_amount": certificate.penalty_amount,
            "penalty_remarks": certificate.penalty_remarks,
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/penalty_payment_required.html", context)
        send_mail("Penalty Payment Required for Occupancy Certificate", html_content, [certificate.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_penalty_payment_required")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_approved(self, certificate_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "building_number": certificate.building_number,
            "building_qr_code": certificate.building_qr_code,
            "building_units": certificate.building_units.all(),
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/approved.html", context)
        send_mail("Occupancy Certificate Approved", html_content, [certificate.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_approved")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_rejected(self, certificate_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "remarks": certificate.remarks,
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/rejected.html", context)
        send_mail("Occupancy Certificate Rejected", html_content, [certificate.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_rejected")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_renewal(self, certificate_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "valid_until": certificate.valid_until,
            "building_number": certificate.building_number,
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/renewal_notification.html", context)
        send_mail("Occupancy Certificate Renewal Notification", html_content, [certificate.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_renewal")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_renewal_requested(self, certificate_id):
    try:
        certificate = get_object_or_404(Certificate, pk=certificate_id)
        dros = User.objects.filter(roles__name="dro")
        if not dros.exists():
            return
        url = f"{os.getenv('HOST_URL', '')}/services/occupancy-certificate/{certificate_id}"
        context = {
            "certificate": certificate,
            "permit": certificate.permit,
            "applicant": certificate.user,
            "building_number": certificate.building_number,
            "parent_certificate": certificate.parent_certificate,
            "url": url,
        }
        html_content = render_to_string("mailers/occupancy/renewal_requested.html", context)
        send_mail("Occupancy Certificate Renewal Request", html_content, [dro.email for dro in dros])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_renewal_requested")
