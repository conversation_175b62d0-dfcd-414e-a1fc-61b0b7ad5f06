from rest_framework import generics, viewsets, response, status
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django_fsm import can_proceed
from django_fsm_log.models import StateLog
from rest_framework.filters import SearchFilter
from rest_framework.decorators import action
from django.db import transaction
from django_weasyprint import WeasyTemplateResponseMixin
from rest_framework.exceptions import ValidationError
from datetime import timedelta
from common.helpers import adm_user

from occupancy.models import Certificate, InspectionReport, BuildingUnit
from occupancy.mailers import notify_renewal, notify_renewal_requested
from occupancy.serializers import (
    CertificateSerializer,
    CertificateListSerializer,
    InspectionReportSerializer,
    BuildingUnitSerializer,
)
from occupancy.populators import CertificatePopulator
from common.serializers import StateLogSerializer


class CertificateView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating occupancy certificates
    """

    serializer_class = CertificateSerializer
    filter_backends = [SearchFilter]
    search_fields = (
        "serial_no",
        "state",
        "user__first_name",
        "user__last_name",
        "user__email",
        "task_pools__user__first_name",
        "task_pools__user__last_name",
        "permit__serial_no",
    )

    def get_queryset(self):
        return CertificatePopulator(self.request.user, self.request.query_params).populate().distinct()

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                serializer.validated_data["user_id"] = self.request.user.id
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class CertificateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting occupancy certificates
    """

    queryset = Certificate.objects.all()
    serializer_class = CertificateSerializer


class CertificateTransitionView(viewsets.ViewSet):
    """
    API endpoint for transitioning occupancy certificates
    """

    @action(detail=True, methods=["put"])
    def transition(self, request, *args, **kwargs):
        with transaction.atomic():
            try:
                instance = get_object_or_404(Certificate, pk=kwargs.get("pk"))
                action = request.data.pop("action")
                if not hasattr(instance, action) or not can_proceed(getattr(instance, action)):
                    return response.Response(
                        {"error": f"Invalid transition, cannot change state from `{instance.state}` using `{action}`"}, status=status.HTTP_422_UNPROCESSABLE_ENTITY
                    )
                if getattr(instance, "can_" + action)(request.user):
                    if not action == "start" and instance.task_pools.filter(user=request.user, state="assigned").exists():
                        raise ValidationError({"error": "You need to start your task first."})
                    getattr(instance, action)(request.data, by=request.user)
                    instance.save()
                    serializer = CertificateSerializer(instance)
                    return response.Response(serializer.data, status=status.HTTP_200_OK)
                else:
                    return response.Response({"error": "You are not permitted to perform this action."}, status=status.HTTP_400_BAD_REQUEST)
            except ValidationError as e:
                transaction.set_rollback(True)
                return response.Response(e.detail, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                transaction.set_rollback(True)
                return response.Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["get"])
    def activity_logs(self, request, *args, **kwargs):
        instance = get_object_or_404(Certificate, pk=kwargs.get("pk"))
        logs = StateLog.objects.for_(instance)
        serializer = StateLogSerializer(logs, many=True)
        return response.Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["post"])
    def notify_renewal(self, request, *args, **kwargs):
        """
        Send notification for certificate renewal
        """
        with transaction.atomic():
            try:
                instance = get_object_or_404(Certificate, pk=kwargs.get("pk"))

                # Check if certificate is eligible for renewal
                if not instance.is_eligible_for_renewal():
                    return response.Response({"error": "This certificate is not eligible for renewal yet."}, status=status.HTTP_400_BAD_REQUEST)

                # Transition to renewal notification state
                if can_proceed(instance.notify_renewal):
                    instance.notify_renewal(request.data, by=request.user)
                    instance.save()

                    # Send email notification
                    notify_renewal.delay(instance.id)

                    serializer = CertificateSerializer(instance)
                    return response.Response(serializer.data, status=status.HTTP_200_OK)
                else:
                    return response.Response({"error": f"Cannot transition to renewal notification state from {instance.state}"}, status=status.HTTP_400_BAD_REQUEST)
            except ValidationError as e:
                transaction.set_rollback(True)
                return response.Response(e.detail, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                transaction.set_rollback(True)
                return response.Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class InspectionReportView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating inspection reports
    """

    serializer_class = InspectionReportSerializer
    filter_backends = [SearchFilter]
    search_fields = ("certificate__serial_no", "inspector__first_name", "inspector__last_name")

    def get_queryset(self):
        return InspectionReport.objects.all()

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                serializer.validated_data["inspector_id"] = self.request.user.id
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class InspectionReportDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting inspection reports
    """

    queryset = InspectionReport.objects.all()
    serializer_class = InspectionReportSerializer

    def perform_update(self, serializer):
        with transaction.atomic():
            try:
                super().perform_update(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class BuildingUnitView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating building units
    """

    serializer_class = BuildingUnitSerializer
    filter_backends = [SearchFilter]
    search_fields = ("unit_number", "certificate__serial_no")

    def get_queryset(self):
        return BuildingUnit.objects.all()

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class BuildingUnitDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting building units
    """

    queryset = BuildingUnit.objects.all()
    serializer_class = BuildingUnitSerializer

    def perform_update(self, serializer):
        with transaction.atomic():
            try:
                super().perform_update(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class CertificateRenewalView(generics.CreateAPIView):
    """
    API endpoint for requesting certificate renewals
    """

    def post(self, request, *args, **kwargs):
        with transaction.atomic():
            try:
                certificate_id = request.data.get("certificate_id")
                if not certificate_id:
                    return Response({"error": "Certificate ID is required"}, status=status.HTTP_400_BAD_REQUEST)
                try:
                    certificate = Certificate.objects.get(id=certificate_id)
                except Certificate.DoesNotExist:
                    return Response({"error": "Certificate not found"}, status=status.HTTP_404_NOT_FOUND)
                if not certificate.is_eligible_for_renewal():
                    return Response({"error": "This certificate is not eligible for renewal"}, status=status.HTTP_400_BAD_REQUEST)
                if not certificate.can_request_renewal(request.user):
                    return Response({"error": "You are not authorized to renew this certificate"}, status=status.HTTP_403_FORBIDDEN)
                renewal = certificate.create_renewal_certificate()
                notify_renewal_requested.delay(renewal.id)
                serialized_data = CertificateSerializer(renewal).data
                return Response(serialized_data, status=status.HTTP_201_CREATED)
            except Exception as e:
                transaction.set_rollback(True)
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CertificatePdfView(WeasyTemplateResponseMixin, generics.RetrieveAPIView):
    """
    API endpoint for generating PDF certificates
    """

    queryset = Certificate.objects.all()
    serializer_class = CertificateSerializer
    template_name = "documents/pdfs/occupancy/certificate.html"

    def get(self, request, *args, **kwargs):
        certificate = get_object_or_404(Certificate, pk=kwargs["pk"])
        if certificate.state != "approved":
            raise ValidationError({"error": "Certificate not approved."})
        finyear = certificate.created_at - timedelta(days=365)
        setting = adm_user(certificate).settings.filter(key="seal").first()
        seal = setting.file.file.url if setting else None
        context = {"certificate": certificate, "user": certificate.user, "finyear": finyear, "seal": seal, "building_units": certificate.building_units.all()}
        return self.render_to_response(context)
