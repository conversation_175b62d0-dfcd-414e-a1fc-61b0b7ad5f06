from django.test import TestCase
from django.utils import timezone
from datetime import timedelta
from unittest.mock import patch, Mock

from occupancy.populators import CertificatePopulator


class CertificatePopulatorTest(TestCase):
    """
    Test cases for the CertificatePopulator class
    """

    @patch("occupancy.populators.Certificate.objects.all")
    def test_filter_expiring_soon(self, mock_all):
        """Test filtering for certificates expiring soon"""
        # Set up mock user
        mock_user = Mock()
        mock_user.current_role = Mock(name="gadm")

        # Set up mock queryset
        mock_queryset = Mock()
        mock_all.return_value = mock_queryset
        mock_queryset.filter.return_value = mock_queryset

        # Create populator with expiring_soon=true
        populator = CertificatePopulator(mock_user, {"expiring_soon": "true"})

        # Call populate method
        populator.populate()

        # Verify that the correct filter was applied
        mock_queryset.filter.assert_any_call(state__in=["approved", "closed"], valid_until__isnull=False, valid_until__lte=timezone.now().date() + timedelta(days=90))

    @patch("occupancy.populators.Certificate.objects.all")
    def test_filter_eligible_for_renewal(self, mock_all):
        """Test filtering for certificates eligible for renewal"""
        # Set up mock user
        mock_user = Mock()
        mock_user.current_role = Mock(name="gadm")

        # Set up mock queryset
        mock_queryset = Mock()
        mock_all.return_value = mock_queryset
        mock_queryset.filter.return_value = mock_queryset

        # Create populator with eligible_for_renewal=true
        populator = CertificatePopulator(mock_user, {"eligible_for_renewal": "true"})

        # Call populate method
        populator.populate()

        # Verify that the correct filter was applied
        mock_queryset.filter.assert_any_call(state__in=["approved", "closed"], valid_until__isnull=False, valid_until__lte=timezone.now().date() + timedelta(days=90))
