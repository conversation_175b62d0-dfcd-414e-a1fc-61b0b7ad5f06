import json
from django.urls import reverse, path, include
from django.utils import timezone
from django.core.management import call_command
from datetime import timedelta
from rest_framework import status
from rest_framework.test import URLPatternsTestCase
from unittest.mock import patch

from occupancy.models import Certificate, InspectionReport
from occupancy.enums import CertificateStatus, CertificateType, InspectionReportStatus, IssueType
from user.models import Role
from user.factories import UserFactory, ProfileFactory
from building.factories import PermitFactory
from address.factories import DzongkhagFactory, GewogFactory, ThromdeFactory, VillageFactory
from planning.factories import ApplicationFactory, ApplicantFactory
from file.factories import AttachmentFactory


from common.tests import BaseTest


class CertificateRenewalTest(BaseTest, URLPatternsTestCase):
    """
    Test case for the complete certificate renewal process
    """

    urlpatterns = [
        path("api/v1/", include("user.urls")),
        path("api/v1/", include("occupancy.urls")),
        path("api/v1/", include("payment.urls")),
    ]

    def setUp(self):
        """Set up test data"""
        # Load roles first
        call_command("loaddata", "seed/000_role.json")

        # Create the user first for BaseTest
        self.user = UserFactory()

        # Call BaseTest's setUp which sets up request context
        super().setUp()

        # Now set up the user roles
        self.user.roles.add(Role.objects.get(name="user"))
        self.user.current_role = Role.objects.get(name="user")
        self.user.save()

        # Create locations
        self.dzo = DzongkhagFactory()
        self.gewog = GewogFactory(dzongkhag=self.dzo)
        self.village = VillageFactory(gewog=self.gewog)
        self.thromde = ThromdeFactory(dzongkhag=self.dzo)

        # Set up profile for the user (applicant)
        ProfileFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog)

        # Create DRO (using Dzongkhag Admin role)
        self.dro = UserFactory()
        self.dro.roles.add(Role.objects.get(name="dadm"))
        self.dro.current_role = Role.objects.get(name="dadm")
        self.dro.save()
        ProfileFactory(user=self.dro, dzongkhag=self.dzo)

        # Create Chief Engineer
        self.chief = UserFactory()
        self.chief.roles.add(Role.objects.get(name="dce"))
        self.chief.current_role = Role.objects.get(name="dce")
        self.chief.save()
        ProfileFactory(user=self.chief, dzongkhag=self.dzo)

        # Create Building Inspector
        self.inspector = UserFactory()
        self.inspector.roles.add(Role.objects.get(name="binsp"))
        self.inspector.current_role = Role.objects.get(name="binsp")
        self.inspector.save()
        ProfileFactory(user=self.inspector, dzongkhag=self.dzo)

        # Create attachments for application
        self.land_certificate = AttachmentFactory()
        self.site_condition_plan = AttachmentFactory()
        self.construction_clearance = AttachmentFactory()

        # Create application and permit with patched signals to avoid issues
        with patch.object(Certificate, "save", return_value=None):
            with patch("planning.signals.post_save", return_value=None):
                # Create application and permit
                self.application = ApplicationFactory(
                    user=self.user,
                    dzongkhag=self.dzo,
                    gewog=self.gewog,
                    village=self.village,
                    land_certificate=self.land_certificate,
                    site_condition_plan=self.site_condition_plan,
                    construction_clearance=self.construction_clearance,
                )
                self.applicant_details = ApplicantFactory(application=self.application)

                # Create permit with state="closed" to avoid transition issues
                self.permit = PermitFactory(user=self.user, application=self.application, dzongkhag=self.dzo, gewog=self.gewog, village=self.village, state="closed")

        # Create an original certificate that is eligible for renewal
        today = timezone.now().date()
        self.original_certificate = Certificate.objects.create(
            permit=self.permit,
            user=self.user,
            state=CertificateStatus.APPROVED,
            certificate_type=CertificateType.NEW,
            valid_from=today - timedelta(days=365 * 5 - 60),  # Almost 5 years old
            valid_until=today + timedelta(days=60),  # Expires in 2 months
            certificate_number="CERT-12345678",
            building_number="BLD-12345678",
            building_qr_code="qr-code-data",
            certificate_date=today - timedelta(days=365 * 5 - 60),
        )

        # Generate tokens for authentication
        self.applicant_token = self.get_token(self.user)
        self.dro_token = self.get_token(self.dro)
        self.chief_token = self.get_token(self.chief)
        self.inspector_token = self.get_token(self.inspector)

        # Mock payment service
        self.payment_patcher = patch("cas_api.services.birms_service.generate_payment_advice")
        self.mock_payment = self.payment_patcher.start()
        self.mock_payment.return_value = {"payment_id": "PAY123456", "status": "success"}
        self.addCleanup(self.payment_patcher.stop)

    def get_token(self, user):
        """Get authentication token for a user"""
        url = reverse("token_obtain_pair")
        data = {"username": user.email, "password": "Dcpl@123"}
        response = self.client.post(url, data, format="json")
        return response.data["data"]["access"]

    def test_complete_renewal_process(self):
        """Test the complete certificate renewal process"""
        # Step 1: Notify renewal (by Dzongkhag Admin acting as DRO)
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.dro_token}"
        url = reverse("certificate_notify_renewal", kwargs={"pk": self.original_certificate.id})
        data = {"remarks": "Your certificate is expiring soon. Please renew."}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["state"], CertificateStatus.RENEWAL_NOTIFICATION)

        # Step 2: Applicant requests renewal
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.applicant_token}"
        url = reverse("certificate_renewals")
        data = {"certificate_id": self.original_certificate.id, "remarks": "Requesting renewal of my occupancy certificate"}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        renewal_id = response.data["data"]["id"]

        # Get the renewal certificate
        url = reverse("certificate_detail", kwargs={"pk": renewal_id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["state"], CertificateStatus.REQUESTED)
        self.assertEqual(response.data["data"]["certificate_type"], CertificateType.RENEWAL)
        self.assertEqual(response.data["data"]["parent_certificate_id"], self.original_certificate.id)

        # Step 3: Dzongkhag Admin (acting as DRO) assigns inspector
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.dro_token}"
        url = reverse("certificate_transition", kwargs={"pk": renewal_id})
        data = {"action": "assign_inspector", "inspector_id": self.inspector.id, "remarks": "Please inspect this property for renewal"}
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["state"], CertificateStatus.INSPECTOR_ASSIGNED)

        # Step 4: Inspector schedules inspection
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.inspector_token}"
        url = reverse("certificate_transition", kwargs={"pk": renewal_id})
        data = {
            "action": "schedule",
            "scheduled_date": (timezone.now().date() + timedelta(days=7)).isoformat(),
            "schedule_remarks": "Will visit next week",
            "remarks": "Inspection scheduled for next week",
        }
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["state"], CertificateStatus.SCHEDULED)

        # Step 5: Inspector creates inspection report
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.inspector_token}"
        url = reverse("inspection_reports")
        data = {
            "certificate_id": renewal_id,
            "issue_type": IssueType.NO_ISSUE,
            "remarks": "Building is in good condition",
            "recommendations": "Approve renewal",
            "status": InspectionReportStatus.APPROVED,
            "site_visit_date": timezone.now().date().isoformat(),
            "site_visit_time": "10:00:00",
            "site_visit_location": "Building site",
            "illegal_structures": False,
            "setback_deviations": False,
            "architectural_deviations": False,
            "basement_parking_issues": False,
            "safety_grills_issues": False,
            "debris_cleared": True,
            "septic_drainage_issues": False,
            "property_damages": False,
            "certified_work": True,
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        inspection_id = response.data["data"]["id"]

        # Step 6: Inspector marks as inspected
        url = reverse("certificate_transition", kwargs={"pk": renewal_id})
        data = {"action": "inspect", "inspection_report_id": inspection_id, "remarks": "Inspection completed, no issues found"}
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["state"], CertificateStatus.INSPECTED)

        # Step 7: Inspector forwards to chief
        url = reverse("certificate_transition", kwargs={"pk": renewal_id})
        data = {"action": "forward_to_chief", "remarks": "Forwarding to chief for approval"}
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["state"], CertificateStatus.FORWARDED_TO_CHIEF)

        # Step 8: Chief requires payment
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.chief_token}"
        url = reverse("certificate_transition", kwargs={"pk": renewal_id})
        data = {"action": "payment", "amount": 1000, "remarks": "Payment required for certificate renewal"}
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["state"], CertificateStatus.PENDING_PAYMENT)

        # Step 9: Mock payment completion
        # In a real scenario, this would be handled by a webhook or callback
        # Here we'll directly transition to the next state
        url = reverse("certificate_transition", kwargs={"pk": renewal_id})
        data = {"action": "update_building_details", "remarks": "Payment completed, update building details"}
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["state"], CertificateStatus.BUILDING_DETAILS_UPDATE)

        # Step 10: Chief approves the certificate
        url = reverse("certificate_transition", kwargs={"pk": renewal_id})
        data = {"action": "approve", "certificate_date": timezone.now().date().isoformat(), "remarks": "Certificate renewal approved"}
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["state"], CertificateStatus.APPROVED)

        # Step 11: Generate PDF certificate
        url = reverse("certificate_pdf", kwargs={"pk": renewal_id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "application/pdf")

        # Step 12: Close the certificate
        url = reverse("certificate_transition", kwargs={"pk": renewal_id})
        data = {"action": "close", "remarks": "Certificate renewal process completed"}
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["state"], CertificateStatus.CLOSED)

        # Verify the final state of the renewal certificate
        url = reverse("certificate_detail", kwargs={"pk": renewal_id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.applicant_token}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["state"], CertificateStatus.CLOSED)
        self.assertEqual(response.data["data"]["certificate_type"], CertificateType.RENEWAL)
        self.assertEqual(response.data["data"]["parent_certificate_id"], self.original_certificate.id)
