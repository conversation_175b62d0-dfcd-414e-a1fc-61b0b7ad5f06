from django.db.models.signals import post_save
from django.dispatch import receiver
from django_fsm.signals import post_transition
from rest_framework.exceptions import ValidationError
from django.utils.timezone import now
from notifications.signals import notify

from occupancy.models import Certificate
from occupancy.enums import CertificateStatus
from common.models import TaskPool
from common.helpers import determine_assignee_user, generate_payment
from occupancy.mailers import (
    notify_requested,
    notify_inspector_assigned,
    notify_scheduled,
    notify_inspected,
    notify_changes_required,
    notify_forwarded_to_chief,
    notify_payment_required,
    notify_penalty_payment_required,
    notify_approved,
    notify_rejected,
    notify_renewal,
    notify_renewal_requested,
)
from cas_api.middleware.current_request import get_current_request


def create_notification(instance, recipient, verb):
    """
    Create a notification for the user
    """
    notify.send(instance.by or instance.user, recipient=recipient, verb=verb, action_object=instance, target=instance.permit, url=f"/services/occupancy-certificate/{instance.id}")


@receiver(post_save, sender=Certificate)
def certificate_post_save(sender, instance, created, **kwargs):
    """
    Signal handler for OccupancyCertificate post_save
    """
    by = getattr(get_current_request(), "user", None)
    if created:
        if instance.permit and instance.permit.construction_id:
            instance.construction_id = instance.permit.construction_id
        # Initialize the certificate
        instance.initial(by=instance.user)
        instance.save()
        # Generate serial number
        instance.generate_serial_no()
        if instance.nature == "urban":
            chief_user, chief_role = determine_assignee_user("hoi", dzo=instance.dzongkhag, thrm=instance.thromde)
        else:
            chief_user, chief_role = determine_assignee_user("dro", dzo=instance.dzongkhag)
        instance.task_pools.create(user=chief_user, role=chief_role, by=by or instance.user, by_role=getattr(by, "current_role", None))
        # Notify chief
        create_notification(instance, chief_user, "requested")
        notify_requested.delay(instance.id, chief_user.id)


@receiver(post_transition, sender=Certificate)
def certificate_post_transition(sender, instance, name, source, target, **kwargs):
    """
    Signal handler for Certificate post_transition
    """
    by = getattr(get_current_request(), "user", None)
    if not name in ["payment", "change", "initial", "resubmitted"]:
        task = instance.task_pools.filter(user_id=getattr(by, "id", None), state="in_progress").first()
        if task:
            task.approve({}, by=by)
            task.save()
    if name == "assign_inspector":
        inspector_id = getattr(instance, "data", {}).get("inspector_id")
        if not inspector_id:
            raise ValidationError({"error": "Inspector ID is required"})
        # Try to find user with building inspector role first, if not found try me role
        if instance.nature == "urban":
            user, role = determine_assignee_user("tbi", user_id=inspector_id, dzo=instance.dzongkhag, thrm=instance.thromde)
        else:
            try:
                user, role = determine_assignee_user("dbi", user_id=inspector_id, dzo=instance.dzongkhag)
            except ValidationError:
                user, role = determine_assignee_user("dro", user_id=inspector_id, dzo=instance.dzongkhag)
        instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
        create_notification(instance, user, "assigned")
        notify_inspector_assigned.delay(instance.id, user.id)
    elif name == "schedule":
        create_notification(instance, instance.user, "scheduled")
        notify_scheduled.delay(instance.id)
    elif name == "inspect":
        create_notification(instance, instance.user, "inspected")
        notify_inspected.delay(instance.id)
    elif name == "require_changes":
        create_notification(instance, instance.user, "changes required")
        notify_changes_required.delay(instance.id)
    elif name == "forward_to_chief":
        if instance.permit.nature == "urban":
            chief_user, chief_role = determine_assignee_user("tce", dzo=instance.permit.dzongkhag, thrm=instance.permit.thromde)
        else:
            chief_user, chief_role = determine_assignee_user("dce", dzo=instance.permit.dzongkhag)
        instance.task_pools.create(user=chief_user, role=chief_role, by=by, by_role=getattr(by, "current_role", None))
        create_notification(instance, chief_user, "forwarded")
        notify_forwarded_to_chief.delay(instance.id, chief_user.id)
    elif name == "payment":
        data = instance.data or {}
        if data["amount"] and float(data["amount"]) >= 0:
            generate_payment("oc", data["amount"], instance)
            create_notification(instance, instance.user, "payment required")
            notify_payment_required.delay(instance.id)
        else:
            instance.pay()
    elif name == "penalty_payment":
        if data["penalty_amount"] and float(data["penalty_amount"]) >= 0:
            generate_payment("oc", data["penalty_amount"], instance)
            create_notification(instance, instance.user, "penalty payment required")
            notify_penalty_payment_required.delay(instance.id)
        else:
            instance.pay()
    elif name == "update_building_details":
        # Assign building inspector to update building details
        role_names = ["tbi"] if instance.nature == "urban" else ["dbi", "dro"]
        bi_task = instance.task_pools.filter(role__name__in=role_names).order_by("-created_at").first()
        if bi_task:
            user = bi_task.user
            role = bi_task.role
            instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
            create_notification(instance, user, "building details update required")
    elif name == "approve":
        instance.generate_certificate_number()
        if not instance.certificate_date:
            instance.certificate_date = now().date()
            instance.save(update_fields=["certificate_date"])
        data = instance.data or {}
        if data:
            instance.building_number = data.get("building_number")
            instance.building_qr_code = data.get("building_qr_code")
            instance.save(update_fields=["building_number", "building_qr_code"])
            units_data = data.get("building_units", [])
            for unit_data in units_data:
                instance.building_units.create(**unit_data)
        create_notification(instance, instance.user, "approved")
        notify_approved.delay(instance.id)
    elif name == "reject":
        create_notification(instance, instance.user, "rejected")
        notify_rejected.delay(instance.id)


@receiver(post_save, sender=Certificate)
def certificate_validity_post_save(sender, instance, created, **kwargs):
    """
    Signal handler for Certificate post_save to handle validity period
    """
    # For approved certificates, set validity period if not already set
    if instance.state == CertificateStatus.APPROVED and not instance.valid_until:
        instance.set_validity_period()


@receiver(post_transition, sender=Certificate)
def certificate_renewal_post_transition(sender, instance, name, source, target, **kwargs):
    """
    Signal handler for Certificate renewal-related transitions
    """
    by = getattr(get_current_request(), "user", None)

    # Handle renewal-specific transitions
    if name == "notify_renewal":
        # Send notification to the certificate owner
        create_notification(instance, instance.user, "renewal notification")
        notify_renewal.delay(instance.id)
    elif name == "request_renewal":
        # Create a renewal certificate
        renewal = instance.create_renewal_certificate()
        renewal.initial(by=instance.user)
        renewal.save()

        # Notify DRO about the renewal request
        notify_renewal_requested.delay(renewal.id)
    elif name == "request_renewal_inspection":
        # Find DRO to assign inspector
        dro_user, dro_role = determine_assignee_user("dro", dzo=instance.dzongkhag)
        instance.task_pools.create(user=dro_user, role=dro_role, by=by or instance.user, by_role=getattr(by, "current_role", None))
        create_notification(instance, dro_user, "renewal inspection requested")


@receiver(post_transition, sender=TaskPool)
def task_pool_transition(sender, instance, name, source, target, **kwargs):
    """
    Signal handler for TaskPool post_transition
    """
    if not isinstance(instance.poolable, Certificate):
        return

    certificate = instance.poolable
    by = getattr(get_current_request(), "user", None)
    if name == "change":
        if certificate.state == CertificateStatus.INSPECTED:
            certificate.require_changes(
                {
                    "changes_required": instance.data.get("changes_required"),
                    "penalty_amount": instance.data.get("penalty_amount"),
                    "penalty_remarks": instance.data.get("penalty_remarks"),
                },
                by=by,
            )
            certificate.save()
    elif name == "reject":
        certificate.reject({"remarks": instance.data.get("remarks")}, by=by)
        certificate.save()
