from django.template.loader import render_to_string
from cas_api.celery import app
from modification.models import Permit
import os
from cas_api.services.mailer_service import send_mail
from user.models import User
from django.shortcuts import get_object_or_404
from django_fsm_log.models import StateLog


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_requested(self, permit_id, user_id):
    """
    Send email notification when a new modification permit is requested
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        html_content = render_to_string(
            "mailers/modification/requested.html",
            {"name": user.name, "url": url, "serial_no": permit.serial_no, "permit": permit, "user": user},
        )
        send_mail("New Modification Permit Request", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_requested")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forwarded_to_roid(self, permit_id, user_id):
    """
    Send email notification when a modification permit is forwarded to ROID
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        html_content = render_to_string(
            "mailers/modification/forwarded_to_roid.html",
            {"name": user.name, "url": url, "serial_no": permit.serial_no, "permit": permit, "user": user},
        )
        send_mail("Modification Permit Forwarded to ROID", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_forwarded_to_roid")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forwarded_to_dhs(self, permit_id, user_id):
    """
    Send email notification when a modification permit is forwarded to DHS
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        html_content = render_to_string(
            "mailers/modification/forwarded_to_dhs.html",
            {"name": user.name, "url": url, "serial_no": permit.serial_no, "permit": permit, "user": user},
        )
        send_mail("Modification Permit Forwarded to DHS", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_forwarded_to_dhs")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_architect_assigned(self, permit_id, user_id):
    """
    Send email notification when an architect is assigned to a modification permit
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        html_content = render_to_string(
            "mailers/modification/architect_assigned.html",
            {"name": user.name, "url": url, "serial_no": permit.serial_no, "permit": permit, "user": user},
        )
        send_mail("Architect Assigned to Modification Permit", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_architect_assigned")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_resubmitted(self, permit_id, user_id):
    """
    Send email notification when a modification permit is resubmitted
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        html_content = render_to_string(
            "mailers/modification/resubmitted.html",
            {"name": user.name, "url": url, "serial_no": permit.serial_no, "permit": permit, "user": user},
        )
        send_mail("Modification Permit Resubmitted", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_resubmitted")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_change(self, permit_id):
    """
    Send email notification when changes are requested for a modification permit
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        logs = StateLog.objects.for_(permit)
        html_content = render_to_string(
            "mailers/modification/change_requested.html",
            {"name": permit.user.name, "url": url, "serial_no": permit.serial_no, "permit": permit, "reason": getattr(logs.last(), "description", "")},
        )
        send_mail("Changes Requested for Modification Permit", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_change")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_rejected(self, permit_id):
    """
    Send email notification when a modification permit is rejected
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        logs = StateLog.objects.for_(permit)
        html_content = render_to_string(
            "mailers/modification/rejected.html",
            {"name": permit.user.name, "url": url, "serial_no": permit.serial_no, "permit": permit, "reason": getattr(logs.last(), "description", "")},
        )
        send_mail("Modification Permit Rejected", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_rejected")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_approved(self, permit_id):
    """
    Send email notification when a modification permit is approved
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        html_content = render_to_string(
            "mailers/modification/approved.html",
            {"name": permit.user.name, "url": url, "serial_no": permit.serial_no, "permit": permit},
        )
        send_mail("Modification Permit Approved", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_approved")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_closed(self, permit_id):
    """
    Send email notification when a modification permit is closed
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        html_content = render_to_string(
            "mailers/modification/closed.html",
            {"name": permit.user.name, "url": url, "serial_no": permit.serial_no, "permit": permit},
        )
        send_mail("Modification Permit Closed", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_closed")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_payment(self, permit_id):
    """
    Send email notification when payment is required for a modification permit
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        html_content = render_to_string(
            "mailers/modification/payment.html",
            {"name": permit.user.name, "url": url, "serial_no": permit.serial_no, "permit": permit, "fee": permit.fee},
        )
        send_mail("Payment Required for Modification Permit", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_payment")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forward_to_bpc(self, permit_id, user_id):
    """
    Send email notification when a modification permit is forwarded to BPC
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        html_content = render_to_string(
            "mailers/modification/bpc_forwarded.html",
            {"name": user.name, "url": url, "serial_no": permit.serial_no, "permit": permit, "user": user},
        )
        send_mail("Modification Permit Forwarded to BPC", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_forward_to_bpc")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_bpc_response(self, permit_id):
    """
    Send email notification when BPC responds to a modification permit
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        html_content = render_to_string(
            "mailers/modification/bpc_response.html",
            {"name": permit.user.name, "url": url, "serial_no": permit.serial_no, "permit": permit},
        )
        send_mail("BPC Response for Modification Permit", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_bpc_response")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_task_pool_approved(self, permit_id, user_id):
    """
    Send email notification when a task pool is approved for a modification permit
    """
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-modification/{permit_id}"
        html_content = render_to_string(
            "mailers/modification/task_pool_approved.html",
            {"name": user.name, "url": url, "serial_no": permit.serial_no, "permit": permit, "user": user},
        )
        send_mail("Task Approved for Modification Permit", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_task_pool_approved")
