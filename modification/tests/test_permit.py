import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase
from django.urls import path, include
from django.core.management import call_command
from user.factories import UserFactory, ProfileFactory
from address.factories import DzongkhagFactory, GewogFactory, ThromdeFactory, VillageFactory
from user.models import Role, User
from modification.models import Permit
from modification.enums import ModificationType, Purpose


class PermitTest(APITestCase, URLPatternsTestCase):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("modification.urls")),
        path("api/v1/", include("common.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.dzo = DzongkhagFactory(name="Mongar")
        self.gewog = GewogFactory(name="Mongar", dzongkhag=self.dzo)
        self.thromde = ThromdeFactory(name="Mongar", dzongkhag=self.dzo)
        self.village = VillageFactory(name="Mongar", gewog=self.gewog)

        # Create regular user
        self.user = UserFactory(username="user", roles=Role.objects.filter(name__in=["user"]).values_list("id", flat=True))
        ProfileFactory(user=self.user, dzongkhag=self.dzo)

        # Get token
        res = self.client.post(reverse("token_obtain_pair"), {"username": self.user.username, "password": "Dcpl@123"})
        self.token = json.loads(res.content)["data"]["access"]

        self.permit_data = {
            "dzongkhag_id": self.dzo.id,
            "gewog_id": self.gewog.id,
            "modification_type": ModificationType.DZONGKHAG,
            "purpose": Purpose.EXTENSION,
        }

    def test_create_permit(self):
        url = reverse("permits")
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"

        # Add state to make it a draft first
        data = self.permit_data.copy()
        data["state"] = "draft"

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Permit.objects.count(), 1)

        # In test mode, the signal is skipped, so state remains draft
        permit = Permit.objects.first()
        self.assertEqual(permit.state, "draft")

    def test_list_permits(self):
        url = reverse("permits")
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_retrieve_permit(self):
        # Create a permit through the API instead of directly
        url = reverse("permits")
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.post(url, self.permit_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Now retrieve it
        permit_id = response.data["id"]
        url = reverse("permits", kwargs={"pk": permit_id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], permit_id)
