import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase
from django.urls import path, include
from common.tests import BaseTest
from modification.models import Modification
from modification.enums import ModificationType, Purpose


class ModificationTest(BaseTest):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("modification.urls")),
        path("api/v1/", include("common.urls")),
    ]

    def setUp(self):
        super().setUp()
        self.modification_data = {
            "dzongkhag_id": self.dzo.id,
            "gewog_id": self.gewog.id,
            "modification_type": ModificationType.DZONGKHAG,
            "purpose": Purpose.EXTENSION,
        }

    def test_create_modification(self):
        url = reverse("modifications")
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.post(url, self.modification_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Modification.objects.count(), 1)
        self.assertEqual(Modification.objects.get().state, "requested")

    def test_list_modifications(self):
        url = reverse("modifications")
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_retrieve_modification(self):
        modification = Modification.objects.create(
            user=self.user,
            dzongkhag=self.dzo,
            gewog=self.gewog,
            modification_type=ModificationType.DZONGKHAG,
            purpose=Purpose.EXTENSION,
        )
        url = reverse("modifications", kwargs={"pk": modification.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], modification.id)
