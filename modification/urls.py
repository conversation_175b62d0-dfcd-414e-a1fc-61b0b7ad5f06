from django.urls import path
from modification import views

urlpatterns = [
    path("permits/", views.PermitView.as_view(), name="permits"),
    path("permits/<pk>/", views.PermitPKView.as_view(), name="permits"),
    path("permits/<pk>/transition/", views.PermitViewSet.as_view({"put": "transition"}), name="transitions"),
    path("permits/<pk>/activity_logs/", views.PermitViewSet.as_view({"get": "activity_logs"}), name="activity_logs"),
    path("permits/<permit_id>/certificate/", views.PdfView.as_view(), name="certificate"),
    path("permits/draft", views.PermitViewSet.as_view({"get": "draft"}), name="draft"),
]
