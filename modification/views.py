from rest_framework.filters import SearchFilter
from rest_framework import generics, viewsets, response, status
from django.shortcuts import get_object_or_404
from django_fsm import can_proceed
from django.db import transaction
from rest_framework.exceptions import ValidationError
from datetime import timedelta
from django_weasyprint import WeasyTemplateResponseMixin
from common.serializers import StateLogSerializer
from modification.helpers.view_helper import perform_save
from modification.models import Permit
from modification.populators import Populator
from modification.serializers import PermitSerializer
from common.models import DesignTeam
from common.helpers import adm_user
from rest_framework.decorators import action
from django_fsm_log.models import StateLog


class PermitView(generics.ListCreateAPIView):
    serializer_class = PermitSerializer
    filter_backends = [SearchFilter]
    search_fields = (
        "serial_no",
        "state",
        "user__first_name",
        "user__last_name",
        "user__email",
        "dzongkhag__name",
        "thromde__name",
        "gewog__name",
        "village__name",
    )

    def get_queryset(self):
        return Populator(self.request.user, self.request.query_params).populate().distinct()

    def create(self, request, *args, **kwargs):
        data = request.data
        if data.get("state") == "draft":
            design_teams = data.pop("design_teams", [])
            permit_data = Permit(**data, user=request.user)
            permit = Permit.objects.bulk_create([permit_data])[0]
            instances = []
            for design_team in design_teams:
                instances.append(DesignTeam(teamable=permit, **design_team))
            DesignTeam.objects.bulk_create(instances)
            return response.Response(PermitSerializer(permit).data, status=status.HTTP_201_CREATED)
        return super().create(request, *args, **kwargs)

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                perform_save(self, serializer)
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class PermitPKView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = PermitSerializer
    queryset = Permit.objects.all()


class PermitViewSet(viewsets.ViewSet):

    @action(detail=True, methods=["put"])
    def transition(self, request, *args, **kwargs):
        with transaction.atomic():
            try:
                instance = get_object_or_404(Permit, pk=kwargs.get("pk"))
                action = request.data.pop("action")
                if not hasattr(instance, action) or not can_proceed(getattr(instance, action)):
                    return response.Response({"error": "Invalid transition"}, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
                if getattr(instance, "can_" + action)(request.user):
                    if not action == "start" and instance.task_pools.filter(user=request.user, state="assigned").exists():
                        raise ValidationError({"error": "You need to start your task first."})
                    getattr(instance, action)(request.data, by=request.user)
                    instance.save()
                    serializer = PermitSerializer(instance)
                    return response.Response(serializer.data, status=status.HTTP_200_OK)
                else:
                    return response.Response({"error": "You are not permited to perform this action."}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                transaction.set_rollback(True)
                raise e

    @action(detail=True, methods=["get"])
    def activity_logs(self, request, **kwargs):
        inspection = get_object_or_404(Permit, pk=kwargs.get("pk"))
        logs = StateLog.objects.for_(inspection)
        serializer = StateLogSerializer(logs, many=True)
        return response.Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"])
    def draft(self, request):
        permits = Permit.objects.filter(user=request.user, state="draft")
        return response.Response(PermitSerializer(permits, many=True).data)


class PdfView(WeasyTemplateResponseMixin, generics.RetrieveAPIView):
    queryset = Permit.objects.all()
    serializer_class = PermitSerializer
    template_name = "documents/pdfs/modifications/certificate.html"

    def get(self, request, *args, **kwargs):
        permit = get_object_or_404(Permit, pk=kwargs["permit_id"])
        if permit.state != "approved":
            raise ValidationError({"error": "Application not approved."})
        finyear = permit.created_at - timedelta(days=365)
        setting = adm_user(permit).settings.filter(key="seal").first()
        seal = setting.file.file.url if setting else None
        context = {"permit": permit, "user": permit.user, "finyear": finyear, "seal": seal}
        return self.render_to_response(context)
