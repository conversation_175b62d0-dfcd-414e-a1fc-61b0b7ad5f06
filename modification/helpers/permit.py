from modification.enums import ModificationFlowStage, Purpose


class PermitHelper:
    @property
    def data(self):
        return getattr(self, "_data", {})

    @property
    def is_simple_modification(self):
        return self.purpose in [Purpose.USE_TYPE_CHANGE]

    @property
    def next_flow_stage(self):
        """Determine the next stage in the flow based on current stage and modification type."""
        if self.is_simple_modification:
            return ModificationFlowStage.PLANNING_PERMIT

        stages = list(ModificationFlowStage.choices)
        current_index = stages.index((self.flow_stage, self.get_flow_stage_display()))

        if current_index < len(stages) - 1:
            return stages[current_index + 1][0]
        return self.flow_stage

    @data.setter
    def data(self, value):
        self._data = value

    def can_forward_to_roid(self, current_user):
        current_role = current_user.current_role
        if current_role.name == "dce" and self.nature == "rural":
            return True
        else:
            return False

    def can_forward_to_dhs(self, current_user):
        current_role = current_user.current_role
        if current_role.name in ["dce", "roidce"]:
            return True
        else:
            return False

    def can_assign_architect(self, current_user):
        current_role = current_user.current_role
        if current_role.name in ["dce", "tce", "roidce", "dhsce"]:
            return True
        else:
            return False

    def can_reject(self, current_user):
        return True

    def can_approve(self, current_user):
        return True

    def can_close(self, current_user):
        return True

    def can_change(self, current_user):
        return True

    def pay(self, payment=None):
        self.approve({}, by=self.user)
        self.save()
