import sys
from django.dispatch import receiver
from django.db.models.signals import post_save
from django_fsm.signals import post_transition
from modification.models import Permit
from rest_framework.exceptions import ValidationError
from notifications.signals import notify
from common.helpers import determine_assignee_user, generate_payment
from cas_api.middleware.current_request import get_current_request
from django.db import transaction
from modification.mailers import (
    notify_forwarded_to_roid,
    notify_forwarded_to_dhs,
    notify_requested,
    notify_resubmitted,
    notify_closed,
    notify_rejected,
    notify_approved,
    notify_architect_assigned,
    notify_payment,
    notify_change,
)
from user.models import User
from common.constants import ARCHITECTS


@receiver(post_save, sender=Permit)
def auto_assign_reviewer(sender, instance, created, **kwargs):
    permit = instance
    by = getattr(get_current_request(), "user", None)
    if instance.planning_permit and instance.planning_permit.construction_id:
        instance.construction_id = instance.planning_permit.construction_id
    if created:
        instance.initial(by=instance.user)
        instance.save()
        create_notification(instance, "assigned")
        if permit.nature == "urban":
            user, role = determine_assignee_user("tce", dzo=permit.dzongkhag, thrm=permit.thromde)
        else:
            if permit.region_id:
                user, role = determine_assignee_user("roidce", rgn=permit.region)
            else:
                user, role = determine_assignee_user("dce", dzo=permit.dzongkhag)
        instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
        notify_requested.delay(permit.id, user.id)
    elif not created:
        if getattr(by, "id", None) == instance.user_id and permit.state == "pending_change":
            permit.resubmit({}, by=permit.user)
            permit.save()
            pending_change_tasks = permit.task_pools.filter(state="pending_change")
            for task in pending_change_tasks:
                task.assign({}, by=task.user)
                task.save()
                notify_resubmitted.delay(permit.id, task.user.id)


@receiver(post_transition, sender=Permit)
def after_order_transition(sender, instance, name, source, target, **kwargs):
    with transaction.atomic():
        by = getattr(get_current_request(), "user", None)
        if name not in ["initial", "change", "payment", "approve", "reject", "close"]:
            task = instance.task_pools.filter(user=by, state="in_progress").first()
            if task:
                task.approve({}, by=by)
                task.save()
        if name == "approve":
            notify_approved.delay(instance.id)
        elif name == "reject":
            notify_rejected.delay(instance.id)
        elif name == "payment":
            generate_payment("mob", instance.fee, instance)
            notify_payment.delay(instance.id)
        elif name == "forward_to_roid":
            user, role = determine_assignee_user("roidce", rgn=instance.region)
            instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
            notify_forwarded_to_roid.delay(instance.id, user.id)
        elif name == "forward_to_dhs":
            user, role = determine_assignee_user("dhsce")
            instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
            notify_forwarded_to_dhs.delay(instance.id, user.id)
        elif name == "close":
            notify_closed.delay(instance.id)
        elif name == "assign_architect":
            user = User.objects.get(id=instance.architect_id)
            user_roles = [role.name for role in user.roles.all()]
            role = user.roles.filter(name__in=ARCHITECTS).first()
            if not user:
                msg = f"No '{user.current_role.description}' user found in {instance.dzongkhag.name}"
                raise ValidationError({"error": msg})
            elif not any(x in ARCHITECTS for x in user_roles):
                raise ValidationError({"error": "Please select correct architect."})
            instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
            notify_architect_assigned.delay(instance.id, user.id)
        elif name == "change":
            notify_change.delay(instance.id)
        create_notification(instance, target)


def create_notification(instance, action):
    url = f"/services/building-modification/{instance.id}"
    notify.send(instance.by, recipient=instance.user, verb=action, action_object=instance, target=instance, url=url)
    notify.send(instance.by, recipient=instance.by, verb=action, action_object=instance, target=instance, url=url)
