from rest_framework import serializers
from common.models import BuildingType, ConstructionType
from modification.models import Permit
from drf_writable_nested.serializers import WritableNestedModelSerializer
from common.serializers import DesignTeamSearializer
from payment.serializers import PaymentSerializer
from common.serializers import SlabSerializer
from building.models import Permit as BuildingPermit
from information.models import Inquiry
from design.models import Permit as DesignPermit
from planning.models import Application


class PermitSerializer(WritableNestedModelSerializer):
    design_teams = DesignTeamSearializer(many=True, required=False)
    payments = PaymentSerializer(many=True, read_only=True)
    slabs = SlabSerializer(many=True, read_only=True)
    planning_information_id = serializers.PrimaryKeyRelatedField(queryset=Inquiry.objects.all(), source="planning_information", required=False, allow_null=True)
    planning_permit_id = serializers.PrimaryKeyRelatedField(queryset=Application.objects.all(), source="planning_permit", required=False, allow_null=True)
    design_permit_id = serializers.PrimaryKeyRelatedField(queryset=DesignPermit.objects.all(), source="design_permit", required=False, allow_null=True)
    building_permit_id = serializers.PrimaryKeyRelatedField(queryset=BuildingPermit.objects.all(), source="building_permit", required=False, allow_null=True)
    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    construction_type_id = serializers.PrimaryKeyRelatedField(queryset=ConstructionType.objects.all(), source="construction_type", required=False)
    building_type_id = serializers.PrimaryKeyRelatedField(queryset=BuildingType.objects.all(), source="building_type", required=False)
    state_display = serializers.CharField(source="get_state_display", read_only=True)

    class Meta:
        model = Permit
        fields = [
            "id",
            "planning_information_id",
            "planning_permit_id",
            "design_permit_id",
            "building_permit_id",
            "user_id",
            "user_name",
            "serial_no",
            "construction_id",
            "state",
            "state_display",
            "region_id",
            "dzongkhag_id",
            "gewog_id",
            "thromde_id",
            "village_id",
            "purpose",
            "construction_type_id",
            "building_type_id",
            "created_at",
            "fee",
            "flow_stage",
            "nature",
            "design_teams",
            "slabs",
            "payments",
            "number_of_floors",
            "land_pooling",
            "build_up_area",
            "units_use",
            "architectural_change",
            "require_engineers",
        ]
