# Generated by Django 4.1.7 on 2025-05-22 22:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm
import modification.helpers.permit
import modification.transitions.permit


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('common', '0013_rename_cdb_no_designteam_bcta_no'),
        ('address', '0010_region_dzongkhag_created_at_dzongkhag_updated_at_and_more'),
        ('building', '0039_permit_development_type_permit_purpose'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Permit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nature', models.CharField(choices=[('urban', 'Urban'), ('rural', 'Rural')], default='rural', max_length=50, null=True)),
                ('serial_no', models.CharField(max_length=50, null=True)),
                ('state', django_fsm.FSMField(choices=[('draft', 'Draft'), ('initiated', 'Initiated'), ('requested', 'Requested'), ('forwarded_to_roid', 'Forwarded to ROID'), ('forwarded_to_dhs', 'Forwarded to DHS'), ('forwarded_to_bpc', 'Forwarded to BPC'), ('architect_assigned', 'Architect Assigned'), ('under_architect_review', 'Under Architect Review'), ('under_focal_review', 'Under Focal Points Review'), ('under_bpc_review', 'Under BPC Review'), ('bpc_approved', 'BPC Approved'), ('bpc_rejected', 'BPC Rejected'), ('resubmitted', 'Re-submitted'), ('pending_change', 'Pending Change'), ('pending_payment', 'Pending Payment'), ('technical_clearance', 'Technical Clearance'), ('forwarded_to_cde', 'Forwarded to Chief Dzongkhag Engineer'), ('under_cde_review', 'Under Chief Dzongkhag Engineer Review'), ('cde_approved', 'Chief Dzongkhag Engineer Approved'), ('cde_rejected', 'Chief Dzongkhag Engineer Rejected'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('closed', 'Closed')], default='initiated', max_length=50)),
                ('purpose', models.CharField(choices=[('extension', 'Extension'), ('use_type_change', 'Use Type Change'), ('structural_change', 'Structural Change'), ('other', 'Other')], max_length=100)),
                ('fee', models.FloatField(blank=True, null=True)),
                ('architectural_change', models.BooleanField(default=True)),
                ('require_engineers', models.BooleanField(default=False)),
                ('number_of_floors', models.IntegerField(blank=True, null=True)),
                ('land_pooling', models.BooleanField(default=False)),
                ('build_up_area', models.FloatField(blank=True, null=True)),
                ('units_use', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('building_permit', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='modification_permits', to='building.permit')),
                ('building_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.buildingtype')),
                ('by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_modification_permits', to=settings.AUTH_USER_MODEL)),
                ('construction_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.constructiontype')),
                ('dzongkhag', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modification_permits', to='address.dzongkhag')),
                ('gewog', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modification_permits', to='address.gewog')),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modification_permits', to='address.region')),
                ('thromde', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modification_permits', to='address.thromde')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='modification_permits', to=settings.AUTH_USER_MODEL)),
                ('village', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modification_permits', to='address.village')),
            ],
            options={
                'verbose_name': 'modification_permit',
                'verbose_name_plural': 'modification_permits',
            },
            bases=(models.Model, modification.helpers.permit.PermitHelper, modification.transitions.permit.PermitTransition),
        ),
    ]
