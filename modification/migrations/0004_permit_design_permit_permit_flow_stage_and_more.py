# Generated by Django 4.1.7 on 2025-06-10 12:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('information', '0014_inquiry_region'),
        ('planning', '0064_application_region'),
        ('building', '0041_permit_building_pe_created_93e403_idx_and_more'),
        ('design', '0008_permit_design_perm_created_14592f_idx_and_more'),
        ('modification', '0003_permit_modificatio_created_f44802_idx_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='permit',
            name='design_permit',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modification_design_permits', to='design.permit'),
        ),
        migrations.AddField(
            model_name='permit',
            name='flow_stage',
            field=models.CharField(choices=[('planning_info', 'Planning Information'), ('planning_permit', 'Planning Permit'), ('design_permit', 'Design Permit'), ('building_permit', 'Building Permit')], default='planning_permit', max_length=50),
        ),
        migrations.AddField(
            model_name='permit',
            name='planning_information',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modification_planning_informations', to='information.inquiry'),
        ),
        migrations.AddField(
            model_name='permit',
            name='planning_permit',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modification_planning_permits', to='planning.application'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='building_permit',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modification_building_permits', to='building.permit'),
        ),
    ]
