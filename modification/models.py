from django.db import models
from modification.enums import PermitStatus, Purpose, ModificationFlowStage
from common.enums import LandNature
from user.models import User
from django_fsm import F<PERSON><PERSON>ield
from modification.helpers.permit import PermitHelper
from modification.transitions.permit import PermitTransition
from django.contrib.contenttypes.fields import GenericRelation
from django.utils.translation import gettext_lazy as _


class Permit(models.Model, PermitHelper, PermitTransition):
    by = models.ForeignKey(User, related_name="assigned_modification_permits", null=True, blank=True, on_delete=models.SET_NULL)
    user = models.ForeignKey(User, null=False, related_name="modification_permits", on_delete=models.CASCADE)
    planning_information = models.ForeignKey("information.Inquiry", null=True, related_name="modification_planning_informations", on_delete=models.SET_NULL)
    planning_permit = models.ForeignKey("planning.Application", null=True, related_name="modification_planning_permits", on_delete=models.SET_NULL)
    design_permit = models.ForeignKey("design.Permit", null=True, related_name="modification_design_permits", on_delete=models.SET_NULL)
    building_permit = models.ForeignKey("building.Permit", null=True, related_name="modification_building_permits", on_delete=models.SET_NULL)
    nature = models.CharField(max_length=50, null=True, choices=LandNature.choices, default=LandNature.RURAL)
    serial_no = models.CharField(max_length=50, null=True)
    construction_id = models.CharField(max_length=100, null=True, blank=True, help_text="Unique identifier for tracking construction across all modules")
    state = FSMField(max_length=50, null=False, choices=PermitStatus.choices, default=PermitStatus.INITIATED)
    flow_stage = models.CharField(max_length=50, null=False, choices=ModificationFlowStage.choices, default=ModificationFlowStage.PLANNING_PERMIT)
    region = models.ForeignKey("address.Region", null=True, on_delete=models.SET_NULL, blank=True, related_name="modification_permits")
    dzongkhag = models.ForeignKey("address.Dzongkhag", null=True, on_delete=models.SET_NULL, related_name="modification_permits")
    gewog = models.ForeignKey("address.Gewog", null=True, on_delete=models.SET_NULL, blank=True, related_name="modification_permits")
    village = models.ForeignKey("address.Village", null=True, on_delete=models.SET_NULL, blank=True, related_name="modification_permits")
    thromde = models.ForeignKey("address.Thromde", null=True, on_delete=models.SET_NULL, blank=True, related_name="modification_permits")
    purpose = models.CharField(max_length=100, choices=Purpose.choices)
    construction_type = models.ForeignKey("common.ConstructionType", null=True, on_delete=models.SET_NULL, blank=True)
    building_type = models.ForeignKey("common.BuildingType", null=True, on_delete=models.SET_NULL, blank=True)
    task_pools = GenericRelation("common.TaskPool", related_query_name="modification_permit")
    design_teams = GenericRelation("common.DesignTeam", related_query_name="modification_permit")
    payments = GenericRelation("payment.Payment", related_query_name="modification_permit")
    slabs = GenericRelation("common.Slab", related_query_name="modification_permit")
    fee = models.FloatField(null=True, blank=True)
    architectural_change = models.BooleanField(default=True)
    require_engineers = models.BooleanField(default=False)

    # Fields for floor details
    number_of_floors = models.IntegerField(null=True, blank=True)
    land_pooling = models.BooleanField(default=False)
    build_up_area = models.FloatField(null=True, blank=True)
    units_use = models.CharField(max_length=100, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        verbose_name = _("modification_permit")
        verbose_name_plural = _("modification_permits")
        indexes = [
            models.Index(fields=["created_at"]),
            models.Index(fields=["state"]),
            models.Index(fields=["dzongkhag"]),
            models.Index(fields=["gewog"]),
            models.Index(fields=["construction_id"]),
        ]
