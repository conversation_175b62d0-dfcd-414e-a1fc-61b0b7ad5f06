from django.db import models
from django.utils.translation import gettext_lazy as _


class PermitStatus(models.TextChoices):
    DRAFT = "draft", _("Draft")
    INITIATED = "initiated", _("Initiated")
    REQUESTED = "requested", _("Requested")
    FORWARDED_TO_ROID = "forwarded_to_roid", _("Forwarded to ROID")
    FORWARDED_TO_DHS = "forwarded_to_dhs", _("Forwarded to DHS")
    FORWARDED_TO_BPC = "forwarded_to_bpc", _("Forwarded to BPC")
    ARCHITECT_ASSIGNED = "architect_assigned", _("Architect Assigned")
    UNDER_ARCHITECT_REVIEW = "under_architect_review", _("Under Architect Review")
    UNDER_FOCAL_REVIEW = "under_focal_review", _("Under Focal Points Review")
    UNDER_BPC_REVIEW = "under_bpc_review", _("Under BPC Review")
    BPC_APPROVED = "bpc_approved", _("BPC Approved")
    BPC_REJECTED = "bpc_rejected", _("BPC Rejected")
    RESUBMITTED = "resubmitted", _("Re-submitted")
    PENDING_CHANGE = "pending_change", _("Pending Change")
    PENDING_PAYMENT = "pending_payment", _("Pending Payment")
    TECHNICAL_CLEARANCE = "technical_clearance", _("Technical Clearance")
    FORWARDED_TO_CDE = "forwarded_to_cde", _("Forwarded to Chief Dzongkhag Engineer")
    UNDER_CDE_REVIEW = "under_cde_review", _("Under Chief Dzongkhag Engineer Review")
    CDE_APPROVED = "cde_approved", _("Chief Dzongkhag Engineer Approved")
    CDE_REJECTED = "cde_rejected", _("Chief Dzongkhag Engineer Rejected")
    APPROVED = "approved", _("Approved")
    REJECTED = "rejected", _("Rejected")
    CLOSED = "closed", _("Closed")


class Purpose(models.TextChoices):
    EXTENSION = "extension", _("Extension")
    USE_TYPE_CHANGE = "use_type_change", _("Use Type Change")
    STRUCTURAL_CHANGE = "structural_change", _("Structural Change")
    OTHER = "other", _("Other")


class ModificationFlowStage(models.TextChoices):
    PLANNING_INFO = "planning_info", _("Planning Information")
    PLANNING_PERMIT = "planning_permit", _("Planning Permit")
    DESIGN_PERMIT = "design_permit", _("Design Permit")
    BUILDING_PERMIT = "building_permit", _("Building Permit")
