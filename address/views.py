from rest_framework import generics
from address.serializers import *
from rest_framework.filters import SearchFilter


class RegionView(generics.ListCreateAPIView):
    queryset = Region.objects.all()
    serializer_class = RegionSerializer
    filter_backends = [SearchFilter]
    search_fields = ("name", "description")


class RegionPKView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Region.objects.all()
    serializer_class = RegionSerializer


class DzongkhagView(generics.ListCreateAPIView):
    queryset = Dzongkhag.objects.all()
    serializer_class = DzongkhagSerializer
    filter_backends = [SearchFilter]
    search_fields = ("name", "description")

    def get_queryset(self):
        reg_id = self.kwargs.get("pk")
        return Dzongkhag.objects.filter(region_id=reg_id) if reg_id else Dzongkhag.objects.all()


class DzongkhagPKView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Dzongkhag.objects.all()
    serializer_class = DzongkhagSerializer


class GewogView(generics.ListCreateAPIView):
    serializer_class = GewogSerializer
    filter_backends = [SearchFilter]
    search_fields = ("name", "description")

    def get_queryset(self):
        dzo_id = self.kwargs.get("pk")
        return Gewog.objects.filter(dzongkhag_id=dzo_id) if dzo_id else Gewog.objects.all()


class GewogPKView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Gewog.objects.all()
    serializer_class = GewogSerializer


class VillageView(generics.ListCreateAPIView):
    serializer_class = VillageSerializer
    filter_backends = [SearchFilter]
    search_fields = ("name", "description")

    def get_queryset(self):
        gewog_id = self.kwargs.get("pk")
        return Village.objects.filter(gewog_id=gewog_id) if gewog_id else Village.objects.all()


class VillagePKView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Village.objects.all()
    serializer_class = VillageSerializer


class ThromdeView(generics.ListCreateAPIView):
    serializer_class = ThromdeSerializer
    filter_backends = [SearchFilter]
    search_fields = ("name", "description")

    def get_queryset(self):
        dzo_id = self.kwargs.get("pk")
        return Thromde.objects.filter(dzongkhag_id=dzo_id) if dzo_id else Thromde.objects.all()


class ThromdePKView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Thromde.objects.all()
    serializer_class = ThromdeSerializer
