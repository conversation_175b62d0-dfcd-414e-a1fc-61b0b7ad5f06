from django.db import models


class Region(models.Model):
    name = models.CharField(max_length=50, null=False, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)


class Dzongkhag(models.Model):
    name = models.CharField(max_length=50, null=False, unique=True)
    description = models.TextField(blank=True)
    agency_code = models.CharField(max_length=50, null=True, unique=True)
    region = models.ForeignKey(Region, null=True, on_delete=models.SET_NULL, related_name="dzongkhags")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)


class Gewog(models.Model):
    name = models.CharField(max_length=50, null=False)
    description = models.TextField(blank=True)
    dzongkhag = models.ForeignKey(Dzongkhag, null=True, on_delete=models.SET_NULL, related_name="gewogs")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = [("name", "dzongkhag")]

    def __str__(self):
        return str(self.__dict__)


class Village(models.Model):
    name = models.CharField(max_length=50, null=False)
    description = models.TextField(blank=True)
    gewog = models.ForeignKey(Gewog, null=True, on_delete=models.SET_NULL, related_name="villages")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = [("name", "gewog")]

    def __str__(self):
        return str(self.__dict__)


class Thromde(models.Model):
    name = models.CharField(max_length=50, null=False)
    description = models.TextField(blank=True)
    dzongkhag = models.ForeignKey(Dzongkhag, null=True, on_delete=models.SET_NULL, related_name="thromdes")
    tenant_code = models.CharField(max_length=50, null=True, unique=True)
    counter_code = models.CharField(max_length=50, null=True, unique=True)
    service_code = models.CharField(max_length=50, null=True, unique=True)
    username = models.CharField(max_length=50, null=True, unique=True)
    password = models.CharField(max_length=50, null=True, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = [("name", "dzongkhag")]

    def __str__(self):
        return str(self.__dict__)
