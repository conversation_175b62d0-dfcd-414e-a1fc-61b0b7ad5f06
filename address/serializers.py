from rest_framework import serializers
from .models import *


class RegionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Region
        fields = ["id", "name", "description"]


class DzongkhagSerializer(serializers.ModelSerializer):
    region_id = serializers.PrimaryKeyRelatedField(queryset=Region.objects.all(), source="region", required=False, allow_null=True)
    region = RegionSerializer(read_only=True)

    class Meta:
        model = Dzongkhag
        fields = ["id", "name", "agency_code", "region_id", "region", "description"]


class GewogSerializer(serializers.ModelSerializer):
    dzongkhag = DzongkhagSerializer(read_only=True)
    dzongkhag_id = serializers.PrimaryKeyRelatedField(queryset=Dzongkhag.objects.all(), source="dzongkhag", required=True)

    class Meta:
        model = Gewog
        fields = ["id", "name", "dzongkhag_id", "description", "dzongkhag"]


class VillageSerializer(serializers.ModelSerializer):
    gewog = GewogSerializer(read_only=True)
    gewog_id = serializers.PrimaryKeyRelatedField(queryset=Gewog.objects.all(), source="gewog", required=True)

    class Meta:
        model = Village
        fields = ["id", "name", "description", "gewog_id", "gewog"]
        depth = 1


class ThromdeSerializer(serializers.ModelSerializer):
    dzongkhag_id = serializers.PrimaryKeyRelatedField(queryset=Dzongkhag.objects.all(), source="dzongkhag", required=True)

    class Meta:
        model = Thromde
        fields = ["id", "name", "description", "dzongkhag_id", "tenant_code", "counter_code", "service_code", "username", "password"]
