import factory
from .models import *


class DzongkhagFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Dzongkhag

    name = factory.LazyAttribute(lambda obj: factory.Faker("state"))
    description = factory.Faker("paragraph")


class ThromdeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Thromde

    name = factory.LazyAttribute(lambda obj: factory.Faker("state"))
    description = factory.Faker("paragraph") 
    dzongkhag = factory.SubFactory(DzongkhagFactory)


class GewogFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Gewog

    name = factory.LazyAttribute(lambda obj: factory.Faker("state"))
    description = factory.Faker("paragraph")
    dzongkhag = factory.SubFactory(DzongkhagFactory)


class VillageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Village

    name = factory.LazyAttribute(lambda obj: factory.Faker("state"))
    description = factory.Faker("paragraph")
    gewog = factory.SubFactory(GewogFactory)
