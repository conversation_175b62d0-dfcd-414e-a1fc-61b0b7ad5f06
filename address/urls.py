from django.urls import path
from address import views

urlpatterns = [
    path("regions/", views.RegionView.as_view()),
    path("regions/<pk>/", views.RegionPKView.as_view()),
    path("regions/<pk>/dzongkhags/", views.DzongkhagView.as_view()),
    path("dzongkhags/", views.DzongkhagView.as_view()),
    path("dzongkhags/<pk>/", views.DzongkhagPKView.as_view()),
    path("dzongkhags/<pk>/gewogs/", views.GewogView.as_view()),
    path("gewogs/", views.GewogView.as_view()),
    path("gewogs/<pk>/", views.GewogPKView.as_view()),
    path("gewogs/<pk>/villages/", views.VillageView.as_view()),
    path("villages/", views.VillageView.as_view()),
    path("villages/<pk>/", views.VillagePKView.as_view()),
    path("dzongkhags/<pk>/thromdes/", views.ThromdeView.as_view()),
    path("thromdes/", views.ThromdeView.as_view()),
    path("thromdes/<pk>/", views.ThromdePKView.as_view()),
]
