# Generated by Django 4.1.7 on 2024-11-13 03:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('address', '0006_thromde_code'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='thromde',
            name='code',
        ),
        migrations.AddField(
            model_name='thromde',
            name='counter_code',
            field=models.Char<PERSON>ield(max_length=50, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='thromde',
            name='password',
            field=models.Char<PERSON>ield(max_length=50, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='thromde',
            name='service_code',
            field=models.Char<PERSON><PERSON>(max_length=50, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='thromde',
            name='tenant_code',
            field=models.Char<PERSON>ield(max_length=50, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='thromde',
            name='username',
            field=models.Char<PERSON><PERSON>(max_length=50, null=True, unique=True),
        ),
    ]
