from django_fsm import transition
from django_fsm_log.decorators import fsm_log_by
from django_fsm_log.decorators import fsm_log_description


class Transition:

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="status", source=["*"], target="pending")
    def start(self, by=None, description=None):
        discription = "Retrying payment" if self.status == "pending" else "Payment started."
        description.set(discription)
        self.by = by

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="status", source=["pending", "initiated", "cheque_bounce"], target="paid")
    def pay(self, by=None, description=None):
        description.set("Payment successful.")
        self.by = by

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="status", source="*", target="failed")
    def fail(self, data, by=None, description=None):
        description.set(data.get("message", "Payment failed."))
        self.by = by

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="status", source=["paid", "cheque_bounce"], target="cheque_bounce")
    def bounce(self, data, by=None, description=None):
        from payment.models import Receipt

        remark = data.get("remarks", "Cheque bounced.")
        receipt_no = data.get("receiptNo", None)
        description.set(remark)
        receipt = Receipt.objects.get(receipt_no=receipt_no)
        receipt.status = "failed"
        receipt.remarks = remark
        receipt.save()
        self.by = by

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="status", source="*", target="cancelled")
    def cancel(self, data, by=None, description=None):
        remark = data.get("remarks", "Payment cancelled.")
        description.set(remark)
        self.data = data
        self.by = by
