from math import perm
from django.shortcuts import get_object_or_404
from django_fsm import can_proceed
from rest_framework.exceptions import ValidationError
from rest_framework import generics, response, status, viewsets
from rest_framework.filters import SearchFilter
from building.models import Permit
import payment
from payment.models import Payment
from payment.serializers import PaymentSerializer
from rest_framework.decorators import action
from django.db.models import Q
from django_fsm_log.models import StateLog
from common.serializers import StateLogSerializer


class PaymentView(generics.ListCreateAPIView):
    serializer_class = PaymentSerializer
    filter_backends = [SearchFilter]
    search_fields = ("transaction_no", "remarks")


class PaymentPKView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer


class PaymentCustomView(viewsets.ModelViewSet):
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer

    @action(detail=True, methods=["put"])
    def transition(self, request, *args, **kwargs):
        instance = get_object_or_404(Payment, pk=kwargs.get("pk"))
        action = request.data.pop("action")
        if not hasattr(instance, action) or not can_proceed(getattr(instance, action)):
            return response.Response({"error": "Invalid transition"}, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
        if getattr(instance, "can_" + action)(request.user):
            if not action == "start" and instance.task_pools.filter(user=request.user, state="assigned").exists():
                raise ValidationError({"error": "You need to start your task first."})
            getattr(instance, action)(by=request.user)
            instance.save()
            serializer = PaymentSerializer(instance)
            return response.Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return response.Response({"error": "You are not permited to perform this action."}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def confirm(self, request, *args, **kwargs):
        import json

        data = json.loads(request.body)
        if not data and not data.get("refNo", None) and not data.get("paymentAdviceNo", None):
            msg = "Reference number or payment advice number is required."
            raise ValidationError({"error": msg})
        refNo = data.get("refNo", "")
        paNo = data.get("paymentAdviceNo", "")
        payment = Payment.objects.filter(Q(reference_no=refNo) | Q(transaction_no=paNo)).first()
        if not payment:
            raise ValidationError({"error": f"Transaction not found or Invalid request with refNo: '{refNo}' and paymentAdviceNo: '{paNo}'."})
        if payment.status == "paid":
            raise ValidationError({"error": "You have already made the payment."})
        payable = payment.payable
        if data.get("faultcode", "") == "200":
            payment.pay(payable.user)
            payable.pay(payment)
            payment.data = data
            payment.create_receipt(data.get("receiptList", []))
            payable.save()
        else:
            payment.fail(data, payable.user)
        payment.save()
        return response.Response({"message": "Payment confirmed successfully."}, status=status.HTTP_200_OK)

    @action(detail=False, methods=["get"])
    def activity_logs(self, request, *args, **kwargs):
        payments = get_object_or_404(Payment, pk=kwargs.get("pk"))
        logs = StateLog.objects.for_(payments)
        serializer = StateLogSerializer(logs, many=True)
        return response.Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["post"])
    def cheque_bounce(self, request, *args, **kwargs):
        import json

        data = json.loads(request.body)
        if not data.get("receiptNo", None):
            raise ValidationError({"error": "Invalid request"})
        payment = Payment.objects.filter(receipts__receipt_no=data.get("receiptNo", None)).first()
        if not payment:
            raise ValidationError({"error": "Information with given receiptNo is not found."})
        if payment.status == "cheque_bounce":
            raise ValidationError({"error": "Cheque has already been bounced."})
        payable = payment.payable
        payment.bounce(data, payable.user)
        payment.save()
        payable.payment({"remarks": data.get("remarks", "Cheque bounced.")}, payable.user)
        payable.save()
        return response.Response({"message": "Cheque bounce request sent successfully."}, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"])
    def receipt(self, request, *args, **kwargs):
        from cas_api.services.birms_service import generate_receipt, generate_autonomous_receipt

        payment = Payment.objects.get(receipts__receipt_no=kwargs.get("receipt_no"))
        res = generate_receipt(kwargs.get("receipt_no")) if payment.payable == "rural" else generate_autonomous_receipt(kwargs.get("receipt_no"))
        return response.Response(res, status=status.HTTP_200_OK)

    @action(detail=True, methods=["post"])
    def cancel(self, request, *args, **kwargs):
        from cas_api.services.birms_service import cancel_payment, cancel_autonomous_payment

        name = request.user.current_role.name
        if name not in ("dadm", "moitadm", "thrmadm", "gadm"):
            return response.Response({"error": "You are not allowed to cancel the payment."}, status=status.HTTP_400_BAD_REQUEST)
        data = request.data
        payment = Payment.objects.get(id=kwargs.get("pk", None))
        if payment.status == "cancelled":
            return response.Response({"error": "Payment already cancelled."}, status=status.HTTP_400_BAD_REQUEST)
        cancel_payment(payment, data, request.user) if payment.payable.nature == "rural" else cancel_autonomous_payment(payment, data, request.user)
        payment.cancel(data, request.user)
        payment.save()
        return response.Response({"message": "Payment cancelled successfully."}, status=status.HTTP_200_OK)
