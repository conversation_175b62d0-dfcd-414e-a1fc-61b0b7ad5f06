from django.dispatch import receiver
from django_fsm.signals import post_transition
from payment.models import Payment
from notifications.signals import notify


@receiver(post_transition, sender=Payment)
def after_payment_transition(sender, instance, name, source, target, **kwargs):
    payable = instance.payable
    if name == "cancel":
        payable.pay(getattr(instance, "data", {}), by=instance.by)
        payable.save()
    create_notification(instance, target)


def create_notification(instance, action):
    notify.send(instance.by, recipient=instance.payable.user, verb=action, action_object=instance, target=instance, url="")
    notify.send(instance.by, recipient=instance.by, verb=action, action_object=instance, target=instance, url="")
