from rest_framework.exceptions import ValidationError


class Helper:
    def can_start(self, current_user):
        current_role = current_user.current_role
        if current_role.name == "user" and self.payable.user == current_user:
            return True
        else:
            raise ValidationError({"error": "Not permitted to perform this action."})

    def can_fail(self, current_user):
        return False

    def can_pay(self, current_user):
        return False

    def create_receipt(self, data):
        from datetime import datetime

        for obj in data:
            self.receipts.create(
                receipt_no=obj.get("receiptNo", None),
                receipt_date=obj.get("receiptDate", datetime.now()),
                total_receipt_amount=obj.get("totalReceiptAmount", None),
                payment_advice_amount=obj.get("paymentAdviceAmount", None),
                payment_advice_amount_paid=obj.get("paymentAdviceAmountPaid", None),
                balance_amount=obj.get("balanceAmount", None),
                penalty_amount=obj.get("penaltyAmount", None),
                penality_description=obj.get("penalityDescription", None),
                payment_advice_status=obj.get("paymentAdviceStatus", None),
            )
