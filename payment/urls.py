from django.urls import path
from payment import views

urlpatterns = [
    path("payments/", views.PaymentView.as_view(), name="payments"),
    path("payments/<int:pk>/", views.PaymentPKView.as_view(), name="payments"),
    path(
        "payments/confirm/",
        views.PaymentCustomView.as_view({"post": "confirm"}),
        name="confirm",
    ),
    path(
        "payments/<pk>/transition/",
        views.PaymentCustomView.as_view({"put": "transition"}),
        name="transitions",
    ),
    path(
        "payments/<pk>/activity_logs/",
        views.PaymentCustomView.as_view({"get": "activity_logs"}),
        name="activity_logs",
    ),
    path(
        "payments/cheque_bounce/",
        views.PaymentCustomView.as_view({"post": "cheque_bounce"}),
        name="cheque_bounce",
    ),
    path(
        "payments/<receipt_no>/receipt/",
        views.PaymentCustomView.as_view({"get": "receipt"}),
        name="receipt",
    ),
    path(
        "payments/<pk>/cancel/",
        views.PaymentCustomView.as_view({"post": "cancel"}),
        name="cancel",
    ),
]
