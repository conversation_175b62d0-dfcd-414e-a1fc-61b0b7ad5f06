from pyexpat import model
from django.db import models
from django_fsm import F<PERSON><PERSON>ield
from payment.helpers import Helper
from payment.transitions import Transition
from .enums import PaymentMode, PaymentStatus, ReceiptStatus
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType


class Payment(models.Model, Transition, Helper):
    amount = models.DecimalField(decimal_places=2, max_digits=50, null=False)
    status = FSMField(max_length=20, null=False, choices=PaymentStatus.choices, default=PaymentStatus.INITIATED)
    mode = models.CharField(max_length=50, null=False, choices=PaymentMode.choices, default=PaymentMode.COUNTER)
    reference_no = models.CharField(null=True, blank=True, max_length=50)
    transaction_no = models.CharField(null=True, blank=True, max_length=50)
    payment_date = models.DateTimeField(null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    remarks = models.TextField(null=True, blank=True)
    redirect_url = models.TextField(null=True, blank=True)
    data = models.JSONField(null=True, blank=True)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True)
    object_id = models.PositiveBigIntegerField(null=True)
    payable = GenericForeignKey("content_type", "object_id")
    payer_cid = models.CharField(max_length=50, null=True)
    payer_name = models.CharField(max_length=50, null=True)
    payer_email = models.EmailField(null=True)
    payer_phone = models.CharField(max_length=50, null=True)
    agency_code = models.CharField(max_length=50, null=True)
    counter_code = models.CharField(max_length=50, null=True)
    tenant_code = models.CharField(max_length=50, null=True)
    service_code = models.CharField(max_length=50, null=False, default="178")
    service_name = models.CharField(max_length=200, null=False, default="New Construction Approval")

    def __str__(self):
        return str(self.__dict__)


class Receipt(models.Model):
    payment = models.ForeignKey(Payment, related_name="receipts", on_delete=models.CASCADE)
    receipt_no = models.CharField(null=False, blank=False, max_length=50)
    receipt_date = models.DateTimeField(null=False)
    total_receipt_amount = models.DecimalField(decimal_places=2, max_digits=50)
    payment_advice_amount = models.DecimalField(decimal_places=2, max_digits=50)
    payment_advice_amount_paid = models.DecimalField(decimal_places=2, max_digits=50)
    balance_amount = models.DecimalField(decimal_places=2, max_digits=50)
    penalty_amount = models.DecimalField(decimal_places=2, max_digits=50)
    penality_description = models.TextField(null=True, blank=True)
    payment_advice_status = models.CharField(max_length=50)
    status = models.CharField(max_length=20, null=False, choices=ReceiptStatus.choices, default=ReceiptStatus.SUCCESS)
    remarks = models.TextField(null=True, blank=True)

    def __str__(self):
        return str(self.__dict__)
