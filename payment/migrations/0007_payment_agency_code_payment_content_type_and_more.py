# Generated by Django 4.1.7 on 2025-03-25 20:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('payment', '0006_alter_payment_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='payment',
            name='agency_code',
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='content_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='payment',
            name='counter_code',
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='object_id',
            field=models.PositiveBigIntegerField(null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='payer_cid',
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='payer_email',
            field=models.EmailField(max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='payer_name',
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='payer_phone',
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='reference_no',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='service_code',
            field=models.CharField(default='178', max_length=50),
        ),
        migrations.AddField(
            model_name='payment',
            name='service_name',
            field=models.CharField(default='New Construction Approval', max_length=200),
        ),
        migrations.AddField(
            model_name='payment',
            name='tenant_code',
            field=models.CharField(max_length=50, null=True),
        ),
    ]
