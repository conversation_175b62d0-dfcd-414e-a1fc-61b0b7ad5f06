# Generated by Django 4.1.7 on 2023-12-27 10:30

from django.db import migrations, models
import django_fsm


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=50)),
                ('status', django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('pending', 'Pending'), ('paid', 'Paid'), ('failed', 'Failed')], default='initiated', max_length=20)),
                ('mode', models.CharField(choices=[('counter', 'Counter'), ('birms', 'BIRMS')], default='counter', max_length=50)),
                ('transaction_no', models.CharField(blank=True, max_length=50, null=True)),
                ('payment_date', models.DateTimeField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('remarks', models.TextField(blank=True, null=True)),
            ],
        ),
    ]
