# Generated by Django 4.1.7 on 2024-03-13 08:39

from django.db import migrations, models
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('payment', '0004_receipt'),
    ]

    operations = [
        migrations.AddField(
            model_name='receipt',
            name='remarks',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='receipt',
            name='status',
            field=models.CharField(choices=[('success', 'Initiated'), ('failed', 'Failed')], default='success', max_length=20),
        ),
        migrations.AlterField(
            model_name='payment',
            name='status',
            field=django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('pending', 'Pending'), ('paid', 'Paid'), ('failed', 'Failed'), ('cheque_bounce', 'Cheque Bounce')], default='initiated', max_length=20),
        ),
    ]
