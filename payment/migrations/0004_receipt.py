# Generated by Django 4.1.7 on 2024-03-12 20:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('payment', '0003_payment_data'),
    ]

    operations = [
        migrations.CreateModel(
            name='Receipt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('receipt_no', models.CharField(max_length=50)),
                ('receipt_date', models.DateTimeField()),
                ('total_receipt_amount', models.DecimalField(decimal_places=2, max_digits=50)),
                ('payment_advice_amount', models.DecimalField(decimal_places=2, max_digits=50)),
                ('payment_advice_amount_paid', models.DecimalField(decimal_places=2, max_digits=50)),
                ('balance_amount', models.DecimalField(decimal_places=2, max_digits=50)),
                ('penalty_amount', models.DecimalField(decimal_places=2, max_digits=50)),
                ('penality_description', models.TextField(blank=True, null=True)),
                ('payment_advice_status', models.CharField(max_length=50)),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='receipts', to='payment.payment')),
            ],
        ),
    ]
