import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase
from django.core.management import call_command
from user.factories import UserFactory, ProfileFactory
from address.factories import DzongkhagFactory, GewogFactory, ThromdeFactory
from django.urls import path, include
from user.models import Role
from payment.models import Payment
from payment.factories import PaymentFactory
from common.models import Service
from common.factories import ServiceFactory


class PaymentTransitionTest(APITestCase, URLPatternsTestCase):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("payment.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.dzo = DzongkhagFactory(name="Mongar")
        self.gewog = GewogFactory(name="Mongar", dzongkhag=self.dzo)
        self.thromde = ThromdeFactory(name="Mongar", dzongkhag=self.dzo)

        # Create regular user
        self.user = UserFactory(username="user", roles=Role.objects.filter(name__in=["user"]).values_list("id", flat=True))
        ProfileFactory(user=self.user, dzongkhag=self.dzo)

        # Create cashier
        self.cashier = UserFactory(username="cashier", roles=Role.objects.filter(name__in=["cashier"]).values_list("id", flat=True))
        ProfileFactory(user=self.cashier, dzongkhag=self.dzo)

        # Get token for cashier
        res = self.client.post(reverse("token_obtain_pair"), {"username": self.cashier.username, "password": "Dcpl@123"})
        self.cashier_token = json.loads(res.content)["data"]["access"]

        # Create service
        self.service = ServiceFactory(service_type="bp", name="Building Permit", code="BP")

        # Create payment
        self.payment = PaymentFactory(
            amount=1000,
            payer=self.user,
            service=self.service,
            agency_code=self.dzo.agency_code,
            counter_code=self.thromde.counter_code if self.thromde else None,
            tenant_code=self.thromde.tenant_code if self.thromde else None
        )

    def test_invalid_transition(self):
        """Test that invalid transitions return appropriate error"""
        data = {"action": "invalid_action"}
        url = reverse("payment-transitions", kwargs={"pk": self.payment.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.cashier_token}"
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertEqual(json.loads(response.content)["error"], "Invalid transition")

    def test_payment_flow(self):
        """Test complete payment flow"""
        url = reverse("payment-transitions", kwargs={"pk": self.payment.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.cashier_token}"

        # Test payment verification
        data = {
            "action": "verify",
            "remarks": "Payment verified"
        }
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["status"], "verified")

        # Test payment completion
        data = {
            "action": "complete",
            "remarks": "Payment completed",
            "receipt_no": "RCPT-001"
        }
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["status"], "completed")

    def test_payment_rejection(self):
        """Test payment rejection"""
        url = reverse("payment-transitions", kwargs={"pk": self.payment.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.cashier_token}"

        # Test rejection without remarks
        data = {"action": "reject"}
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test rejection with remarks
        data = {
            "action": "reject",
            "reject_remarks": "Invalid payment amount"
        }
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["status"], "rejected")
