import factory
from factory.django import DjangoModelFactory
from payment.models import Payment
from user.factories import UserFactory
from common.factories import ServiceFactory
from payment.enums import PaymentStatus, PaymentMode


class PaymentFactory(DjangoModelFactory):
    class Meta:
        model = Payment

    amount = factory.Sequence(lambda n: 1000 + n)
    status = PaymentStatus.INITIATED
    mode = PaymentMode.BIRMS
    reference_no = factory.Sequence(lambda n: f"REF-{n}")
    payer = factory.SubFactory(UserFactory)
    service = factory.SubFactory(ServiceFactory)
    payer_cid = factory.LazyAttribute(lambda o: o.payer.username)
    payer_name = factory.LazyAttribute(lambda o: o.payer.get_full_name())
    payer_email = factory.LazyAttribute(lambda o: o.payer.email)
    payer_phone = factory.LazyAttribute(lambda o: getattr(o.payer.profile, 'phone', None))
    agency_code = None
    counter_code = None
    tenant_code = None
    service_code = factory.LazyAttribute(lambda o: o.service.code)
    service_name = factory.LazyAttribute(lambda o: o.service.name)
