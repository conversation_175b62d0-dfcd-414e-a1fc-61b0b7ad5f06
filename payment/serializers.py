from rest_framework import serializers
from payment.models import Payment, Receipt


class ReceiptSerializer(serializers.ModelSerializer):
    class Meta:
        model = Receipt
        fields = (
            "receipt_no",
            "receipt_date",
            "total_receipt_amount",
            "payment_advice_amount",
            "payment_advice_amount_paid",
            "balance_amount",
            "penalty_amount",
            "penality_description",
            "payment_advice_status",
            "status",
            "remarks",
        )


class PaymentSerializer(serializers.ModelSerializer):
    receipts = ReceiptSerializer(many=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)

    class Meta:
        model = Payment
        fields = [
            "id",
            "reference_no",
            "transaction_no",
            "amount",
            "mode",
            "status",
            "status_display",
            "payment_date",
            "redirect_url",
            "updated_at",
            "agency_code",
            "payer_name",
            "payer_email",
            "payer_phone",
            "payer_cid",
            "service_code",
            "service_name",
            "counter_code",
            "tenant_code",
            "receipts",
        ]
