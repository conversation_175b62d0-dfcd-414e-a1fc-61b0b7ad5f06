from django.db import models
from django.utils.translation import gettext_lazy as _


class PaymentStatus(models.TextChoices):
    INITIATED = "initiated", _("Initiated")
    PENDING = "pending", _("Pending")
    PAID = "paid", _("Paid")
    FAILED = "failed", _("Failed")
    CHEQUE_BOUNCE = "cheque_bounce", _("Cheque Bounce")
    CANCELLED = "cancelled", _("Cancelled")


class ReceiptStatus(models.TextChoices):
    SUCCESS = "success", _("Initiated")
    FAILED = "failed", _("Failed")


class PaymentMode(models.TextChoices):
    COUNTER = "counter", _("Counter")
    BIRMS = "birms", _("BIRMS")
