from django.db import models
from django.utils.translation import gettext_lazy as _


class RoleChoices(models.TextChoices):
    ADMIN = "admin", _("Admin")
    DPD = "dpd", _("Dzongkhag Planning Division")
    UPD = "upd", _("Urban Planning Division")
    LCD = "lcd", _("Location Clearance Division")
    # TCB = 'tcb', _('Tourism Counsil of Bhutan')
    # DRA = 'dra', _('Drug Regulatory Authority')
    # BPC = 'bpc', _('Bhutan Power Corporation')
    # BAFRA = 'bafra', _('Bhutan Agriculture and Food Regulatory Authority')
    DADM = "dadm", _("Dzongkhag Administration")
    GADM = "gadm", _("Gewog Administration")
    DES = "des", _("Department of Engineering Services")
    DESSE = "desse", _("Department of Engineering Services Structural Engineer")
    DESEE = "desee", _("Department of Engineering Services Electrical Engineer")
    DAR = "dar", _("Dzongkhag Architect")
    DSE = "dse", _("Dzongkhag Structural Engineer")
    DEE = "dee", _("Dzongkhag Electrical Engineer")
    DDZ = "ddz", _("Dasho Dzongda")
    DRDH = "drdh", _("Development and Regulatory Head")
    DRDFD = "drdfd", _("Development and Regulatory Division Front Desk")
    THRMP = "thrmp", _("Thrompon")
    THRMAR = "thrmar", _("Thromde Architect")
    THRMSE = "thrmse", _("Thromde Structural Engineer")
    THRMEE = "thrmee", _("Thromde Electrical Engineer")
    THRMRC = "thrmrc", _("Thromde Revenue Counter")
    EXESEC = "exesec", _("Executive Secretary")
    USER = "user", _("User")
