import json
from django.urls import include, path, reverse
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase
from django.core.management import call_command
from address.factories import DzongkhagFactory, GewogFactory

from user.factories import ProfileFactory, SettingFactory, UserFactory
from .models import Role, User


class UserTest(APITestCase, URLPatternsTestCase):
    urlpatterns = [
        path("api/v1/", include("user.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.user1 = User.objects.create_user(
            email="<EMAIL>",
            password="Dcpl@123",
            cid="123456789",
            username="user",
            roles=Role.objects.filter(name__in=["thrmwsf"]).values_list(
                "id", flat=True
            ),
        )

    def test_login(self):
        url = reverse("token_obtain_pair")
        data = {"username": "<EMAIL>", "password": "Dcpl@123"}
        response = self.client.post(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)

    def test_setting_create(self):
        dzo = DzongkhagFactory()
        dadm = UserFactory(
            roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True)
        )
        ProfileFactory(user=dadm, dzongkhag=dzo)

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dadm.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"

        url = reverse("settings", kwargs={"user_id": dadm.id})
        data = {"key": "fee_rate", "value": 120}
        response = self.client.post(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue("data" in response_data)

    def test_user_invite(self):
        dzo = DzongkhagFactory()
        gewog = GewogFactory(dzongkhag=dzo)
        dadm = UserFactory(
            roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True)
        )

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dadm.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"

        url = "/api/v1/accounts/register-email/"
        data = {
            "username": "test123",
            "email": "<EMAIL>",
            "password": "Test@123",
            "roles": [1],
            "profile": {"dzongkhag_id": dzo.id, "gewog_id": gewog.id},
        }
        response = self.client.post(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(
            response_data["data"]["detail"], "Register email link email sent"
        )

    def test_profile_update(self):
        dzo = DzongkhagFactory()
        gewog = GewogFactory(dzongkhag=dzo)
        dadm = UserFactory(
            roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True)
        )

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dadm.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"

        url = reverse("user_detail", kwargs={"pk": dadm.id})
        data = {"profile": {"dzongkhag_id": dzo.id, "gewog_id": gewog.id}}
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["profile"]["dzongkhag_id"], dzo.id)
        self.assertEqual(response_data["data"]["profile"]["gewog_id"], gewog.id)

    def test_setting_update(self):
        dzo = DzongkhagFactory()
        dadm = UserFactory(
            roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True)
        )
        ProfileFactory(user=dadm, dzongkhag=dzo)

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dadm.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"

        sf = SettingFactory(user=dadm)

        url = reverse("settings", kwargs={"pk": sf.id})
        data = {"key": "fee_rate", "value": 120}
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)

    def test_setting_list(self):
        dzo = DzongkhagFactory()
        dadm = UserFactory(
            roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True)
        )
        ProfileFactory(user=dadm, dzongkhag=dzo)

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dadm.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"

        SettingFactory(user=dadm)
        SettingFactory(user=dadm)
        SettingFactory(user=dadm)

        url = reverse("settings", kwargs={"user_id": dadm.id})

        response = self.client.get(url)
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(len(response_data["data"]), 3)

    def test_setting_detail(self):
        dzo = DzongkhagFactory()
        dadm = UserFactory(
            roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True)
        )
        ProfileFactory(user=dadm, dzongkhag=dzo)

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dadm.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"

        sf = SettingFactory(user=self.user1)

        url = reverse("settings", kwargs={"pk": sf.id})
        response = self.client.get(url)
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)

    def test_setting_delete(self):
        dzo = DzongkhagFactory()
        dadm = UserFactory(
            roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True)
        )
        ProfileFactory(user=dadm, dzongkhag=dzo)

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dadm.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"

        sf = SettingFactory(user=self.user1)

        url = reverse("settings", kwargs={"pk": sf.id})
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
