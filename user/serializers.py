from django.forms import <PERSON><PERSON>ield
from rest_framework import serializers
from file.models import Attachment
from file.serializers import AttachmentSerializer
from .models import Setting, User, Profile, Role
from drf_writable_nested.serializers import WritableNestedModelSerializer
from rest_framework.validators import UniqueValidator
from address.models import Dzongkhag, Gewog, Thromde, Village, Region
from common.constants import DZO_ROLES, DZONGKHAG_ROLES, REGION_ROLES, ROID_ROLES, THRM_ROLES, THROMDE_ROLES


class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ["id", "name", "description"]


class ProfileSerializer(WritableNestedModelSerializer):
    dzongkhag_name = serializers.CharField(source="dzongkhag.name", read_only=True)
    thromde_name = serializers.CharField(source="thromde.name", read_only=True)
    gewog_name = serializers.CharField(source="gewog.name", read_only=True)
    village_name = serializers.CharField(source="village.name", read_only=True)
    region_name = serializers.CharField(source="region.name", read_only=True)
    region_id = serializers.PrimaryKeyRelatedField(queryset=Region.objects.all().values_list("id", flat=True), required=False, allow_null=True)
    dzongkhag_id = serializers.PrimaryKeyRelatedField(queryset=Dzongkhag.objects.all().values_list("id", flat=True), required=False, allow_null=True)
    thromde_id = serializers.PrimaryKeyRelatedField(queryset=Thromde.objects.all().values_list("id", flat=True), required=False, allow_null=True)
    gewog_id = serializers.PrimaryKeyRelatedField(queryset=Gewog.objects.all().values_list("id", flat=True), required=False, allow_null=True)
    village_id = serializers.PrimaryKeyRelatedField(queryset=Village.objects.all().values_list("id", flat=True), required=False, allow_null=True)

    class Meta:
        model = Profile
        fields = [
            "dzongkhag_id",
            "dzongkhag_name",
            "thromde_id",
            "thromde_name",
            "gewog_id",
            "gewog_name",
            "village_id",
            "village_name",
            "region_id",
            "region_name",
            "dob",
            "photo",
        ]


class InviteProfileSerializer(serializers.ModelSerializer):
    region_name = serializers.CharField(source="region.name", read_only=True)
    dzongkhag_name = serializers.CharField(source="dzongkhag.name", read_only=True)
    thromde_name = serializers.CharField(source="thromde.name", read_only=True)
    gewog_name = serializers.CharField(source="gewog.name", read_only=True)
    village_name = serializers.CharField(source="village.name", read_only=True)
    region_id = serializers.PrimaryKeyRelatedField(queryset=Region.objects.all(), source="region", write_only=True, required=False)
    dzongkhag_id = serializers.PrimaryKeyRelatedField(queryset=Dzongkhag.objects.all(), source="dzongkhag", write_only=True, required=False)
    thromde_id = serializers.PrimaryKeyRelatedField(queryset=Thromde.objects.all(), source="thromde", write_only=True, required=False)
    gewog_id = serializers.PrimaryKeyRelatedField(queryset=Gewog.objects.all(), source="gewog", write_only=True, required=False)
    village_id = serializers.PrimaryKeyRelatedField(queryset=Village.objects.all(), source="village", write_only=True, required=False)

    class Meta:
        model = Profile
        fields = [
            "dzongkhag_id",
            "dzongkhag_name",
            "thromde_id",
            "thromde_name",
            "gewog_id",
            "gewog_name",
            "village_id",
            "village_name",
            "region_id",
            "region_name",
            "dob",
            "photo",
        ]


class UserSerializer(WritableNestedModelSerializer):
    profile = ProfileSerializer()
    roles = RoleSerializer(many=True)

    class Meta:
        model = User
        fields = [
            "id",
            "cid",
            "email",
            "roles",
            "username",
            "phone",
            "first_name",
            "last_name",
            "active",
            "is_active",
            "profile",
            "current_role",
        ]
        depth = 1


class UserUpdateSerializer(UserSerializer):
    roles = serializers.PrimaryKeyRelatedField(many=True, queryset=Role.objects.all(), required=False)

    def update(self, instance, validated_data):
        instance = super().update(instance, validated_data)
        if instance.current_role not in instance.roles.all():
            instance.current_role = instance.roles.first()
            instance.save()
        return instance


class InviteSerializer(WritableNestedModelSerializer):
    profile = InviteProfileSerializer(required=False)
    username = serializers.CharField(required=False, validators=[UniqueValidator(queryset=User.objects.all())])
    phone = serializers.CharField(required=False, validators=[UniqueValidator(queryset=User.objects.all())])
    cid = serializers.CharField(required=False, validators=[UniqueValidator(queryset=User.objects.all())])
    email = serializers.CharField(required=True, validators=[UniqueValidator(queryset=User.objects.all())])
    first_name = serializers.CharField(required=False)
    last_name = serializers.CharField(required=False)

    class Meta:
        model = User
        fields = ["id", "cid", "email", "roles", "username", "phone", "first_name", "last_name", "active", "profile"]
        extra_kwargs = {"password": {"write_only": True}}

    def validate(self, instance):
        current_user = self.context.get("request").user
        instance["is_superuser"] = False
        instance["is_active"] = False
        if current_user.roles.filter(name="user").exists():
            raise serializers.ValidationError({"error": "You are not authorized to invite users"})
        roles = [role[0] for role in Role.objects.all().values_list("name")]
        if len(set([x.name for x in instance.get("roles")]) & set(roles)) < 1:
            raise serializers.ValidationError({"error": "Invalid role"})
        role_name = instance["roles"][0].name
        profile = instance["profile"]

        if role_name in THROMDE_ROLES:
            if not profile.get("dzongkhag"):
                raise serializers.ValidationError({"error": "Dzongkhag cannot be blank"})
            elif not profile.get("thromde"):
                raise serializers.ValidationError({"error": "Thromde cannot be blank"})
        if role_name in DZONGKHAG_ROLES:
            if not profile.get("dzongkhag"):
                raise serializers.ValidationError({"error": "Dzongkhag cannot be blank"})
        if role_name in ("gup",):
            if not profile.get("dzongkhag"):
                raise serializers.ValidationError({"error": "Dzongkhag cannot be blank"})
            elif not profile.get("gewog"):
                raise serializers.ValidationError({"error": "Gewog cannot be blank"})
        if role_name in REGION_ROLES:
            if not profile.get("dzongkhag"):
                raise serializers.ValidationError({"error": "Dzongkhag cannot be blank"})
        return instance


class ProfilePhotoSerializer(serializers.ModelSerializer):
    photo = ImageField(required=False)

    class Meta:
        model = Profile
        fields = ["photo"]


class SettingSerializer(serializers.ModelSerializer):
    file = AttachmentSerializer(read_only=True)
    file_id = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="file", required=False)

    class Meta:
        model = Setting
        fields = ["id", "key", "value", "created_at", "user_id", "file_id", "file"]


class GenericNotificationRelatedField(serializers.RelatedField):
    def to_representation(self, value):
        import importlib

        value_class = value.__class__
        module_name = value_class.__module__.split(".")[0]
        class_name = value_class.__name__

        try:
            module = importlib.import_module(module_name + ".serializers")
            serializer_class = getattr(module, class_name + "Serializer")
            serializer_instance = serializer_class(value)
            return serializer_instance.data
        except (ImportError, AttributeError):
            pass


class NotificationSerializer(serializers.Serializer):
    id = serializers.IntegerField(read_only=True)
    unread = serializers.BooleanField(read_only=True)
    verb = serializers.CharField(read_only=True)
    description = serializers.CharField(read_only=True)
    timestamp = serializers.DateTimeField(read_only=True)
    naturaltime = serializers.CharField(read_only=True)
    naturalday = serializers.CharField(read_only=True)
    data = serializers.JSONField(read_only=True)
    actor_content_type = serializers.CharField(read_only=True)
    actor = GenericNotificationRelatedField(read_only=True)
    action_object_content_type = serializers.CharField(read_only=True)
    action_object = GenericNotificationRelatedField(read_only=True)
    target_content_type = serializers.CharField(read_only=True)
    target = GenericNotificationRelatedField(read_only=True)
    recipient = UserSerializer(User, read_only=True)
