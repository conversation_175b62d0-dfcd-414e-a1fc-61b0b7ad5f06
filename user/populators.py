from user.models import User
from django.db.models import Q


class UserPopulator:
    def __init__(self, current_user, params):
        self.records = User.objects.all().order_by("-created_at")
        self.current_user = current_user
        self.params = params

    def populate(self):
        role = self.params.get("role_id")
        dzo_id = self.params.get("dzongkhag_id")
        thrm_id = self.params.get("thromde_id")
        self.records = self.records.filter(roles__id=role) if role else self.records
        self.records = (
            self.records.filter(profile__dzongkhag_id=dzo_id)
            if dzo_id
            else self.records
        )
        self.records = (
            self.records.filter(profile__thromde_id=thrm_id)
            if thrm_id
            else self.records
        )
        return self.records
