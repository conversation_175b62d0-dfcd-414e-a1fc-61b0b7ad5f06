from django.db import models
from django.contrib.auth.models import AbstractUser
from django.contrib.auth.models import AbstractUser, <PERSON><PERSON><PERSON><PERSON><PERSON> as DefaultUserManager
from address.models import *
from file.models import Attachment
from user.helpers.user import UserHelper


class Role(models.Model):
    name = models.CharField(max_length=254, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)


class UserManager(DefaultUserManager):
    def create(self, *args, **kwargs):
        roles = kwargs.pop("roles", [])
        user = super().create(*args, **kwargs)
        if roles:
            user.current_role_id = roles[0]
            user.roles.set(roles)
            user.save()
        return user

    def _create_user(self, *args, **extra_fields):
        roles = extra_fields.pop("roles", [])
        user = super()._create_user(*args, **extra_fields)
        if roles:
            user.current_role_id = roles[0]
            user.roles.set(roles)
            user.save()
        return user

    def create_user(self, *args, **extra_fields):
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        return self._create_user(*args, **extra_fields)

    def create_superuser(self, *args, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        return self._create_user(*args, **extra_fields)


class User(AbstractUser, UserHelper):
    phone = models.CharField(max_length=24, null=True, blank=True)
    first_name = models.CharField(max_length=100, null=True, blank=True)
    last_name = models.CharField(max_length=100, null=True, blank=True)
    cid = models.CharField(max_length=11, null=True, blank=True, db_index=True)
    active = models.BooleanField(default=True)
    email = models.EmailField(max_length=254, unique=True, null=True, db_index=True)
    username = models.CharField(max_length=254, unique=True, null=True, blank=True)
    invited = models.BooleanField(default=False, null=True)
    invited_by_id = models.IntegerField(null=True, blank=True)
    roles = models.ManyToManyField(Role, blank=False, related_name="users")
    current_role = models.ForeignKey(Role, related_name="current_role", on_delete=models.SET_NULL, null=True, blank=True, unique=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = UserManager()

    REQUIRED_FIELDS = ["email", "phone", "cid", "roles"]

    def __str__(self):
        return str(self.__dict__)


class Profile(models.Model):
    region = models.ForeignKey(Region, null=True, blank=True, on_delete=models.SET_NULL)
    dzongkhag = models.ForeignKey(Dzongkhag, null=True, blank=True, on_delete=models.SET_NULL)
    gewog = models.ForeignKey(Gewog, null=True, blank=True, on_delete=models.SET_NULL)
    village = models.ForeignKey(Village, null=True, blank=True, on_delete=models.SET_NULL)
    thromde = models.ForeignKey(Thromde, null=True, blank=True, on_delete=models.SET_NULL)
    dob = models.DateField(null=True, blank=True)
    photo = models.ImageField(null=True, blank=True)
    user = models.OneToOneField(User, blank=False, related_name="profile", on_delete=models.CASCADE, primary_key=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)


class Setting(models.Model):
    user = models.ForeignKey(User, null=False, related_name="settings", on_delete=models.CASCADE)
    key = models.CharField(max_length=254, null=False, db_index=True)
    value = models.TextField(null=True, blank=True, db_index=True)
    file = models.ForeignKey(Attachment, related_name="files", null=True, blank=True, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
