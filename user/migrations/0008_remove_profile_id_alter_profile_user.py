# Generated by Django 4.1 on 2023-02-15 21:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("user", "0007_role_remove_user_role_user_roles"),
    ]

    operations = [
        migrations.RemoveField(model_name="profile", name="id",),
        migrations.AlterField(
            model_name="profile",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                primary_key=True,
                related_name="profile",
                serialize=False,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
