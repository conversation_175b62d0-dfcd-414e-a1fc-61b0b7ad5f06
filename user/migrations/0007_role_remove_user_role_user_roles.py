# Generated by Django 4.1 on 2023-02-15 21:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("user", "0006_user_role"),
    ]

    operations = [
        migrations.CreateModel(
            name="Role",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(blank=True, max_length=254, null=True)),
                ("description", models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.RemoveField(model_name="user", name="role",),
        migrations.AddField(
            model_name="user",
            name="roles",
            field=models.ManyToManyField(related_name="roles", to="user.role"),
        ),
    ]
