from cas_api.libs.pagination import Pagination
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status, generics, views, viewsets, serializers, exceptions
from cas_api.services.govtech_service import fetch_citizen_detail
from cas_api.services.ndi_service import create_proof_requests, fetch_ndi_access_token, process_ndi_info
from common.constants import MINISTRY_ROLES, REGION_ROLES, DZONGKHAG_ROLES, THROMDE_ROLES
from .serializers import *
from .models import *
from .populators import UserPopulator
from rest_framework.filters import SearchFilter
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken
import os, requests
from django.shortcuts import get_object_or_404
from notifications.models import Notification


class UserListView(generics.ListAPIView):
    serializer_class = UserSerializer
    filter_backends = [SearchFilter]
    search_fields = (
        "first_name",
        "last_name",
        "email",
        "current_role__name",
        "phone",
        "cid",
        "username",
        "profile__dzongkhag__name",
        "profile__gewog__name",
        "profile__village__name",
        "profile__thromde__name",
    )

    def get_queryset(self):
        return UserPopulator(self.request.user, self.request.query_params).populate()


class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = User.objects.all()

    def get_serializer_class(self):
        if self.request.method in ["PATCH", "PUT"]:
            return UserUpdateSerializer
        return UserSerializer


class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer

    @action(detail=True, methods=["get"])
    def profile(self, request, *args, **kwargs):
        user = request.user
        serializer = UserSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["put"])
    def set_current_role(self, request, *args, **kwargs):
        user = request.user
        role_id = request.data.get("role_id")
        if role_id not in user.roles.values_list("id", flat=True):
            return Response({"error": "Role was not assigned."}, status=status.HTTP_404_NOT_FOUND)
        user.current_role_id = role_id
        user.save()
        serializer = UserSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["put"])
    def upload_photo(self, request, *args, **kwargs):
        user = request.user
        photo = request.data.get("photo")
        profile, _ = Profile.objects.get_or_create(user=user)
        profile.photo = photo
        profile.save()
        serializer = UserSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=False, methods=["get"])
    def proof_requests(self, request, *args, **kwargs):
        res = create_proof_requests(request.data)

        return Response(res, status=res["status"] if res["status"] == 201 else status.HTTP_400_BAD_REQUEST)

    def citizen_detail(self, request, *args, **kwargs):
        response = fetch_citizen_detail(kwargs.get("cid"))

        return Response(response, status=response.get("status"))


class RoleListView(generics.ListAPIView):
    serializer_class = RoleSerializer
    filter_backends = [SearchFilter]
    search_fields = ("name", "description")

    def get_queryset(self):
        role = self.request.user.current_role
        roles = Role.objects.all().order_by("id")
        if role.name == "admin":
            return roles
        elif role.name in THROMDE_ROLES:
            return roles.filter(name__in=THROMDE_ROLES)
        elif role.name in DZONGKHAG_ROLES:
            return roles.filter(name__in=DZONGKHAG_ROLES)
        elif role.name in REGION_ROLES:
            return roles.filter(name__in=REGION_ROLES)
        elif role.name in MINISTRY_ROLES:
            return roles.filter(name__in=MINISTRY_ROLES)
        else:
            return roles.filter(name="user")


class EngineerListView(generics.ListAPIView):
    serializer_class = UserSerializer
    filter_backends = [SearchFilter]
    search_fields = (
        "first_name",
        "last_name",
        "email",
        "current_role__name",
        "phone",
        "cid",
        "username",
        "profile__dzongkhag__name",
        "profile__gewog__name",
        "profile__village__name",
        "profile__thromde__name",
    )

    def get_queryset(self):
        user = self.request.user
        role_names = self.request.query_params.getlist("role_name[]")
        if not role_names:
            raise exceptions.ValidationError({"error": "Please provide role name."})
        architects = User.objects.none()
        try:
            if user.current_role.name in THROMDE_ROLES:
                architects = User.objects.filter(roles__name__in=role_names, profile__dzongkhag=user.dzongkhag, profile__thromde=user.thromde).exclude(
                    roles__name__in=REGION_ROLES + DZONGKHAG_ROLES + MINISTRY_ROLES
                )
            elif user.current_role.name in REGION_ROLES:
                architects = User.objects.filter(roles__name__in=role_names, profile__region=user.region).exclude(roles__name__in=THROMDE_ROLES + DZONGKHAG_ROLES + MINISTRY_ROLES)
            elif user.current_role.name in DZONGKHAG_ROLES:
                architects = User.objects.filter(roles__name__in=role_names, profile__dzongkhag=user.dzongkhag).exclude(
                    roles__name__in=THROMDE_ROLES + REGION_ROLES + MINISTRY_ROLES
                )
            elif user.current_role.name in MINISTRY_ROLES:
                architects = architects = User.objects.filter(roles__name__in=role_names).exclude(roles__name__in=THROMDE_ROLES + REGION_ROLES + DZONGKHAG_ROLES)
        except Exception as e:
            pass
        return architects


class UserSettingView(generics.ListCreateAPIView):
    serializer_class = SettingSerializer

    def get_queryset(self):
        user_id = self.kwargs.get("user_id")
        return Setting.objects.filter(user_id=user_id).order_by("created_at")

    def perform_create(self, serializer):
        serializer.validated_data["user_id"] = self.request.user.id
        return super().perform_create(serializer)


class UserSettingPKView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = SettingSerializer
    queryset = Setting.objects.all()


class NdiTokenView(views.APIView):
    permission_classes = ()
    authentication_classes = ()

    def post(self, request):
        if not request.data:
            raise serializers.ValidationError({"error": "Invalid request"})
        jdata = request.data
        cid_data = jdata["data"]["requested_presentation"]["revealed_attrs"]
        data = {"cid": cid_data[os.getenv("NDI_IDENTITY")][0]["value"], "thread_id": jdata["pattern"]}
        try:
            user = process_ndi_info(data)
        except Exception as e:
            raise serializers.ValidationError({"error": str(e)}, status.HTTP_400_BAD_REQUEST)
        if user is not None:
            refresh = RefreshToken.for_user(user)
            return Response({"access": str(refresh.access_token), "refresh": str(refresh)}, status=status.HTTP_200_OK)
        else:
            raise serializers.ValidationError({"error": "Invalid user information."}, status.HTTP_400_BAD_REQUEST)


class NotificationView(views.APIView):
    pagination_class = Pagination

    def get(self, request, **kwargs):
        user_id = kwargs.get("pk") or request.user.id
        notifications_qs = Notification.objects.filter(recipient_id=user_id).order_by("-timestamp")
        unread_count = notifications_qs.unread().count()

        notification_id = kwargs.get("id")
        if notification_id:
            notification = get_object_or_404(notifications_qs, pk=notification_id)
            if notification.unread:
                notification.mark_as_read()
            serializer = NotificationSerializer(notification)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            paginator = self.pagination_class()
            paginated_notifications = paginator.paginate_queryset(notifications_qs, request)
            serializer = NotificationSerializer(paginated_notifications, many=True)
            return paginator.get_paginated_response(serializer.data, meta={"count": unread_count})
