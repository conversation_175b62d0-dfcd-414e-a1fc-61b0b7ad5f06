class UserHelper:
    @property
    def user_profile(self):
        return getattr(self, "profile", None)

    @property
    def name(self):
        name = f"{self.first_name or ''} {self.last_name or ''}".strip() if self.first_name or self.last_name else self.email
        return name

    @property
    def dzongkhag(self):
        return getattr(self.user_profile, "dzongkhag", None)

    @property
    def gewog(self):
        return getattr(self.user_profile, "gewog", None)

    @property
    def village(self):
        return getattr(self.user_profile, "village", None)

    @property
    def thromde(self):
        return getattr(self.user_profile, "thromde", None)

    @property
    def region(self):
        return getattr(self.user_profile, "region", None)

    @property
    def dzongkhag_id(self):
        return getattr(self.user_profile, "dzongkhag_id", None)

    @property
    def gewog_id(self):
        return getattr(self.user_profile, "gewog_id", None)

    @property
    def village_id(self):
        return getattr(self.user_profile, "village_id", None)

    @property
    def thromde_id(self):
        return getattr(self.user_profile, "thromde_id", None)

    @property
    def region_id(self):
        return getattr(self.user_profile, "region_id", None)
