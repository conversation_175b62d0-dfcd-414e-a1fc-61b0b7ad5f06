from django.urls import path, include
from . import views
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView, TokenVerifyView

urlpatterns = [
    path("accounts/", include("rest_registration.api.urls")),
    path("token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("ndi_token/", views.NdiTokenView.as_view(), name="ndi_token_pair"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("token/verify/", TokenVerifyView.as_view(), name="token_verify"),
    path("users/", views.UserListView.as_view(), name="users"),
    path("users/profile/", views.UserViewSet.as_view({"get": "profile"})),
    path("users/set_current_role/", views.UserViewSet.as_view({"put": "set_current_role"})),
    path("users/<pk>/", views.UserDetailView.as_view(), name="user_detail"),
    path("roles/", views.RoleListView.as_view(), name="role_list"),
    path("engineers/", views.EngineerListView.as_view(), name="engineers"),
    path("users/<pk>/upload_photo", views.UserViewSet.as_view({"put": "upload_photo"})),
    path("citizens/<cid>", views.UserViewSet.as_view({"get": "citizen_detail"})),
    path("users/<user_id>/settings/", views.UserSettingView.as_view(), name="settings"),
    path("settings/<pk>/", views.UserSettingPKView.as_view(), name="settings"),
    path("users/<pk>/notifications/", views.NotificationView.as_view(), name="notifications"),
    path("notifications/<id>", views.NotificationView.as_view(), name="notifications"),
    path("proof_requests/", views.UserViewSet.as_view({"post": "proof_requests"}, authentication_classes=[], permission_classes=[])),
]
