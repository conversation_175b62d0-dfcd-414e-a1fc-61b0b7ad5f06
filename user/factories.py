import factory

from file.factories import AttachmentFactory
from .models import *
from address.factories import *
from django.contrib.auth.hashers import make_password


class RoleFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Role

    name = factory.Faker("name")
    description = factory.Faker("text")


class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User

    phone = factory.Faker("phone_number")
    cid = factory.Faker("ssn")
    email = factory.Faker("email")
    username = factory.Faker("user_name")
    password = make_password("Dcpl@123")


class ProfileFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Profile

    dzongkhag = factory.SubFactory(DzongkhagFactory)
    gewog = factory.SubFactory(GewogFactory)
    village = factory.SubFactory(VillageFactory)
    thromde = factory.SubFactory(ThromdeFactory)
    dob = factory.Faker("date_of_birth")
    photo = factory.Faker("file_path", category="image")
    user = factory.SubFactory(UserFactory)


class SettingFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Setting

    key = factory.Faker("name")
    value = factory.Faker("ssn")
    file = factory.SubFactory(AttachmentFactory)
    user = factory.SubFactory(UserFactory)
