from rest_framework import generics, viewsets, response, status, exceptions
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.db.models import Q, Count, Case, When, IntegerField
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from django_fsm import can_proceed
from django_fsm_log.models import StateLog
from common.serializers import StateLogSerializer
from ongoing_construction.models import Inspection, InspectionReport, StopWorkOrder, CommitteeDecision, Penalty, Notice
from ongoing_construction.serializers import (
    InspectionSerializer,
    InspectionReportSerializer,
    StopWorkOrderSerializer,
    CommitteeDecisionSerializer,
    PenaltySerializer,
    NoticeSerializer,
)
from ongoing_construction.populators import InspectionPopulator
from ongoing_construction.enums import InspectionStatus, NoticeType


class InspectionView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating inspections
    """

    serializer_class = InspectionSerializer
    filter_backends = [SearchFilter]
    search_fields = (
        "serial_no",
        "state",
        "user__first_name",
        "user__last_name",
        "user__email",
        "task_pools__user__first_name",
        "task_pools__user__last_name",
        "permit__serial_no",
    )

    def get_queryset(self):
        return InspectionPopulator(self.request.user, self.request.query_params).populate().distinct()

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                serializer.validated_data["user_id"] = self.request.user.id
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class InspectionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting inspections
    """

    serializer_class = InspectionSerializer
    queryset = Inspection.objects.all()


class InspectionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for additional inspection actions
    """

    queryset = Inspection.objects.all()
    serializer_class = InspectionSerializer

    @action(detail=True, methods=["put"])
    def transition(self, request, **kwargs):
        """
        Handle state transitions for inspections
        """
        with transaction.atomic():
            try:
                instance = get_object_or_404(Inspection, pk=kwargs.get("pk"))
                action = request.data.pop("action")

                if not hasattr(instance, action) or not can_proceed(getattr(instance, action)):
                    return response.Response(
                        {"error": f"Invalid transition, cannot change state from `{instance.state}` using `{action}`"}, status=status.HTTP_422_UNPROCESSABLE_ENTITY
                    )
                if hasattr(instance, f"can_{action}") and not getattr(instance, f"can_{action}")(request.user):
                    return response.Response({"error": "You are not permitted to perform this action."}, status=status.HTTP_403_FORBIDDEN)
                if not action == "start" and instance.task_pools.filter(user=request.user, state="assigned").exists():
                    raise exceptions.ValidationError({"error": "You need to start your task first."})
                getattr(instance, action)(request.data, by=request.user)
                instance.save()
                serializer = InspectionSerializer(instance)
                return response.Response(serializer.data, status=status.HTTP_200_OK)
            except Exception as e:
                transaction.set_rollback(True)
                raise e

    @action(detail=True, methods=["get"])
    def activity_logs(self, request, **kwargs):
        """
        Get activity logs for an inspection
        """
        inspection = get_object_or_404(Inspection, pk=kwargs.get("pk"))
        logs = StateLog.objects.for_(inspection)
        serializer = StateLogSerializer(logs, many=True)
        return response.Response(serializer.data, status=status.HTTP_200_OK)


class InspectionReportView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating inspection reports
    """

    serializer_class = InspectionReportSerializer

    def get_queryset(self):
        inspection = get_object_or_404(Inspection, pk=self.kwargs.get("pk"))
        return inspection.reports.all()

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                serializer.validated_data["inspector_id"] = self.request.user.id
                inspection = serializer.validated_data["inspection"]
                if inspection.state == InspectionStatus.COMPLETED:
                    raise exceptions.ValidationError({"error": "Inspection is already completed. You cannot create a report."})
                elif not inspection.state == InspectionStatus.INSPECTED:
                    raise exceptions.ValidationError({"error": "You can only create inspection report after inspection is completed."})
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class InspectionReportDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting inspection reports
    """,

    serializer_class = InspectionReportSerializer
    queryset = InspectionReport.objects.all()


class StopWorkOrderView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating stop work orders
    """

    serializer_class = StopWorkOrderSerializer

    def get_queryset(self):
        inspection = get_object_or_404(Inspection, pk=self.kwargs.get("pk"))
        return inspection.stop_work_orders.all()

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                serializer.validated_data["issued_by_id"] = self.request.user.id
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class StopWorkOrderDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting stop work orders
    """

    serializer_class = StopWorkOrderSerializer
    queryset = StopWorkOrder.objects.all()


class CommitteeDecisionView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating committee decisions
    """

    serializer_class = CommitteeDecisionSerializer

    def get_queryset(self):
        inspection = get_object_or_404(Inspection, pk=self.kwargs.get("pk"))
        return inspection.committee_decisions.all()

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                serializer.validated_data["recorded_by_id"] = self.request.user.id
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class CommitteeDecisionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting committee decisions
    """

    serializer_class = CommitteeDecisionSerializer
    queryset = CommitteeDecision.objects.all()


class PenaltyView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating penalties
    """

    serializer_class = PenaltySerializer

    def get_queryset(self):
        inspection = get_object_or_404(Inspection, pk=self.kwargs.get("pk"))
        return inspection.penalties.all()

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                serializer.validated_data["imposed_by_id"] = self.request.user.id
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class PenaltyDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting penalties
    """

    serializer_class = PenaltySerializer
    queryset = Penalty.objects.all()


class NoticeView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating notices
    """

    serializer_class = NoticeSerializer

    def get_queryset(self):
        inspection = get_object_or_404(Inspection, pk=self.kwargs.get("pk"))
        return inspection.notices.all()

    def perform_create(self, serializer):
        serializer.save(issued_by=self.request.user)


class NoticeDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting notices
    """

    serializer_class = NoticeSerializer
    queryset = Notice.objects.all()
