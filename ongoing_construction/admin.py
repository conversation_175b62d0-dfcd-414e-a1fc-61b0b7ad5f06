from django.contrib import admin
from ongoing_construction.models import Inspection, InspectionReport, StopWorkOrder, CommitteeDecision, Penalty, Notice


# Register models
@admin.register(Inspection)
class InspectionAdmin(admin.ModelAdmin):
    list_display = ("serial_no", "permit", "user", "inspection_type", "state", "created_at")
    list_filter = ("state", "inspection_type")
    search_fields = ("serial_no", "user__first_name", "user__last_name", "permit__serial_no")
    date_hierarchy = "created_at"


@admin.register(InspectionReport)
class InspectionReportAdmin(admin.ModelAdmin):
    list_display = ("inspection", "inspector", "issue_type", "status", "created_at")
    list_filter = ("issue_type", "status")
    search_fields = ("inspection__serial_no", "inspector__first_name", "inspector__last_name")
    date_hierarchy = "created_at"


@admin.register(StopWorkOrder)
class StopWorkOrderAdmin(admin.ModelAdmin):
    list_display = ("inspection", "issued_by", "is_active", "issued_at", "released_at")
    list_filter = ("is_active",)
    search_fields = ("inspection__serial_no", "issued_by__first_name", "issued_by__last_name")
    date_hierarchy = "issued_at"


@admin.register(CommitteeDecision)
class CommitteeDecisionAdmin(admin.ModelAdmin):
    list_display = ("inspection", "recorded_by", "decided_at")
    search_fields = ("inspection__serial_no", "recorded_by__first_name", "recorded_by__last_name", "decision")
    date_hierarchy = "decided_at"


@admin.register(Penalty)
class PenaltyAdmin(admin.ModelAdmin):
    list_display = ("inspection", "amount", "imposed_by", "is_paid", "imposed_at", "paid_at")
    list_filter = ("is_paid",)
    search_fields = ("inspection__serial_no", "imposed_by__first_name", "imposed_by__last_name")
    date_hierarchy = "imposed_at"


@admin.register(Notice)
class NoticeAdmin(admin.ModelAdmin):
    list_display = ("inspection", "notice_type", "subject", "issued_by", "is_read", "issued_at")
    list_filter = ("notice_type", "is_read")
    search_fields = ("inspection__serial_no", "issued_by__first_name", "issued_by__last_name", "subject", "content")
    date_hierarchy = "issued_at"
