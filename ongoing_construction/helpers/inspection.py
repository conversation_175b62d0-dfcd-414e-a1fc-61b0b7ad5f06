from django.utils.crypto import get_random_string
from user.models import Role
from rest_framework.exceptions import ValidationError


class InspectionHelper:
    """
    Helper methods for the Inspection model
    """

    def generate_serial_no(self):
        """
        Generate a unique serial number for the inspection
        """
        if not self.serial_no:
            prefix = "CASOC"
            random_str = get_random_string(length=8, allowed_chars="0123456789")
            self.serial_no = f"{prefix}-{random_str}"
            self.save(update_fields=["serial_no"])
        return self.serial_no

    @property
    def nature(self):
        return self.permit.nature

    @property
    def dzongkhag(self):
        return self.permit.dzongkhag

    @property
    def thromde(self):
        return self.permit.thromde

    @property
    def gewog(self):
        return self.permit.gewog

    def can_assign_inspector(self, user):
        """
        Check if the user can schedule an inspection
        """
        return user.current_role.name in ["dce", "tce"]

    def can_schedule_inspection(self, user):
        """
        Check if the user can schedule an inspection
        """
        return True

    def can_complete_inspection(self, user):
        """
        Check if the user can complete the inspection
        """
        return user.current_role.name in ["tbi", "dbi"]

    def can_report_minor_issue(self, user):
        """
        Check if the user can report a minor issue
        """
        return user.current_role.name in ["tbi", "dbi"]

    def can_report_major_issue(self, user):
        """
        Check if the user can report a major issue
        """
        return user.current_role.name in ["tbi", "dbi"]

    def can_issue_stop_work(self, user):
        """
        Check if the user can issue a stop work order
        """
        return user.current_role.name in ["tbi", "dbi", "dro"]

    def can_request_committee_meeting(self, user):
        """
        Check if the user can request a committee meeting
        """
        return user.current_role.name in ["tbi", "dbi", "dro"]

    def can_record_committee_decision(self, user):
        """
        Check if the user can record a committee decision
        """
        return user.current_role.name in ["dce", "tce"]

    def can_require_changes(self, user):
        """
        Check if the user can require changes
        """
        return user.current_role.name in ["tbi", "dbi", "dro"]

    def can_verify_changes(self, user):
        """
        Check if the user can verify changes
        """
        return user.current_role.name in ["tbi", "dbi"]

    def can_impose_penalty(self, user):
        """
        Check if the user can impose a penalty
        """
        return user.current_role.name in ["dce", "tce"]

    def can_pay_penalty(self, user):
        """
        Check if the user can pay a penalty
        """
        return user.id == self.user_id

    def can_release_stop_work(self, user):
        """
        Check if the user can release a stop work order
        """
        return user.current_role.name in ["dro"]

    def can_complete(self, user):
        """
        Check if the user can complete the inspection process
        """
        return user.current_role.name in ["tbi", "dbi", "dro"]

    def can_reject(self, user):
        """
        Check if the user can reject the inspection
        """
        return user.current_role.name in ["tbi", "dbi", "dro"]

    def create_notice(self, notice_type, subject, content, issued_by):
        """
        Create a notice for the inspection
        """
        from ongoing_construction.models import Notice

        notice = Notice.objects.create(inspection=self, notice_type=notice_type, subject=subject, content=content, issued_by=issued_by)
        return notice

    def get_building_inspector(self):
        """
        Get the assigned building inspector from task_pools
        """
        building_inspector_task = self.task_pools.filter(role__name__in=["tbi", "dbi"]).first()
        return building_inspector_task.user if building_inspector_task else None

    def assign_building_inspector(self, inspector, by=None):
        """
        Assign a building inspector through task_pools
        """

        # Get the building inspector role
        bi_role = Role.objects.filter(name__in=["tbi", "dbi"]).first()
        if not bi_role:
            return False

        # Check if there's already a building inspector assigned
        existing_task = self.task_pools.filter(role__name__in=["tbi", "dbi"]).first()
        if existing_task:
            # Update the existing task
            existing_task.user = inspector
            existing_task.by = by
            existing_task.save()
        else:
            # Create a new task
            self.task_pools.create(user=inspector, role=bi_role, by=by, by_role=getattr(by, "current_role", None))

        return True

    def pay(self, payment=None):
        self.pay_penalty({}, by=self.user)
        self.save()

    def can_conflict_of_interest(self, user):
        return user.current_role.name in ["tbi", "dbi", "dro"]
