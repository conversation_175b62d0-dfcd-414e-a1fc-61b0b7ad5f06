from django.urls import path
from ongoing_construction import views

urlpatterns = [
    # Inspection URLs
    path("inspections/", views.InspectionView.as_view(), name="inspections"),
    path("inspections/<int:pk>/", views.InspectionDetailView.as_view(), name="inspection_detail"),
    path("inspections/<int:pk>/transition/", views.InspectionViewSet.as_view({"put": "transition"}), name="inspection_transition"),
    path("inspections/<int:pk>/activity_logs/", views.InspectionViewSet.as_view({"get": "activity_logs"}), name="inspection_activity_logs"),
    # Inspection Report URLs
    path("inspections/<int:pk>/inspection_reports/", views.InspectionReportView.as_view(), name="inspection_reports"),
    path("inspection_reports/<int:pk>/", views.InspectionReportDetailView.as_view(), name="inspection_report_detail"),
    # Stop Work Order URLs
    path("inspections/<int:pk>/stop_work_orders/", views.StopWorkOrderView.as_view(), name="stop_work_orders"),
    path("stop_work_orders/<int:pk>/", views.StopWorkOrderDetailView.as_view(), name="stop_work_order_detail"),
    # Committee Decision URLs
    path("inspections/<int:pk>/committee_decisions/", views.CommitteeDecisionView.as_view(), name="committee_decisions"),
    path("committee_decisions/<int:pk>/", views.CommitteeDecisionDetailView.as_view(), name="committee_decision_detail"),
    # Penalty URLs
    path("inspections/<int:pk>/penalties/", views.PenaltyView.as_view(), name="penalties"),
    path("penalties/<int:pk>/", views.PenaltyDetailView.as_view(), name="penalty_detail"),
    # Notice URLs
    path("inspections/<int:pk>/notices/", views.NoticeView.as_view(), name="notices"),
    path("notices/<int:pk>/", views.NoticeDetailView.as_view(), name="notice_detail"),
]
