from rest_framework import serializers
from drf_writable_nested.serializers import WritableNestedModelSerializer
from building.models import Permit
from file.models import Attachment
from file.serializers import AttachmentSerializer
from ongoing_construction.models import Inspection, InspectionReport, StopWorkOrder, CommitteeDecision, Penalty, Notice
from payment.serializers import PaymentSerializer
from common.serializers import TaskPoolSerializer


class InspectionSerializer(WritableNestedModelSerializer):
    """
    Serializer for Inspection model
    """

    permit_id = serializers.PrimaryKeyRelatedField(queryset=Permit.objects.all(), source="permit", required=True)
    user_name = serializers.CharField(source="user.name", read_only=True)
    permit_serial_no = serializers.CharField(source="permit.serial_no", read_only=True)
    inspection_type_display = serializers.CharField(source="get_inspection_type_display", read_only=True)
    state_display = serializers.CharField(source="get_state_display", read_only=True)
    payments = PaymentSerializer(many=True, read_only=True)

    def validate_permit_id(self, value):
        """
        Validate that the permit is approved before allowing inspection request
        Business Rule: After Building Permit has been approved, the applicant will be able to submit a request for inspection
        """
        if value.state != 'approved':
            raise serializers.ValidationError("Inspection can only be requested for approved building permits.")
        return value

    def validate_inspection_type(self, value):
        """
        Validate inspection type and require other_inspection_type for 'others'
        Business Rule: Inspection types must be from predefined list or specify custom type
        """
        if value == 'others' and not self.initial_data.get('other_inspection_type'):
            raise serializers.ValidationError("Please specify the inspection type when selecting 'Others'.")
        return value

    class Meta:
        model = Inspection
        fields = [
            "id",
            "permit_id",
            "permit_serial_no",
            "user_id",
            "user_name",
            "inspection_type",
            "inspection_type_display",
            "other_inspection_type",
            "remarks",
            "state",
            "state_display",
            "serial_no",
            "construction_id",
            "created_at",
            "updated_at",
            "scheduled_date",
            "schedule_remarks",
            "verification_remarks",
            "changes_required",
            "changes_verified",
            "committee_meeting_date",
            "committee_meeting_remarks",
            "payments",
            "is_adhoc",
        ]


class InspectionReportSerializer(WritableNestedModelSerializer):
    """
    Serializer for InspectionReport model
    """

    inspector_name = serializers.CharField(source="inspector.name", read_only=True)
    issue_type_display = serializers.CharField(source="get_issue_type_display", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    attachment_ids = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="attachments", many=True, required=False, write_only=True)
    attachments = AttachmentSerializer(many=True, required=False, read_only=True, allow_empty=True)
    site_visit_date = serializers.DateField(required=False, allow_null=True)
    site_visit_time = serializers.TimeField(required=False, allow_null=True)
    site_visit_location = serializers.CharField(required=False, allow_blank=True)

    def validate(self, data):
        """
        Business Rule Validation for Inspection Reports
        BR 6: If an issue is identified, the construction report along with images will be uploaded to system
        """
        issue_type = data.get('issue_type')
        attachments = data.get('attachments', [])

        # Require attachments for issue reports (images/documents)
        if issue_type in ['minor_issue', 'major_issue'] and not attachments:
            raise serializers.ValidationError({
                'attachments': 'Construction report with images must be uploaded when reporting issues.'
            })

        # Require detailed findings for major issues
        if issue_type == 'major_issue' and not data.get('findings'):
            raise serializers.ValidationError({
                'findings': 'Detailed findings must be provided for major issues.'
            })

        return data
    is_major_issue = serializers.BooleanField(required=False)
    forwarded_to_me = serializers.BooleanField(required=False)
    forwarded_to_committee = serializers.BooleanField(required=False)
    remarks = serializers.CharField(required=False, allow_blank=True)
    recommendations = serializers.CharField(required=False, allow_blank=True)
    inspection_id = serializers.PrimaryKeyRelatedField(queryset=Inspection.objects.all(), source="inspection", required=True)

    class Meta:
        model = InspectionReport
        fields = [
            "id",
            "inspection_id",
            "inspector_id",
            "inspector_name",
            "issue_type",
            "issue_type_display",
            "remarks",
            "recommendations",
            "status",
            "status_display",
            "created_at",
            "updated_at",
            "attachments",
            "site_visit_date",
            "site_visit_time",
            "site_visit_location",
            "is_major_issue",
            "forwarded_to_me",
            "forwarded_to_committee",
            "attachment_ids",
        ]


class StopWorkOrderSerializer(WritableNestedModelSerializer):
    """
    Serializer for StopWorkOrder model
    """

    issued_by_name = serializers.CharField(source="issued_by.name", read_only=True)
    released_by_name = serializers.CharField(source="released_by.name", read_only=True)
    attachments = AttachmentSerializer(many=True, required=False, read_only=True)
    attachment_ids = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="attachments", many=True, required=False, write_only=True)
    inspection_id = serializers.PrimaryKeyRelatedField(queryset=Inspection.objects.all(), source="inspection", required=True)

    class Meta:
        model = StopWorkOrder
        fields = [
            "id",
            "inspection_id",
            "issued_by_id",
            "issued_by_name",
            "reason",
            "is_active",
            "issued_at",
            "released_at",
            "released_by_id",
            "released_by_name",
            "attachments",
            "reminder_sent",
            "reminder_date",
            "reminder_remarks",
            "attachment_ids",
        ]


class CommitteeDecisionSerializer(WritableNestedModelSerializer):
    """
    Serializer for CommitteeDecision model
    """

    recorded_by_name = serializers.CharField(source="recorded_by.name", read_only=True)
    attachments = AttachmentSerializer(many=True, required=False, read_only=True)
    attachment_ids = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="attachments", many=True, required=False, write_only=True)
    inspection_id = serializers.PrimaryKeyRelatedField(queryset=Inspection.objects.all(), source="inspection", required=True)

    def validate(self, data):
        """
        Business Rule Validation for Committee Decisions
        BR: Committee meeting minutes must be uploaded before recording the decision
        """
        attachments = data.get('attachments', [])

        # Require meeting minutes attachment for committee decisions
        if not attachments:
            raise serializers.ValidationError({
                'attachments': 'Committee meeting minutes must be uploaded before recording the decision.'
            })

        # Require decision text
        if not data.get('decision'):
            raise serializers.ValidationError({
                'decision': 'Committee decision must be provided.'
            })

        # Require meeting date
        if not data.get('meeting_date'):
            raise serializers.ValidationError({
                'meeting_date': 'Committee meeting date must be specified.'
            })

        return data

    class Meta:
        model = CommitteeDecision
        fields = [
            "id",
            "inspection_id",
            "decision",
            "penalty_amount",
            "committee_remarks",
            "decided_at",
            "updated_at",
            "recorded_by_id",
            "recorded_by_name",
            "attachments",
            "meeting_date",
            "meeting_location",
            "attendees",
            "is_implemented",
            "implementation_date",
            "implementation_remarks",
            "attachment_ids",
        ]


class PenaltySerializer(WritableNestedModelSerializer):
    """
    Serializer for Penalty model
    """

    imposed_by_name = serializers.CharField(source="imposed_by.name", read_only=True)
    payments = PaymentSerializer(many=True, required=False, read_only=True)
    attachment_ids = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="attachments", many=True, required=False, write_only=True)
    inspection_id = serializers.PrimaryKeyRelatedField(queryset=Inspection.objects.all(), source="inspection", required=True)

    class Meta:
        model = Penalty
        fields = [
            "id",
            "inspection_id",
            "amount",
            "reason",
            "is_paid",
            "imposed_at",
            "paid_at",
            "imposed_by_id",
            "imposed_by_name",
            "payments",
            "payment_reference",
            "payment_method",
            "payment_remarks",
            "attachment_ids",
        ]


class NoticeSerializer(WritableNestedModelSerializer):
    """
    Serializer for Notice model
    """

    issued_by_name = serializers.CharField(source="issued_by.name", read_only=True)
    responded_by_name = serializers.CharField(source="responded_by.name", read_only=True)
    notice_type_display = serializers.CharField(source="get_notice_type_display", read_only=True)
    attachments = AttachmentSerializer(many=True, required=False, read_only=True)
    attachment_ids = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="attachments", many=True, required=False, write_only=True)
    inspection_id = serializers.PrimaryKeyRelatedField(queryset=Inspection.objects.all(), source="inspection", required=True)

    class Meta:
        model = Notice
        fields = [
            "id",
            "inspection_id",
            "notice_type",
            "notice_type_display",
            "subject",
            "content",
            "issued_by_id",
            "issued_by_name",
            "issued_at",
            "attachments",
            "is_read",
            "read_at",
            "response",
            "response_at",
            "responded_by_id",
            "responded_by_name",
            "attachment_ids",
        ]
