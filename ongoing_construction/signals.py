from django.db.models.signals import post_save
from django.dispatch import receiver
from django_fsm.signals import post_transition
from notifications.signals import notify
from common.models import TaskPool
from ongoing_construction.models import Inspection, InspectionReport, StopWorkOrder, CommitteeDecision, Penalty, Notice
from ongoing_construction.enums import InspectionStatus, NoticeType
from ongoing_construction.mailers import (
    notify_inspector_assigned,
    notify_inspection_requested,
    notify_inspection_scheduled,
    notify_minor_issue,
    notify_major_issue,
    notify_stop_work_order,
    notify_committee_meeting,
    notify_committee_decision,
    notify_changes_required,
    notify_changes_verified,
    notify_penalty_imposed,
    notify_stop_work_released,
    notify_inspection_completed,
    notify_notice,
    notify_payment,
    notify_conflict_of_interest,
    notify_resubmitted,
)
from common.helpers import determine_assignee_user, generate_payment
from cas_api.middleware.current_request import get_current_request
from user.models import Role


@receiver(post_save, sender=Inspection)
def inspection_created(sender, instance, created, **kwargs):
    """
    Signal handler for when an inspection is created
    """
    by = getattr(get_current_request(), "user", None)
    if created:
        if instance.permit and instance.permit.construction_id:
            instance.construction_id = instance.permit.construction_id
        instance.initial(by=instance.user)
        instance.generate_serial_no()
        instance.save()
        notify.send(instance.user, recipient=instance.user, verb="created", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}")
        try:
            if instance.permit.nature == "urban":
                chief_user, chief_role = determine_assignee_user("tce", dzo=instance.permit.dzongkhag, thrm=instance.permit.thromde)
            else:
                chief_user, chief_role = determine_assignee_user("dce", dzo=instance.permit.dzongkhag)
            instance.task_pools.create(user=chief_user, role=chief_role, by=by or instance.user, by_role=getattr(by, "current_role", None))
            notify.send(instance.user, recipient=chief_user, verb="requested", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}")
        except Exception as e:
            print(f"Error creating task pool for chief: {e}")
            pass
        notify_inspection_requested.delay(instance.id)
    elif not created:
        by = getattr(instance, "by", None) or getattr(get_current_request(), "user", None)
        if getattr(by, "id", None) == instance.user_id and instance.state == "pending_change":
            change_tasks = instance.task_pools.filter(state="pending_change")
            instance.resubmit({}, by=by)
            instance.save()
            for task in change_tasks:
                task.assign({}, by=by)
                task.save()
                notify_resubmitted.delay(instance.id, task.user.id)
                notify.send(
                    instance.by,
                    recipient=instance.user,
                    verb=f"resubmitted",
                    action_object=instance,
                    target=instance.permit,
                    url=f"/services/ongoing-construction/{instance.id}",
                )


@receiver(post_transition, sender=Inspection)
def inspection_transitioned(instance, name, source, target, **kwargs):
    """
    Signal handler for when an inspection transitions state
    """
    notify.send(
        instance.by, recipient=instance.user, verb=f"changed state to {target}", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}"
    )
    by = getattr(get_current_request(), "user", None)

    if name == "assign_inspector":
        role_name = by.current_role.name
        if role_name == "dce":
            user, role = determine_assignee_user("dbi", user_id=instance.inspector_id, dzo=instance.dzongkhag)
        elif role_name == "tce":
            user, role = determine_assignee_user("tbi", user_id=instance.inspector_id, thrm=instance.thromde, dzo=instance.dzongkhag)
        bi_task = instance.task_pools.filter(user=user, role=role).first()
        if bi_task:
            bi_task.assign(getattr(instance, "data", {}), by=instance.by)
            bi_task.save()
        else:
            instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
        task = instance.task_pools.filter(user=by, state="in_progress").first()
        if task:
            task.approve(getattr(instance, "data", {}), by=by)
            task.save()
        notify_inspector_assigned.delay(instance.id, user.id)
        notify.send(instance.by, recipient=user, verb="assigned", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}")
    elif name == "schedule_inspection":
        building_inspector = instance.get_building_inspector()
        if building_inspector:
            chief_task = instance.task_pools.filter(role__name__in=["tce", "dce"], state__in=["assigned", "in_progress"]).first()
            if chief_task:
                chief_task.approve({}, by=instance.by)
                chief_task.save()
            notify.send(
                instance.by,
                recipient=building_inspector,
                verb="scheduled inspection",
                action_object=instance,
                target=instance.permit,
                url=f"/services/ongoing-construction/{instance.id}",
            )
        notify_inspection_scheduled.delay(instance.id)
    elif name == "report_minor_issue":
        notify.send(
            instance.by, recipient=instance.user, verb="reported minor issue", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}"
        )
        report = instance.reports.last()
        if report:
            notify_minor_issue.delay(instance.id, report.id)
    elif name == "report_major_issue":
        notify.send(
            instance.by, recipient=instance.user, verb="reported major issue", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}"
        )
        try:
            rname = "tbi" if instance.nature == "urban" else "dbi"
            bi_task = instance.task_pools.filter(role__name=rname, state__in=["assigned", "in_progress"]).first()
            if bi_task:
                bi_task.approve({}, by=instance.by)
                bi_task.save()
            notify.send(
                instance.by,
                recipient=instance.user,
                verb="reported major issue",
                action_object=instance,
                target=instance.permit,
                url=f"/services/ongoing-construction/{instance.id}",
            )
        except Exception as e:
            print(f"Error creating task pool for ME/DRO: {e}")
        report = instance.reports.last()
        if report:
            notify_major_issue.delay(instance.id, report.id)

    elif name == "issue_stop_work":
        recipients = [instance.user]
        building_inspector = instance.get_building_inspector()
        if building_inspector:
            recipients.append(building_inspector)
        for recipient in recipients:
            notify.send(
                instance.by, recipient=recipient, verb="issued stop work order", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}"
            )
        stop_work_order = instance.stop_work_orders.last()
        if stop_work_order:
            notify_stop_work_order.delay(instance.id, stop_work_order.id)
    elif name == "request_committee_meeting":
        try:
            bi_task = instance.task_pools.filter(role__name="tbi" if instance.nature == "urban" else "dbi", state__in=["assigned", "in_progress"]).first()
            if bi_task:
                bi_task.approve({}, by=instance.by)
                bi_task.save()
            task = instance.task_pools.filter(role__name="hoi" if instance.nature == "urban" else "dro").first()
            if task:
                chief_user = task.user
                task.assign({}, by=instance.by)
                task.save()
            else:
                role_name = "hoi" if instance.nature == "urban" else "dro"
                chief_user, chief_role = determine_assignee_user(role_name, dzo=instance.permit.dzongkhag)
                instance.task_pools.create(user=chief_user, role=chief_role, by=instance.by, by_role=getattr(instance.by, "current_role", None))
            notify.send(
                instance.by,
                recipient=chief_user,
                verb="requested committee meeting",
                action_object=instance,
                target=instance.permit,
                url=f"/services/ongoing-construction/{instance.id}",
            )
        except Exception as e:
            print(f"Error creating task pool for committee: {e}")
            pass
        notify_committee_meeting.delay(instance.id)
    elif name == "record_committee_decision":
        task = instance.task_pools.filter(role__name="hoi" if instance.nature == "urban" else "dro", state="in_progress").first()
        if task:
            task.approve({}, by=instance.by)
            task.save()
        notify.send(
            instance.by,
            recipient=instance.user,
            verb="recorded committee decision",
            action_object=instance,
            target=instance.permit,
            url=f"/services/ongoing-construction/{instance.id}",
        )
        role_name = "tce" if instance.nature == "urban" else "dce"
        chief_task = instance.task_pools.filter(role__name=role_name, state="in_progress").first()
        if chief_task:
            chief_task.assign({}, by=instance.by)
            chief_task.save()
        committee_decision = instance.committee_decisions.last()
        if committee_decision:
            notify_committee_decision.delay(instance.id, committee_decision.id)
    elif name == "require_changes":
        notify.send(
            instance.by, recipient=instance.user, verb="required changes", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}"
        )
        building_inspector = instance.get_building_inspector()
        if not building_inspector:
            rname = "tbi" if instance.nature == "urban" else "dbi"
            bi_role = Role.objects.filter(name=rname).first()
            if bi_role:
                instance.task_pools.create(user=building_inspector, role=bi_role, by=by, by_role=getattr(by, "current_role", None))
        else:
            task = instance.task_pools.filter(user=building_inspector, role=bi_role).first()
            if task:
                task.assign({}, by=instance.by)
                task.save()
        notify_changes_required.delay(instance.id)
    elif name == "verify_changes":
        notify.send(
            instance.by, recipient=instance.user, verb="verified changes", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}"
        )
        rname = "tbi" if instance.nature == "urban" else "dbi"
        bi_task = instance.task_pools.filter(role__name=rname, state__in=["assigned", "in_progress"]).first()
        if bi_task:
            bi_task.approve({}, by=instance.by)
            bi_task.save()
        notify_changes_verified.delay(instance.id)
    elif name == "impose_penalty":
        notify.send(
            instance.by, recipient=instance.user, verb="imposed penalty", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}"
        )
        user_role = Role.objects.filter(name="applicant").first()
        if user_role:
            instance.task_pools.create(user=instance.user, role=user_role, by=by, by_role=getattr(by, "current_role", None))
        penalty = instance.penalties.last()
        if penalty:
            notify_penalty_imposed.delay(instance.id, penalty.id)
        if penalty.amount > 0:
            generate_payment("ip", penalty.amount, instance)
            notify_payment.delay(instance.id)
        else:
            instance.pay()
    elif name == "pay_penalty":
        recipients = [instance.user]
        building_inspector = instance.get_building_inspector()
        if building_inspector:
            recipients.append(building_inspector)
        try:
            chief_user = instance.task_pools.filter(role__name="tce" if instance.nature == "urban" else "dce").first().user
            recipients.append(chief_user)
        except Exception:
            pass
        for recipient in recipients:
            notify.send(instance.by, recipient=recipient, verb="paid penalty", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}")
    elif name == "release_stop_work":
        recipients = [instance.user]
        building_inspector = instance.get_building_inspector()
        if building_inspector:
            recipients.append(building_inspector)
        for recipient in recipients:
            notify.send(
                instance.by,
                recipient=recipient,
                verb="released stop work order",
                action_object=instance,
                target=instance.permit,
                url=f"/services/ongoing-construction/{instance.id}",
            )
        notify_stop_work_released.delay(instance.id)
    elif name == "complete":
        notify.send(
            instance.by, recipient=instance.user, verb="completed inspection", action_object=instance, target=instance.permit, url=f"/services/ongoing-construction/{instance.id}"
        )
        notify_inspection_completed.delay(instance.id)
    elif name == "conflict_of_interest":
        role_name = "tce" if instance.nature == "urban" else "dce"
        task = instance.task_pools.filter(role__name=role_name).first()
        if task:
            task.assign(getattr(instance, "data", {}), by=by)
            task.save()
        my_task = instance.task_pools.filter(user=by, state="in_progress").first()
        if my_task:
            my_task.close(getattr(instance, "data", {}), by=by)
            my_task.save()
        notify.send(
            instance.by,
            recipient=task.user,
            verb="reported conflict of interest",
            action_object=instance,
            target=instance.permit,
            url=f"/services/ongoing-construction/{instance.id}",
        )
        notify_conflict_of_interest.delay(instance.id, task.user.id)


@receiver(post_save, sender=InspectionReport)
def inspection_report_created(instance, created, **kwargs):
    """
    Signal handler for when an inspection report is created
    """
    by = getattr(get_current_request(), "user", None)
    if created:
        recipients = [instance.inspection.user]
        try:
            role_name = "tce" if instance.inspection.nature == "urban" else "dce"
            chief_user, _ = determine_assignee_user(role_name, dzo=instance.inspection.dzongkhag, thrm=instance.inspection.thromde)
            recipients.append(chief_user)
        except Exception:
            pass
        for recipient in recipients:
            url = f"/inspection_reports/{instance.id}"
            notify.send(instance.inspector, recipient=recipient, verb="created inspection report", action_object=instance, target=instance.inspection, url=url)
        inspection = instance.inspection
        if inspection.state == InspectionStatus.INSPECTED:
            if instance.issue_type == "no_issue":
                inspection.complete({"remarks": "No issues found"}, by=instance.inspector)
                inspection.save()
                notify_inspection_completed.delay(inspection.id)
            elif instance.issue_type == "minor_issue":
                role_name = "dro" if inspection.permit.nature == "rural" else "hoi"
                user, role = determine_assignee_user(role_name, dzo=inspection.permit.dzongkhag, thrm=inspection.permit.thromde)
                inspection.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
                inspection.report_minor_issue({"recommendations": instance.recommendations}, by=instance.inspector)
                inspection.save()
                notify_minor_issue.delay(inspection.id, instance.id)
            elif instance.issue_type == "major_issue":
                inspection.report_major_issue({"issue_description": instance.remarks}, by=instance.inspector)
                inspection.save()
                notify_major_issue.delay(inspection.id, instance.id)


@receiver(post_save, sender=StopWorkOrder)
def stop_work_order_created(instance, created, **kwargs):
    """
    Signal handler for when a stop work order is created
    """
    if created:
        url = f"/stop_work_orders/{instance.id}"
        notify.send(instance.issued_by, recipient=instance.inspection.user, verb="issued stop work order", action_object=instance, target=instance.inspection, url=url)
        instance.inspection.create_notice(
            notice_type=NoticeType.STOP_WORK_ORDER,
            subject="Stop Work Order Issued",
            content=f"A stop work order has been issued for your construction: {instance.reason}",
            issued_by=instance.issued_by,
        )
        inspection = instance.inspection
        if inspection.state == "major_issue":
            inspection.issue_stop_work({"reason": instance.reason}, by=instance.issued_by)
            inspection.save()
        notify_stop_work_order.delay(instance.inspection.id, instance.id)


@receiver(post_save, sender=CommitteeDecision)
def committee_decision_created(instance, created, **kwargs):
    """
    Signal handler for when a committee decision is created
    """
    if created:
        recipients = [instance.inspection.user]
        building_inspector = instance.inspection.get_building_inspector()
        if building_inspector:
            recipients.append(building_inspector)
        for recipient in recipients:
            url = f"/committee-decisions/{instance.id}"
            notify.send(instance.recorded_by, recipient=recipient, verb="recorded committee decision", action_object=instance, target=instance.inspection, url=url)
        instance.inspection.create_notice(
            notice_type=NoticeType.COMMITTEE_DECISION,
            subject="Committee Decision",
            content=f"The committee has made a decision regarding your construction: {instance.decision}",
            issued_by=instance.recorded_by,
        )
        inspection = instance.inspection
        if inspection.state == "committee_meeting_requested":
            inspection.record_committee_decision({"decision": instance.decision}, by=instance.recorded_by)
            inspection.save()
        notify_committee_decision.delay(instance.inspection.id, instance.id)


@receiver(post_save, sender=Penalty)
def penalty_created(instance, created, **kwargs):
    """
    Signal handler for when a penalty is created
    """
    if created:
        url = f"/penalties/{instance.id}"
        notify.send(instance.imposed_by, recipient=instance.inspection.user, verb="imposed penalty", action_object=instance, target=instance.inspection, url=url)
        instance.inspection.create_notice(
            notice_type=NoticeType.RECOMMENDATION,
            subject="Penalty Imposed",
            content=f"A penalty of {instance.amount} has been imposed: {instance.reason}",
            issued_by=instance.imposed_by,
        )
        inspection = instance.inspection
        if inspection.state == "committee_decision":
            inspection.impose_penalty({"penalty_amount": instance.amount, "penalty_reason": instance.reason}, by=instance.imposed_by)
            inspection.save()
        notify_penalty_imposed.delay(instance.inspection.id, instance.id)


@receiver(post_save, sender=Notice)
def notice_created(instance, created, **kwargs):
    """
    Signal handler for when a notice is created
    """
    if created:
        url = f"/notices/{instance.id}"
        verb = f"sent {instance.get_notice_type_display().lower()}"
        notify.send(instance.issued_by, recipient=instance.inspection.user, verb=verb, action_object=instance, target=instance.inspection, url=url)
        notify_notice.delay(instance.id)


@receiver(post_transition, sender=TaskPool)
def task_pool_transition(sender, instance, name, source, target, **kwargs):
    if not isinstance(instance.poolable, Inspection):
        return
    inspection = instance.poolable
    by = getattr(get_current_request(), "user", None)
    if name == "change":
        inspection.require_changes({"changes_required": instance.data.get("remarks")}, by=by)
        inspection.save()
        notify.send(instance.by, recipient=inspection.user, verb="request change", action_object=instance, target=inspection, url=f"/services/ongoing-construction/{inspection.id}")
    elif name == "approve":
        if getattr(getattr(by, "current_role", None), "name") in ["tbi", "dbi"]:
            inspection.complete({"remarks": instance.data.get("remarks")}, by=by)
            inspection.save()
            notify.send(
                instance.by, recipient=inspection.user, verb="complete inspection", action_object=instance, target=inspection, url=f"/services/ongoing-construction/{inspection.id}"
            )
    elif name == "reject":
        inspection.reject({"remarks": instance.remarks}, by=by)
        inspection.save()
        notify.send(
            instance.by, recipient=inspection.user, verb="reject inspection", action_object=instance, target=inspection, url=f"/services/ongoing-construction/{inspection.id}"
        )
