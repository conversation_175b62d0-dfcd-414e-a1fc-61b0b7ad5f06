from http.client import CONFLICT
from django.db import models
from django.utils.translation import gettext_lazy as _


class InspectionStatus(models.TextChoices):
    INITIATED = "initiated", _("Initiated")
    REQUESTED = "requested", _("Requested")
    SCHEDULED = "scheduled", _("Scheduled")
    INSPECTED = "inspected", _("Inspected")
    INSPECTOR_ASSIGNED = "inspector_assigned", _("Inspector Assigned")
    MINOR_ISSUE = "minor_issue", _("Minor Issue")
    MAJOR_ISSUE = "major_issue", _("Major Issue")
    STOP_WORK_ISSUED = "stop_work_issued", _("Stop Work Issued")
    COMMITTEE_MEETING_REQUESTED = "committee_meeting_requested", _("Committee Meeting Requested")
    COMMITTEE_DECISION = "committee_decision", _("Committee Decision")
    CHANGES_REQUIRED = "changes_required", _("Changes Required")
    CHANGES_VERIFIED = "changes_verified", _("Changes Verified")
    PENALTY_IMPOSED = "penalty_imposed", _("Penalty Imposed")
    PENALTY_PAID = "penalty_paid", _("Penalty Paid")
    STOP_WORK_RELEASED = "stop_work_released", _("Stop Work Released")
    RESUBMITTED = "resubmitted", _("Re-submitted")
    COMPLETED = "completed", _("Completed")
    REJECTED = "rejected", _("Rejected")
    CONFLICT_OF_INTEREST = "conflict_of_interest", _("Conflict of Interest")


class InspectionType(models.TextChoices):
    BUILDING_LAYOUT = "building_layout", _("Building Layout")
    FOUNDATION_BASEMENT = "foundation_basement", _("Foundation/Basement")
    COLUMN_FOOTINGS = "column_footings", _("Column and Footings")
    SLAB_BEAM = "slab_beam", _("Slab and beam")
    ROOF_TRUSS = "roof_truss", _("Roof truss")
    SEPTIC_TANK = "septic_tank", _("Septic tank and soak pit/sewerage connection")
    OTHERS = "others", _("Others")


class IssueType(models.TextChoices):
    NO_ISSUE = "no_issue", _("No Issue")
    MINOR_ISSUE = "minor_issue", _("Minor Issue")
    MAJOR_ISSUE = "major_issue", _("Major Issue")


class InspectionReportStatus(models.TextChoices):
    PENDING = "pending", _("Pending")
    COMPLETED = "completed", _("Completed")
    REJECTED = "rejected", _("Rejected")


class NoticeType(models.TextChoices):
    STOP_WORK_ORDER = "stop_work_order", _("Stop Work Order")
    RECOMMENDATION = "recommendation", _("Recommendation")
    REMINDER = "reminder", _("Reminder")
    COMMITTEE_DECISION = "committee_decision", _("Committee Decision")
    RELEASE_STOP_WORK = "release_stop_work", _("Release Stop Work Order")
