from django.db import models
from django.contrib.contenttypes.fields import GenericRelation
from django_fsm import <PERSON>SMField
from django.utils.translation import gettext_lazy as _
from user.models import User

from ongoing_construction.enums import InspectionStatus, InspectionReportStatus, InspectionType, IssueType, NoticeType
from ongoing_construction.transitions.inspection import InspectionTransition
from ongoing_construction.helpers.inspection import InspectionHelper


class Inspection(models.Model, InspectionHelper, InspectionTransition):
    """
    Model for construction inspections
    """

    permit = models.ForeignKey("building.Permit", on_delete=models.CASCADE, related_name="inspections")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="inspections")
    by = models.ForeignKey(User, related_name="assigned_inspections", null=True, blank=True, on_delete=models.SET_NULL)
    inspection_type = models.CharField(max_length=50, choices=InspectionType.choices)
    other_inspection_type = models.Char<PERSON>ield(max_length=100, null=True, blank=True)
    remarks = models.TextField(null=True, blank=True)
    state = FSMField(max_length=50, choices=InspectionStatus.choices, default=InspectionStatus.INITIATED)
    task_pools = GenericRelation("common.TaskPool", related_query_name="inspection")
    payments = GenericRelation("payment.Payment", related_query_name="inspection")
    serial_no = models.CharField(max_length=50, null=True, blank=True)
    construction_id = models.CharField(max_length=100, null=True, blank=True, help_text="Unique identifier for tracking construction across all modules")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    scheduled_date = models.DateField(null=True, blank=True)
    schedule_remarks = models.TextField(null=True, blank=True)
    verification_remarks = models.TextField(null=True, blank=True)

    # Fields for tracking changes required and verification
    changes_required = models.TextField(null=True, blank=True)
    changes_verified = models.BooleanField(default=False)

    # Fields for tracking committee meeting
    committee_meeting_date = models.DateTimeField(null=True, blank=True)
    committee_meeting_remarks = models.TextField(null=True, blank=True)

    # Add this field to clarify ad-hoc inspection triggers
    is_adhoc = models.BooleanField(default=False, help_text="Indicates if this is an ad-hoc inspection.")

    class Meta:
        verbose_name = _("inspection")
        verbose_name_plural = _("inspections")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["created_at"]),
            models.Index(fields=["state"]),
            models.Index(fields=["permit"]),
            models.Index(fields=["construction_id"]),
            models.Index(fields=["inspection_type"]),
        ]

    def __str__(self):
        return f"Inspection {self.id} - {self.get_inspection_type_display()}"


class InspectionReport(models.Model):
    """
    Model for inspection reports created by Building Inspectors
    """

    inspection = models.ForeignKey(Inspection, on_delete=models.CASCADE, related_name="reports")
    inspector = models.ForeignKey(User, on_delete=models.CASCADE, related_name="inspection_reports")
    issue_type = models.CharField(max_length=20, choices=IssueType.choices, default=IssueType.NO_ISSUE)
    remarks = models.TextField(null=True, blank=True)
    recommendations = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=InspectionReportStatus.choices, default=InspectionReportStatus.PENDING)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    attachments = models.ManyToManyField("file.Attachment", related_name="inspection_report", blank=True)

    # Additional fields for site inspection
    site_visit_date = models.DateField(null=True, blank=True)
    site_visit_time = models.TimeField(null=True, blank=True)
    site_visit_location = models.CharField(max_length=255, null=True, blank=True)

    # Fields for tracking major/minor issues
    is_major_issue = models.BooleanField(default=False)
    forwarded_to_me = models.BooleanField(default=False)
    forwarded_to_committee = models.BooleanField(default=False)

    class Meta:
        verbose_name = _("inspection report")
        verbose_name_plural = _("inspection reports")
        ordering = ["-created_at"]

    def __str__(self):
        return f"Inspection Report {self.id} - {self.get_issue_type_display()}"


class StopWorkOrder(models.Model):
    """
    Model for stop work orders issued by Building Inspectors or ME/DRO
    """

    inspection = models.ForeignKey(Inspection, on_delete=models.CASCADE, related_name="stop_work_orders")
    issued_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name="issued_stop_work_orders")
    reason = models.TextField()
    is_active = models.BooleanField(default=True)
    issued_at = models.DateTimeField(auto_now_add=True)
    released_at = models.DateTimeField(null=True, blank=True)
    released_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name="released_stop_work_orders")
    attachments = models.ManyToManyField("file.Attachment", related_name="stop_work_order")

    # Additional fields for reminders
    reminder_sent = models.BooleanField(default=False)
    reminder_date = models.DateTimeField(null=True, blank=True)
    reminder_remarks = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = _("stop work order")
        verbose_name_plural = _("stop work orders")
        ordering = ["-issued_at"]

    def __str__(self):
        return f"Stop Work Order {self.id} - {self.inspection.id}"


class CommitteeDecision(models.Model):
    """
    Model for committee decisions on major issues
    """

    inspection = models.ForeignKey(Inspection, on_delete=models.CASCADE, related_name="committee_decisions")
    decision = models.TextField()
    penalty_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    committee_remarks = models.TextField(null=True, blank=True)  # Renamed from changes_required to avoid duplication
    decided_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    recorded_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name="recorded_committee_decisions")
    attachments = models.ManyToManyField("file.Attachment", related_name="committee_decision")

    # Additional fields for committee meeting
    meeting_date = models.DateTimeField(null=True, blank=True)
    meeting_location = models.CharField(max_length=255, null=True, blank=True)
    attendees = models.TextField(null=True, blank=True)

    # Fields for tracking decision implementation
    is_implemented = models.BooleanField(default=False)
    implementation_date = models.DateTimeField(null=True, blank=True)
    implementation_remarks = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = _("committee decision")
        verbose_name_plural = _("committee decisions")
        ordering = ["-decided_at"]

    def __str__(self):
        return f"Committee Decision {self.id} - {self.inspection.id}"


class Penalty(models.Model):
    """
    Model for penalties imposed on applicants
    """

    inspection = models.ForeignKey(Inspection, on_delete=models.CASCADE, related_name="penalties")
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    reason = models.TextField()
    is_paid = models.BooleanField(default=False)
    imposed_at = models.DateTimeField(auto_now_add=True)
    paid_at = models.DateTimeField(null=True, blank=True)
    imposed_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name="imposed_penalties")
    payments = GenericRelation("payment.Payment", related_query_name="penalty")

    # Additional fields for payment tracking
    payment_reference = models.CharField(max_length=100, null=True, blank=True)
    payment_method = models.CharField(max_length=50, null=True, blank=True)
    payment_remarks = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = _("penalty")
        verbose_name_plural = _("penalties")
        ordering = ["-imposed_at"]

    def __str__(self):
        return f"Penalty {self.id} - {self.amount}"


class Notice(models.Model):
    """
    Model for notices sent to applicants (Stop Work Orders, Recommendations, Reminders)
    """

    inspection = models.ForeignKey(Inspection, on_delete=models.CASCADE, related_name="notices")
    notice_type = models.CharField(max_length=50, choices=NoticeType.choices)
    subject = models.CharField(max_length=255)
    content = models.TextField()
    issued_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name="issued_notices")
    issued_at = models.DateTimeField(auto_now_add=True)
    attachments = models.ManyToManyField("file.Attachment", related_name="notice")

    # For tracking if the notice has been read/acknowledged
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)

    # For tracking responses to notices
    response = models.TextField(null=True, blank=True)
    response_at = models.DateTimeField(null=True, blank=True)
    responded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name="notice_responses")

    class Meta:
        verbose_name = _("notice")
        verbose_name_plural = _("notices")
        ordering = ["-issued_at"]

    def __str__(self):
        return f"{self.get_notice_type_display()} - {self.subject}"
