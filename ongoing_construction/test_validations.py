"""
Test cases for consolidated validation system
"""

from django.test import TestCase
from unittest.mock import Mock
from rest_framework import serializers
from ongoing_construction.validations import (
    InspectionValidations,
    InspectionReportValidations,
    StopWorkOrderValidations,
    CommitteeDecisionValidations,
    PenaltyValidations,
    NoticeValidations
)


class TestInspectionValidations(TestCase):
    """Test cases for InspectionValidations"""

    def test_validate_permit_approval_success(self):
        """Test permit approval validation with approved permit"""
        permit = Mock()
        permit.state = 'approved'
        
        result = InspectionValidations.validate_permit_approval(permit)
        self.assertEqual(result, permit)

    def test_validate_permit_approval_failure(self):
        """Test permit approval validation with unapproved permit"""
        permit = Mock()
        permit.state = 'pending'
        
        with self.assertRaises(serializers.ValidationError) as context:
            InspectionValidations.validate_permit_approval(permit)
        
        self.assertIn("approved building permits", str(context.exception))

    def test_validate_inspection_type_others_success(self):
        """Test inspection type validation with 'others' and specification"""
        result = InspectionValidations.validate_inspection_type('others', 'Custom inspection type')
        self.assertEqual(result, 'others')

    def test_validate_inspection_type_others_failure(self):
        """Test inspection type validation with 'others' but no specification"""
        with self.assertRaises(serializers.ValidationError) as context:
            InspectionValidations.validate_inspection_type('others', None)
        
        self.assertIn("specify the inspection type", str(context.exception))

    def test_validate_inspector_assignment_success(self):
        """Test inspector assignment validation with authorized user"""
        user = Mock()
        user.current_role.name = 'dce'
        
        result = InspectionValidations.validate_inspector_assignment(user, 123)
        self.assertEqual(result, 123)

    def test_validate_inspector_assignment_unauthorized(self):
        """Test inspector assignment validation with unauthorized user"""
        user = Mock()
        user.current_role.name = 'applicant'
        
        with self.assertRaises(serializers.ValidationError) as context:
            InspectionValidations.validate_inspector_assignment(user, 123)
        
        self.assertIn("Only Chief", str(context.exception))

    def test_validate_inspector_assignment_missing_id(self):
        """Test inspector assignment validation with missing inspector ID"""
        user = Mock()
        user.current_role.name = 'dce'
        
        with self.assertRaises(serializers.ValidationError) as context:
            InspectionValidations.validate_inspector_assignment(user, None)
        
        self.assertIn("inspector ID is required", str(context.exception))


class TestInspectionReportValidations(TestCase):
    """Test cases for InspectionReportValidations"""

    def test_validate_issue_report_attachments_success(self):
        """Test attachment validation with attachments for issue report"""
        attachments = ['attachment1.jpg', 'attachment2.pdf']
        
        result = InspectionReportValidations.validate_issue_report_attachments('minor_issue', attachments)
        self.assertEqual(result, attachments)

    def test_validate_issue_report_attachments_failure(self):
        """Test attachment validation without attachments for issue report"""
        with self.assertRaises(serializers.ValidationError) as context:
            InspectionReportValidations.validate_issue_report_attachments('major_issue', [])
        
        error_dict = context.exception.detail
        self.assertIn('attachments', error_dict)
        self.assertIn('images must be uploaded', str(error_dict['attachments']))

    def test_validate_major_issue_findings_success(self):
        """Test findings validation with findings for major issue"""
        findings = "Detailed findings about the major issue"
        
        result = InspectionReportValidations.validate_major_issue_findings('major_issue', findings)
        self.assertEqual(result, findings)

    def test_validate_major_issue_findings_failure(self):
        """Test findings validation without findings for major issue"""
        with self.assertRaises(serializers.ValidationError) as context:
            InspectionReportValidations.validate_major_issue_findings('major_issue', None)
        
        error_dict = context.exception.detail
        self.assertIn('findings', error_dict)


class TestStopWorkOrderValidations(TestCase):
    """Test cases for StopWorkOrderValidations"""

    def test_validate_stop_work_reason_success(self):
        """Test stop work reason validation with valid reason"""
        reason = "Safety violation detected"
        
        result = StopWorkOrderValidations.validate_stop_work_reason(reason)
        self.assertEqual(result, reason)

    def test_validate_stop_work_reason_failure(self):
        """Test stop work reason validation with empty reason"""
        with self.assertRaises(serializers.ValidationError) as context:
            StopWorkOrderValidations.validate_stop_work_reason("")
        
        self.assertIn("provide a reason", str(context.exception))

    def test_validate_stop_work_permissions_success(self):
        """Test stop work permissions with authorized user"""
        user = Mock()
        user.current_role.name = 'tbi'
        
        result = StopWorkOrderValidations.validate_stop_work_permissions(user)
        self.assertTrue(result)

    def test_validate_stop_work_permissions_failure(self):
        """Test stop work permissions with unauthorized user"""
        user = Mock()
        user.current_role.name = 'applicant'
        
        with self.assertRaises(serializers.ValidationError) as context:
            StopWorkOrderValidations.validate_stop_work_permissions(user)
        
        self.assertIn("Building Inspectors", str(context.exception))


class TestCommitteeDecisionValidations(TestCase):
    """Test cases for CommitteeDecisionValidations"""

    def test_validate_committee_meeting_minutes_success(self):
        """Test meeting minutes validation with attachments"""
        attachments = ['minutes.pdf']
        
        result = CommitteeDecisionValidations.validate_committee_meeting_minutes(attachments)
        self.assertEqual(result, attachments)

    def test_validate_committee_meeting_minutes_failure(self):
        """Test meeting minutes validation without attachments"""
        with self.assertRaises(serializers.ValidationError) as context:
            CommitteeDecisionValidations.validate_committee_meeting_minutes([])
        
        error_dict = context.exception.detail
        self.assertIn('attachments', error_dict)
        self.assertIn('meeting minutes', str(error_dict['attachments']))

    def test_validate_committee_decision_text_success(self):
        """Test decision text validation with valid text"""
        decision = "Committee approves the construction with modifications"
        
        result = CommitteeDecisionValidations.validate_committee_decision_text(decision)
        self.assertEqual(result, decision)

    def test_validate_committee_decision_text_failure(self):
        """Test decision text validation with empty text"""
        with self.assertRaises(serializers.ValidationError) as context:
            CommitteeDecisionValidations.validate_committee_decision_text("")
        
        error_dict = context.exception.detail
        self.assertIn('decision', error_dict)


class TestPenaltyValidations(TestCase):
    """Test cases for PenaltyValidations"""

    def test_validate_penalty_amount_success(self):
        """Test penalty amount validation with positive amount"""
        amount = 1000.50
        
        result = PenaltyValidations.validate_penalty_amount(amount)
        self.assertEqual(result, amount)

    def test_validate_penalty_amount_failure(self):
        """Test penalty amount validation with zero or negative amount"""
        with self.assertRaises(serializers.ValidationError) as context:
            PenaltyValidations.validate_penalty_amount(0)
        
        self.assertIn("greater than zero", str(context.exception))

        with self.assertRaises(serializers.ValidationError) as context:
            PenaltyValidations.validate_penalty_amount(-100)
        
        self.assertIn("greater than zero", str(context.exception))


class TestNoticeValidations(TestCase):
    """Test cases for NoticeValidations"""

    def test_validate_notice_content_success(self):
        """Test notice content validation with valid content"""
        content = "This is a valid notice content"
        
        result = NoticeValidations.validate_notice_content(content)
        self.assertEqual(result, content)

    def test_validate_notice_content_failure(self):
        """Test notice content validation with empty content"""
        with self.assertRaises(serializers.ValidationError) as context:
            NoticeValidations.validate_notice_content("")
        
        self.assertIn("Notice content is required", str(context.exception))

    def test_validate_notice_permissions_success(self):
        """Test notice permissions with authorized user"""
        user = Mock()
        user.current_role.name = 'dro'
        
        result = NoticeValidations.validate_notice_permissions(user, 'stop_work_reminder')
        self.assertTrue(result)

    def test_validate_notice_permissions_failure(self):
        """Test notice permissions with unauthorized user"""
        user = Mock()
        user.current_role.name = 'applicant'
        
        with self.assertRaises(serializers.ValidationError) as context:
            NoticeValidations.validate_notice_permissions(user, 'stop_work_reminder')
        
        self.assertIn("Only ME/DRO", str(context.exception))
