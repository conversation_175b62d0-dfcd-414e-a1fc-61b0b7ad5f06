from django_fsm import transition
from django_fsm_log.decorators import fsm_log_by
from rest_framework.exceptions import ValidationError
from django_fsm_log.decorators import fsm_log_description
from ongoing_construction.validations import (
    InspectionValidations,
    StopWorkOrderValidations,
    CommitteeDecisionValidations
)


class InspectionTransition:
    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="initiated", target="requested")
    def initial(self, by=None, description=None):
        """
        Initial transition when applicant submits an inspection
        """
        description.set("Inspection initiated.")
        self.by = by

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["requested", "resubmitted", "conflict_of_interest"], target="inspector_assigned")
    def assign_inspector(self, data, by=None, description=None):
        inspector_id = data.get("inspector_id")

        # Use consolidated validation
        InspectionValidations.validate_inspector_assignment(by, inspector_id)

        self.by = by
        self.data = data
        self.inspector_id = inspector_id
        description.set(data.get("remarks", f"Inspection assigned to Building inspector by {by.name}"))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="inspector_assigned", target="scheduled")
    def schedule_inspection(self, data, by=None, description=None):
        """
        Schedule the inspection (no attachment required at this stage)
        """
        if not data.get("scheduled_date"):
            raise ValidationError({"error": "You must provide a scheduled date for the inspection."})
        self.scheduled_date = data.get("scheduled_date")
        self.schedule_remarks = data.get("schedule_remarks", "")
        description.set(f"Inspection scheduled for {self.scheduled_date}")
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="resubmitted")
    def resubmit(self, data, by=None, description=None):
        """
        Building Inspector resubmit the inspection
        """
        description.set("Inspection resubmitted")
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["requested", "scheduled"], target="inspected")
    def complete_inspection(self, data, by=None, description=None):
        """
        Building Inspector completes the inspection
        """
        description.set(data.get("remarks", "Inspection completed"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["inspected", "scheduled"], target="minor_issue")
    def report_minor_issue(self, data, by=None, description=None):
        """
        Building Inspector reports a minor issue
        """
        if not data.get("recommendations"):
            raise ValidationError({"error": "You must provide recommendations for the minor issue."})

        self.changes_required = data.get("recommendations")
        description.set(f"Minor issue reported: {data.get('recommendations')}")
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["inspected", "scheduled"], target="major_issue")
    def report_major_issue(self, data, by=None, description=None):
        """
        Building Inspector reports a major issue
        """
        if not data.get("issue_description"):
            raise ValidationError({"error": "You must provide a description of the major issue."})

        description.set(f"Major issue reported: {data.get('issue_description')}")
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="major_issue", target="stop_work_issued")
    def issue_stop_work(self, data, by=None, description=None):
        """
        Building Inspector or ME/DRO issues a stop work order
        """
        reason = data.get("reason")

        # Use consolidated validation
        StopWorkOrderValidations.validate_stop_work_reason(reason)
        StopWorkOrderValidations.validate_stop_work_permissions(by)

        description.set(f"Stop work order issued: {reason}")
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="major_issue", target="committee_meeting_requested")
    def request_committee_meeting(self, data, by=None, description=None):
        """
        Building Inspector requests a committee meeting for major issues
        """
        description.set(data.get("remarks", "Committee meeting requested"))
        self.committee_meeting_remarks = data.get("remarks", "")
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="committee_meeting_requested", target="committee_decision")
    def record_committee_decision(self, data, by=None, description=None):
        """
        Record the committee's decision (enforce minutes upload)
        """
        decision = data.get("decision")

        # Use consolidated validation
        CommitteeDecisionValidations.validate_committee_decision_permissions(by)
        CommitteeDecisionValidations.validate_committee_decision_text(decision)

        # Enforce at least one attachment (minutes) for committee decision
        if not self.committee_decisions.exists() or not any(d.attachments.exists() for d in self.committee_decisions.all()):
            raise ValidationError({"attachments": "Committee meeting minutes must be uploaded before recording the decision."})

        description.set(f"Committee decision: {decision}")
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["minor_issue", "committee_decision"], target="changes_required")
    def require_changes(self, data, by=None, description=None):
        """
        Building Inspector or Committee requires changes to be made
        """
        if not data.get("changes_required"):
            raise ValidationError({"error": "You must specify the changes required."})

        self.changes_required = data.get("changes_required")
        description.set(f"Changes required: {data.get('changes_required')}")
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="changes_required", target="changes_verified")
    def verify_changes(self, data, by=None, description=None):
        """
        Building Inspector verifies that required changes have been made
        """
        self.changes_verified = True
        self.verification_remarks = data.get("verification_remarks", "")
        description.set(data.get("verification_remarks", "Changes verified"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="committee_decision", target="penalty_imposed")
    def impose_penalty(self, data, by=None, description=None):
        """
        Committee or Chief imposes a penalty
        """
        if not data.get("penalty_amount"):
            raise ValidationError({"error": "You must specify the penalty amount."})

        description.set(f"Penalty of {data.get('penalty_amount')} imposed")
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="penalty_imposed", target="penalty_paid")
    def pay_penalty(self, data, by=None, description=None):
        """
        Applicant pays the penalty
        """
        description.set("Penalty paid")
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["stop_work_issued", "penalty_paid", "changes_verified"], target="stop_work_released")
    def release_stop_work(self, data, by=None, description=None):
        """
        ME/DRO releases the stop work order
        """
        description.set(data.get("remarks", "Stop work order released"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["inspected", "stop_work_released", "changes_verified"], target="completed")
    def complete(self, data, by=None, description=None):
        """
        Inspection process is completed
        """
        description.set(data.get("remarks", "Inspection completed"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="rejected")
    def reject(self, data, by=None, description=None):
        """
        Reject the inspection
        """
        if not data.get("reject_remarks"):
            raise ValidationError({"error": "You should provide rejection remarks."})

        description.set(data.get("reject_remarks"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="inspector_assigned", target="conflict_of_interest")
    def conflict_of_interest(self, data, by=None, description=None):
        description.set(data.get("remarks"))
        self.by = by
        self.data = data
