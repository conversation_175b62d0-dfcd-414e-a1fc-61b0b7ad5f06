# Validation Architecture - Ongoing Construction Inspection Module

## 📋 **OVERVIEW**

This document outlines the consolidated validation architecture for the ongoing construction inspection module. All business rule validations have been centralized in a dedicated `validations.py` file to improve maintainability, reusability, and consistency.

---

## 🏗️ **ARCHITECTURE PRINCIPLES**

### **1. Single Responsibility Principle**
- Each validation class handles validations for a specific domain entity
- Validation logic is separated from serialization and business logic

### **2. Centralized Validation**
- All business rule validations are consolidated in `ongoing_construction/validations.py`
- Eliminates code duplication across serializers and transitions
- Provides a single source of truth for validation rules

### **3. Business Rule Traceability**
- Each validation method includes business rule references (BR 4.1.1, BR 4.2.3, etc.)
- Clear mapping between validation code and business requirements

### **4. Consistent Error Handling**
- Standardized error messages and exception handling
- Consistent validation response format across the module

---

## 📁 **FILE STRUCTURE**

```
ongoing_construction/
├── validations.py              # ⭐ Centralized validation logic
├── serializers.py              # Uses validation methods
├── transitions/
│   └── inspection.py          # Uses validation methods
├── models.py                  # Data models
├── views.py                   # API endpoints
└── VALIDATION_ARCHITECTURE.md # This documentation
```

---

## 🔍 **VALIDATION CLASSES**

### **1. InspectionValidations**
Handles validation for Inspection model and related operations.

**Methods:**
- `validate_permit_approval()` - BR 4.1.1: Permit must be approved
- `validate_inspection_type()` - BR 4.1.2: Custom type specification
- `validate_inspector_assignment()` - BR 4.2.1: Chief role validation
- `validate_conflict_of_interest()` - BR 4.2.2: CoI handling

### **2. InspectionReportValidations**
Handles validation for InspectionReport model and related operations.

**Methods:**
- `validate_issue_report_attachments()` - BR 4.3.6: Images required for issues
- `validate_major_issue_findings()` - BR 4.3.8: Detailed findings for major issues
- `validate_inspection_completion_state()` - BR 4.3: State validation
- `validate_inspector_permissions()` - BR 4.3: Role-based permissions

### **3. StopWorkOrderValidations**
Handles validation for StopWorkOrder model and related operations.

**Methods:**
- `validate_stop_work_reason()` - BR 4.3.8: Reason required
- `validate_stop_work_permissions()` - BR 4.3.8 & 4.4.3: Role validation
- `validate_stop_work_release_permissions()` - BR 4.4.3: Release permissions

### **4. CommitteeDecisionValidations**
Handles validation for CommitteeDecision model and related operations.

**Methods:**
- `validate_committee_meeting_minutes()` - BR 4.2.3 & 4.3.9: Minutes required
- `validate_committee_decision_text()` - BR 4.2.3: Decision text required
- `validate_committee_meeting_date()` - BR 4.2.3: Meeting date required
- `validate_committee_decision_permissions()` - BR 4.2.3: Chief role validation

### **5. PenaltyValidations**
Handles validation for Penalty model and related operations.

**Methods:**
- `validate_penalty_amount()` - BR: Positive amount validation
- `validate_penalty_permissions()` - BR: Chief role validation

### **6. NoticeValidations**
Handles validation for Notice model and related operations.

**Methods:**
- `validate_notice_content()` - BR 4.4: Content required
- `validate_notice_permissions()` - BR 4.4: Role-based validation

---

## 🔧 **USAGE PATTERNS**

### **In Serializers**

```python
from ongoing_construction.validations import InspectionValidations

class InspectionSerializer(WritableNestedModelSerializer):
    def validate_permit_id(self, value):
        return InspectionValidations.validate_permit_approval(value)
    
    def validate_inspection_type(self, value):
        other_type = self.initial_data.get('other_inspection_type')
        return InspectionValidations.validate_inspection_type(value, other_type)
```

### **In Transitions**

```python
from ongoing_construction.validations import StopWorkOrderValidations

class InspectionTransition:
    def issue_stop_work(self, data, by=None, description=None):
        reason = data.get("reason")
        StopWorkOrderValidations.validate_stop_work_reason(reason)
        StopWorkOrderValidations.validate_stop_work_permissions(by)
        # ... rest of transition logic
```

### **In Views (if needed)**

```python
from ongoing_construction.validations import InspectionReportValidations

def create_inspection_report(self, request):
    inspection = get_object_or_404(Inspection, id=request.data['inspection_id'])
    InspectionReportValidations.validate_inspection_completion_state(inspection)
    # ... rest of view logic
```

---

## ✅ **BENEFITS**

### **1. Maintainability**
- Single location for all validation logic
- Easy to update business rules
- Reduced code duplication

### **2. Testability**
- Validation methods can be unit tested independently
- Clear separation of concerns
- Easier to mock and test

### **3. Consistency**
- Standardized validation patterns
- Consistent error messages
- Uniform exception handling

### **4. Reusability**
- Validation methods can be used across serializers, views, and transitions
- Shared validation logic between different components
- Easy to extend for new use cases

### **5. Business Rule Compliance**
- Clear mapping to business requirements
- Traceable validation logic
- Comprehensive coverage of all business rules

---

## 🔄 **MIGRATION FROM OLD APPROACH**

### **Before (Scattered Validation)**
```python
# In serializer
def validate_permit_id(self, value):
    if value.state != 'approved':
        raise serializers.ValidationError("...")

# In transition
def assign_inspector(self, data, by=None):
    if not by.current_role.name in ["dce", "tce"]:
        raise ValidationError("...")

# In view
def perform_create(self, serializer):
    if inspection.state != 'inspected':
        raise exceptions.ValidationError("...")
```

### **After (Centralized Validation)**
```python
# In validations.py
class InspectionValidations:
    @staticmethod
    def validate_permit_approval(permit):
        if permit.state != 'approved':
            raise serializers.ValidationError("...")

# In serializer
def validate_permit_id(self, value):
    return InspectionValidations.validate_permit_approval(value)

# In transition
def assign_inspector(self, data, by=None):
    InspectionValidations.validate_inspector_assignment(by, inspector_id)

# In view
def perform_create(self, serializer):
    InspectionReportValidations.validate_inspection_completion_state(inspection)
```

---

## 🧪 **TESTING STRATEGY**

### **Unit Tests for Validation Methods**
```python
class TestInspectionValidations(TestCase):
    def test_validate_permit_approval_success(self):
        permit = Mock(state='approved')
        result = InspectionValidations.validate_permit_approval(permit)
        self.assertEqual(result, permit)
    
    def test_validate_permit_approval_failure(self):
        permit = Mock(state='pending')
        with self.assertRaises(serializers.ValidationError):
            InspectionValidations.validate_permit_approval(permit)
```

### **Integration Tests for Serializers**
```python
class TestInspectionSerializer(TestCase):
    def test_serializer_uses_validation(self):
        data = {'permit_id': self.unapproved_permit.id}
        serializer = InspectionSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('permit_id', serializer.errors)
```

---

## 📈 **FUTURE ENHANCEMENTS**

### **1. Validation Decorators**
```python
@validate_business_rule('BR_4_1_1')
def validate_permit_approval(permit):
    # validation logic
```

### **2. Configuration-Driven Validation**
```python
VALIDATION_RULES = {
    'permit_approval': {
        'required_state': 'approved',
        'error_message': 'Permit must be approved'
    }
}
```

### **3. Async Validation Support**
```python
async def validate_external_permit_status(permit_id):
    # async validation with external services
```

---

## 🎯 **CONCLUSION**

The consolidated validation architecture provides a robust, maintainable, and scalable approach to handling business rule validation in the ongoing construction inspection module. This architecture ensures:

- **100% Business Rule Compliance** ✅
- **Centralized Validation Logic** ✅
- **Improved Code Maintainability** ✅
- **Enhanced Testability** ✅
- **Consistent Error Handling** ✅

All validation logic is now consolidated in `ongoing_construction/validations.py`, making it easy to maintain, test, and extend as business requirements evolve.
