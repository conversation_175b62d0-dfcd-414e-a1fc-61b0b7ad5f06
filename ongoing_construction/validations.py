"""
Business Rule Validations for Ongoing Construction Inspection Module

This module consolidates all validation logic for the ongoing construction inspection system
to ensure compliance with business requirements and maintain data integrity.

Business Rules Reference:
- BR 4.1: Applicant Requirements
- BR 4.2: Chief <PERSON><PERSON><PERSON><PERSON>/Dzong<PERSON>g Requirements  
- BR 4.3: Building Inspector Requirements
- BR 4.4: Municipal Engineer/DRO Requirements
"""

from rest_framework import serializers
from django.core.exceptions import ValidationError as DjangoValidationError


class InspectionValidations:
    """
    Validation methods for Inspection model and related operations
    """

    @staticmethod
    def validate_permit_approval(permit):
        """
        BR 4.1.1: After Building Permit has been approved, the applicant will be able to submit a request for inspection
        
        Args:
            permit: Permit instance
            
        Raises:
            serializers.ValidationError: If permit is not approved
        """
        if permit.state != 'approved':
            raise serializers.ValidationError(
                "Inspection can only be requested for approved building permits."
            )
        return permit

    @staticmethod
    def validate_inspection_type(inspection_type, other_inspection_type=None):
        """
        BR 4.1.2: Inspection types must be from predefined list or specify custom type
        
        Args:
            inspection_type: Selected inspection type
            other_inspection_type: Custom inspection type when 'others' is selected
            
        Raises:
            serializers.ValidationError: If 'others' selected without specification
        """
        if inspection_type == 'others' and not other_inspection_type:
            raise serializers.ValidationError(
                "Please specify the inspection type when selecting 'Others'."
            )
        return inspection_type

    @staticmethod
    def validate_inspector_assignment(user, inspector_id):
        """
        BR 4.2.1: Only Chief (DCE/TCE) can assign inspectors
        
        Args:
            user: User attempting to assign inspector
            inspector_id: ID of inspector being assigned
            
        Raises:
            serializers.ValidationError: If user lacks permission
        """
        if not user.current_role.name in ["dce", "tce"]:
            raise serializers.ValidationError(
                "Only Chief (DCE/TCE) can assign building inspectors."
            )
        
        if not inspector_id:
            raise serializers.ValidationError(
                "Building inspector ID is required for assignment."
            )
        
        return inspector_id

    @staticmethod
    def validate_conflict_of_interest(inspector, permit):
        """
        BR 4.2.2: Handle conflict of interest between inspector and applicant
        
        Args:
            inspector: Inspector being assigned
            permit: Permit for inspection
            
        Raises:
            serializers.ValidationError: If conflict of interest exists
        """
        # Add specific conflict of interest logic here
        # This could check relationships, previous interactions, etc.
        if hasattr(permit, 'user') and inspector.id == permit.user.id:
            raise serializers.ValidationError(
                "Inspector cannot inspect their own construction project."
            )
        
        return True


class InspectionReportValidations:
    """
    Validation methods for InspectionReport model and related operations
    """

    @staticmethod
    def validate_issue_report_attachments(issue_type, attachments):
        """
        BR 4.3.6: If an issue is identified, the construction report along with images will be uploaded to system
        
        Args:
            issue_type: Type of issue being reported
            attachments: List of attachments
            
        Raises:
            serializers.ValidationError: If attachments missing for issue reports
        """
        if issue_type in ['minor_issue', 'major_issue'] and not attachments:
            raise serializers.ValidationError({
                'attachments': 'Construction report with images must be uploaded when reporting issues.'
            })
        return attachments

    @staticmethod
    def validate_major_issue_findings(issue_type, findings):
        """
        BR 4.3.8: Major issues require detailed findings and documentation
        
        Args:
            issue_type: Type of issue being reported
            findings: Detailed findings text
            
        Raises:
            serializers.ValidationError: If findings missing for major issues
        """
        if issue_type == 'major_issue' and not findings:
            raise serializers.ValidationError({
                'findings': 'Detailed findings must be provided for major issues.'
            })
        return findings

    @staticmethod
    def validate_inspection_completion_state(inspection):
        """
        BR 4.3: Validate inspection state before creating report
        
        Args:
            inspection: Inspection instance
            
        Raises:
            serializers.ValidationError: If inspection not in correct state
        """
        if inspection.state == 'completed':
            raise serializers.ValidationError(
                "Inspection is already completed. You cannot create a report."
            )
        elif inspection.state != 'inspected':
            raise serializers.ValidationError(
                "You can only create inspection report after inspection is completed."
            )
        return inspection

    @staticmethod
    def validate_inspector_permissions(user, inspection):
        """
        BR 4.3: Validate inspector has permission to create report
        
        Args:
            user: User attempting to create report
            inspection: Inspection instance
            
        Raises:
            serializers.ValidationError: If user lacks permission
        """
        if not user.current_role.name in ["tbi", "dbi"]:
            raise serializers.ValidationError(
                "Only Building Inspectors (TBI/DBI) can create inspection reports."
            )
        return True


class StopWorkOrderValidations:
    """
    Validation methods for StopWorkOrder model and related operations
    """

    @staticmethod
    def validate_stop_work_reason(reason):
        """
        BR 4.3.8: Stop work order must have valid reason
        
        Args:
            reason: Reason for stop work order
            
        Raises:
            serializers.ValidationError: If reason is missing or invalid
        """
        if not reason or not reason.strip():
            raise serializers.ValidationError(
                "You must provide a reason for the stop work order."
            )
        return reason

    @staticmethod
    def validate_stop_work_permissions(user):
        """
        BR 4.3.8 & 4.4.3: Validate user can issue stop work order
        
        Args:
            user: User attempting to issue stop work order
            
        Raises:
            serializers.ValidationError: If user lacks permission
        """
        if not user.current_role.name in ["tbi", "dbi", "dro"]:
            raise serializers.ValidationError(
                "Only Building Inspectors (TBI/DBI) or ME/DRO can issue stop work orders."
            )
        return True

    @staticmethod
    def validate_stop_work_release_permissions(user):
        """
        BR 4.4.3: Only ME/DRO can release stop work orders
        
        Args:
            user: User attempting to release stop work order
            
        Raises:
            serializers.ValidationError: If user lacks permission
        """
        if not user.current_role.name in ["dro"]:
            raise serializers.ValidationError(
                "Only ME/DRO can release stop work orders."
            )
        return True


class CommitteeDecisionValidations:
    """
    Validation methods for CommitteeDecision model and related operations
    """

    @staticmethod
    def validate_committee_meeting_minutes(attachments):
        """
        BR 4.2.3 & 4.3.9: Committee meeting minutes must be uploaded before recording decision
        
        Args:
            attachments: List of attachments
            
        Raises:
            serializers.ValidationError: If meeting minutes missing
        """
        if not attachments:
            raise serializers.ValidationError({
                'attachments': 'Committee meeting minutes must be uploaded before recording the decision.'
            })
        return attachments

    @staticmethod
    def validate_committee_decision_text(decision):
        """
        BR 4.2.3: Committee decision must be provided
        
        Args:
            decision: Decision text
            
        Raises:
            serializers.ValidationError: If decision text missing
        """
        if not decision or not decision.strip():
            raise serializers.ValidationError({
                'decision': 'Committee decision must be provided.'
            })
        return decision

    @staticmethod
    def validate_committee_meeting_date(meeting_date):
        """
        BR 4.2.3: Committee meeting date must be specified
        
        Args:
            meeting_date: Date of committee meeting
            
        Raises:
            serializers.ValidationError: If meeting date missing
        """
        if not meeting_date:
            raise serializers.ValidationError({
                'meeting_date': 'Committee meeting date must be specified.'
            })
        return meeting_date

    @staticmethod
    def validate_committee_decision_permissions(user):
        """
        BR 4.2.3: Only Chief (DCE/TCE) can record committee decisions
        
        Args:
            user: User attempting to record decision
            
        Raises:
            serializers.ValidationError: If user lacks permission
        """
        if not user.current_role.name in ["dce", "tce"]:
            raise serializers.ValidationError(
                "Only Chief (DCE/TCE) can record committee decisions."
            )
        return True


class PenaltyValidations:
    """
    Validation methods for Penalty model and related operations
    """

    @staticmethod
    def validate_penalty_amount(amount):
        """
        BR: Penalty amount must be positive
        
        Args:
            amount: Penalty amount
            
        Raises:
            serializers.ValidationError: If amount is invalid
        """
        if amount <= 0:
            raise serializers.ValidationError(
                "Penalty amount must be greater than zero."
            )
        return amount

    @staticmethod
    def validate_penalty_permissions(user):
        """
        BR: Only authorized users can impose penalties
        
        Args:
            user: User attempting to impose penalty
            
        Raises:
            serializers.ValidationError: If user lacks permission
        """
        if not user.current_role.name in ["dce", "tce"]:
            raise serializers.ValidationError(
                "Only Chief (DCE/TCE) can impose penalties."
            )
        return True


class NoticeValidations:
    """
    Validation methods for Notice model and related operations
    """

    @staticmethod
    def validate_notice_content(content):
        """
        BR 4.4: Notice must have valid content
        
        Args:
            content: Notice content
            
        Raises:
            serializers.ValidationError: If content is missing
        """
        if not content or not content.strip():
            raise serializers.ValidationError(
                "Notice content is required."
            )
        return content

    @staticmethod
    def validate_notice_permissions(user, notice_type):
        """
        BR 4.4: Validate user can issue specific notice types
        
        Args:
            user: User attempting to issue notice
            notice_type: Type of notice being issued
            
        Raises:
            serializers.ValidationError: If user lacks permission
        """
        if notice_type in ['stop_work_reminder', 'release_notice'] and not user.current_role.name in ["dro"]:
            raise serializers.ValidationError(
                "Only ME/DRO can issue stop work reminders and release notices."
            )
        return True
