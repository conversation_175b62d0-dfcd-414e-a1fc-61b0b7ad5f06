from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from ongoing_construction.models import Inspection
from building.models import Permit
from user.models import Role
from common.models import TaskPool
from ongoing_construction.enums import InspectionType, InspectionStatus
from user.factories import UserFactory
from common.factories import ConstructionTypeFactory, BuildingTypeFactory, UseFactory, ProposalTypeFactory
from unittest.mock import patch, MagicMock

User = get_user_model()


# Create a simple test class for Permit
class TestPermit:
    def __init__(self, id, user, serial_no):
        self.id = id
        self.user = user
        self.serial_no = serial_no


class InspectionModelTest(TestCase):
    def setUp(self):
        # Create test users
        self.applicant = UserFactory(
            username="applicant",
            email="<EMAIL>",
            first_name="Test",
            last_name="Applicant",
        )
        self.chief = UserFactory(
            username="chief",
            email="<EMAIL>",
            first_name="Test",
            last_name="Chief",
        )
        self.building_inspector = UserFactory(
            username="inspector",
            email="<EMAIL>",
            first_name="Test",
            last_name="Inspector",
        )

        # Create roles
        self.chief_role = Role.objects.create(name=description="Chief")
        self.bi_role = Role.objects.create(name="binsp", description="Building Inspector")

        # Create a test permit instance
        self.permit = TestPermit(id=1, user=self.applicant, serial_no="TEST-PERMIT-001")

        # Create a test inspection
        self.inspection = Inspection.objects.create(
            permit=self.permit,
            user=self.applicant,
            inspection_type=InspectionType.FOUNDATION_BASEMENT,
            remarks="Test inspection",
            by=self.applicant,
        )

    def test_inspection_creation(self):
        """Test that an inspection can be created"""
        self.assertEqual(self.inspection.permit, self.permit)
        self.assertEqual(self.inspection.user, self.applicant)
        self.assertEqual(self.inspection.inspection_type, InspectionType.FOUNDATION_BASEMENT)
        self.assertEqual(self.inspection.state, InspectionStatus.REQUESTED)

    def test_building_inspector_assignment(self):
        """Test that a building inspector can be assigned through task_pools"""
        # Assign a building inspector
        self.inspection.assign_building_inspector(self.building_inspector, by=self.chief)

        # Check that the building inspector is assigned
        assigned_inspector = self.inspection.get_building_inspector()
        self.assertEqual(assigned_inspector, self.building_inspector)

        # Check that a task pool was created
        task_pool = self.inspection.task_pools.filter(role=self.bi_role).first()
        self.assertIsNotNone(task_pool)
        self.assertEqual(task_pool.user, self.building_inspector)

    def test_inspection_transition(self):
        """Test that an inspection can transition states"""
        # Assign a building inspector
        self.inspection.assign_building_inspector(self.building_inspector, by=self.chief)

        # Schedule the inspection
        scheduled_date = timezone.now() + timezone.timedelta(days=1)
        self.inspection.schedule_inspection(
            {"scheduled_date": scheduled_date, "schedule_remarks": "Test schedule"},
            by=self.chief,
        )
        self.inspection.save()

        # Check that the inspection state is updated
        self.assertEqual(self.inspection.state, InspectionStatus.SCHEDULED)
        self.assertEqual(self.inspection.scheduled_date, scheduled_date)
        self.assertEqual(self.inspection.schedule_remarks, "Test schedule")

    def test_task_pools_data(self):
        """Test that task_pools_data is correctly returned"""
        # Create task pools
        TaskPool.objects.create(
            poolable=self.inspection,
            user=self.chief,
            role=self.chief_role,
            by=self.applicant,
        )
        TaskPool.objects.create(
            poolable=self.inspection,
            user=self.building_inspector,
            role=self.bi_role,
            by=self.chief,
        )

        # Check that task_pools_data is correctly returned
        from ongoing_construction.serializers import InspectionSerializer

        serializer = InspectionSerializer(self.inspection)
        task_pools_data = serializer.get_task_pools_data(self.inspection)
        self.assertEqual(len(task_pools_data), 2)
        self.assertEqual(task_pools_data[0]["user_id"], self.chief.id)
        self.assertEqual(task_pools_data[1]["user_id"], self.building_inspector.id)
