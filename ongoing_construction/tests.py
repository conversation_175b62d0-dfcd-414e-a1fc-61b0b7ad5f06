from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from unittest.mock import patch
from ongoing_construction.models import Inspection
from building.models import Permit
from user.models import Role
from common.models import TaskPool
from ongoing_construction.enums import InspectionType, InspectionStatus
from user.factories import UserFactory

User = get_user_model()


class InspectionModelTest(TestCase):
    def setUp(self):
        # Create test users
        self.applicant = UserFactory(
            username="applicant",
            email="<EMAIL>",
            first_name="Test",
            last_name="Applicant",
        )
        self.chief = UserFactory(
            username="chief",
            email="<EMAIL>",
            first_name="Test",
            last_name="Chief",
        )
        self.building_inspector = UserFactory(
            username="inspector",
            email="<EMAIL>",
            first_name="Test",
            last_name="Inspector",
        )

        # Create roles
        self.chief_role = Role.objects.create(name="dce", description="Chief")
        self.bi_role = Role.objects.create(name="tbi", description="Building Inspector")

    @patch('building.signals.permit.determine_assignee_user')
    def test_inspection_creation(self, mock_determine_assignee):
        """Test that an inspection can be created"""
        # Mock the signal that causes issues
        mock_determine_assignee.return_value = (self.chief, self.chief_role)

        # Create a test permit instance
        permit = Permit.objects.create(
            user=self.applicant,
            serial_no="TEST-PERMIT-001",
            construction_id="TEST-CONST-001"
        )

        # Create a test inspection
        inspection = Inspection.objects.create(
            permit=permit,
            user=self.applicant,
            inspection_type=InspectionType.FOUNDATION_BASEMENT,
            remarks="Test inspection",
            by=self.applicant,
            state=InspectionStatus.INITIATED
        )

        self.assertEqual(inspection.permit, permit)
        self.assertEqual(inspection.user, self.applicant)
        self.assertEqual(inspection.inspection_type, InspectionType.FOUNDATION_BASEMENT)
        # The signal automatically transitions to REQUESTED state
        self.assertEqual(inspection.state, InspectionStatus.REQUESTED)



    @patch('building.signals.permit.determine_assignee_user')
    def test_building_inspector_assignment(self, mock_determine_assignee):
        """Test that a building inspector can be assigned through task_pools"""
        # Mock the signal that causes issues
        mock_determine_assignee.return_value = (self.chief, self.chief_role)

        # Create a test permit and inspection
        permit = Permit.objects.create(
            user=self.applicant,
            serial_no="TEST-PERMIT-002",
            construction_id="TEST-CONST-002"
        )
        inspection = Inspection.objects.create(
            permit=permit,
            user=self.applicant,
            inspection_type=InspectionType.FOUNDATION_BASEMENT,
            remarks="Test inspection",
            by=self.applicant,
            state=InspectionStatus.INITIATED
        )

        # Assign a building inspector
        inspection.assign_building_inspector(self.building_inspector, by=self.chief)

        # Check that the building inspector is assigned
        assigned_inspector = inspection.get_building_inspector()
        self.assertEqual(assigned_inspector, self.building_inspector)

        # Check that a task pool was created
        task_pool = inspection.task_pools.filter(role=self.bi_role).first()
        self.assertIsNotNone(task_pool)
        self.assertEqual(task_pool.user, self.building_inspector)

    @patch('building.signals.permit.determine_assignee_user')
    def test_inspection_transition(self, mock_determine_assignee):
        """Test that an inspection can transition states"""
        # Mock the signal that causes issues
        mock_determine_assignee.return_value = (self.chief, self.chief_role)

        # Create a test permit and inspection
        permit = Permit.objects.create(
            user=self.applicant,
            serial_no="TEST-PERMIT-003",
            construction_id="TEST-CONST-003"
        )
        inspection = Inspection.objects.create(
            permit=permit,
            user=self.applicant,
            inspection_type=InspectionType.FOUNDATION_BASEMENT,
            remarks="Test inspection",
            by=self.applicant,
            state=InspectionStatus.INITIATED
        )

        # First transition to requested state
        inspection.initial(by=self.applicant)
        inspection.save()
        self.assertEqual(inspection.state, InspectionStatus.REQUESTED)

        # Assign a building inspector
        inspection.assign_inspector(
            {"inspector_id": self.building_inspector.id, "remarks": "Assigned inspector"},
            by=self.chief,
        )
        inspection.save()
        self.assertEqual(inspection.state, InspectionStatus.INSPECTOR_ASSIGNED)

        # Schedule the inspection
        scheduled_date = timezone.now().date() + timezone.timedelta(days=1)
        inspection.schedule_inspection(
            {"scheduled_date": scheduled_date, "schedule_remarks": "Test schedule"},
            by=self.building_inspector,
        )
        inspection.save()

        # Check that the inspection state is updated
        self.assertEqual(inspection.state, InspectionStatus.SCHEDULED)
        self.assertEqual(inspection.scheduled_date, scheduled_date)
        self.assertEqual(inspection.schedule_remarks, "Test schedule")

    def test_task_pools_data(self):
        """Test that task_pools_data is correctly returned"""
        # Create task pools
        TaskPool.objects.create(
            poolable=self.inspection,
            user=self.chief,
            role=self.chief_role,
            by=self.applicant,
        )
        TaskPool.objects.create(
            poolable=self.inspection,
            user=self.building_inspector,
            role=self.bi_role,
            by=self.chief,
        )

        # Check that task_pools_data is correctly returned
        from ongoing_construction.serializers import InspectionSerializer

        serializer = InspectionSerializer(self.inspection)
        task_pools_data = serializer.get_task_pools_data(self.inspection)
        self.assertEqual(len(task_pools_data), 2)
        self.assertEqual(task_pools_data[0]["user_id"], self.chief.id)
        self.assertEqual(task_pools_data[1]["user_id"], self.building_inspector.id)
