from django.template.loader import render_to_string
from cas_api.celery import app
from ongoing_construction.models import Inspection, InspectionReport, StopWorkOrder, CommitteeDecision, Penalty, Notice
import os
from cas_api.services.mailer_service import send_mail
from user.models import User
from django.shortcuts import get_object_or_404


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_inspector_assigned(self, inspection_id, user_id):
    """
    Send email notification when an building inspector is assigned
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_inspector_assigned.html",
            {"name": user.name, "url": url, "serial_no": inspection.serial_no},
        )
        send_mail("Inspection Assignment", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_inspector_assigned")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_inspection_requested(self, inspection_id):
    """
    Send email notification when an inspection is created
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_inspection_requested.html",
            {"name": user.name, "url": url, "serial_no": inspection.serial_no, "inspection_type": inspection.get_inspection_type_display()},
        )
        send_mail("Inspection Submitted", html_content, [user.email])

        # Notify chief
        try:
            chief_user, _ = inspection.task_pools.filter(role__name__in=["dce", "tce"]).first().user
            html_content = render_to_string(
                "mailers/ongoing_construction/notify_chief_inspection_requested.html",
                {
                    "name": chief_user.name,
                    "url": url,
                    "serial_no": inspection.serial_no,
                    "inspection_type": inspection.get_inspection_type_display(),
                    "applicant_name": user.name,
                },
            )
            send_mail("New Inspection", html_content, [chief_user.email])
        except Exception:
            pass
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_inspection_requested")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_inspection_scheduled(self, inspection_id):
    """
    Send email notification when an inspection is scheduled
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_inspection_scheduled.html",
            {
                "name": user.name,
                "url": url,
                "serial_no": inspection.serial_no,
                "scheduled_date": inspection.scheduled_date,
                "remarks": inspection.schedule_remarks,
            },
        )
        send_mail("Inspection Scheduled", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_inspection_scheduled")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_minor_issue(self, inspection_id, report_id):
    """
    Send email notification when a minor issue is reported
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        report = get_object_or_404(InspectionReport, pk=report_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_minor_issue.html",
            {"name": user.name, "url": url, "serial_no": inspection.serial_no, "recommendations": report.recommendations, "remarks": report.remarks},
        )
        send_mail("Minor Issue Detected During Inspection", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_minor_issue")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_major_issue(self, inspection_id, report_id):
    """
    Send email notification when a major issue is reported
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        report = get_object_or_404(InspectionReport, pk=report_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_major_issue.html",
            {"name": user.name, "url": url, "serial_no": inspection.serial_no, "issue_description": report.remarks},
        )
        send_mail("Major Issue Detected During Inspection", html_content, [user.email])

        # Notify ME/DRO
        try:
            me_user, _ = inspection.task_pools.filter(role__name__in=["dro", "hoi"]).first().user
            html_content = render_to_string(
                "mailers/ongoing_construction/notify_me_major_issue.html",
                {"name": me_user.name, "url": url, "serial_no": inspection.serial_no, "issue_description": report.remarks, "applicant_name": user.name},
            )
            send_mail("Major Issue Reported", html_content, [me_user.email])
        except Exception:
            pass
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_major_issue")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_stop_work_order(self, inspection_id, stop_work_order_id):
    """
    Send email notification when a stop work order is issued
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        stop_work_order = get_object_or_404(StopWorkOrder, pk=stop_work_order_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_stop_work_order.html",
            {
                "name": user.name,
                "url": url,
                "serial_no": inspection.serial_no,
                "reason": stop_work_order.reason,
                "issued_by": stop_work_order.issued_by.name,
                "issued_at": stop_work_order.issued_at,
            },
        )
        send_mail("Stop Work Order Issued", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_stop_work_order")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_committee_meeting(self, inspection_id):
    """
    Send email notification when a committee meeting is requested
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_committee_meeting.html",
            {
                "name": user.name,
                "url": url,
                "serial_no": inspection.serial_no,
                "meeting_date": inspection.committee_meeting_date,
                "remarks": inspection.committee_meeting_remarks,
            },
        )
        send_mail("Committee Meeting Requested", html_content, [user.email])

        # Notify chief
        try:
            chief_user, _ = inspection.task_pools.filter(role__name__in=["dce", "tce"]).first().user
            html_content = render_to_string(
                "mailers/ongoing_construction/notify_chief_committee_meeting.html",
                {
                    "name": chief_user.name,
                    "url": url,
                    "serial_no": inspection.serial_no,
                    "meeting_date": inspection.committee_meeting_date,
                    "applicant_name": user.name,
                },
            )
            send_mail("Committee Meeting Requested", html_content, [chief_user.email])
        except Exception:
            pass
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_committee_meeting")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_committee_decision(self, inspection_id, decision_id):
    """
    Send email notification when a committee decision is made
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        decision = get_object_or_404(CommitteeDecision, pk=decision_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_committee_decision.html",
            {
                "name": user.name,
                "url": url,
                "serial_no": inspection.serial_no,
                "decision": decision.decision,
                "penalty_amount": decision.penalty_amount,
                "changes_required": decision.committee_remarks,
            },
        )
        send_mail("Committee Decision", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_committee_decision")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_changes_required(self, inspection_id):
    """
    Send email notification when changes are required
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_changes_required.html",
            {"name": user.name, "url": url, "serial_no": inspection.serial_no, "changes_required": inspection.changes_required},
        )
        send_mail("Changes Required", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_changes_required")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_changes_verified(self, inspection_id):
    """
    Send email notification when changes are verified
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_changes_verified.html",
            {"name": user.name, "url": url, "serial_no": inspection.serial_no, "verification_remarks": inspection.verification_remarks},
        )
        send_mail("Changes Verified", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_changes_verified")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_penalty_imposed(self, inspection_id, penalty_id):
    """
    Send email notification when a penalty is imposed
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        penalty = get_object_or_404(Penalty, pk=penalty_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_penalty_imposed.html",
            {"name": user.name, "url": url, "serial_no": inspection.serial_no, "amount": penalty.amount, "reason": penalty.reason, "imposed_by": penalty.imposed_by.name},
        )
        send_mail("Penalty Imposed", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_penalty_imposed")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_stop_work_released(self, inspection_id):
    """
    Send email notification when a stop work order is released
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_stop_work_released.html",
            {"name": user.name, "url": url, "serial_no": inspection.serial_no},
        )
        send_mail("Stop Work Order Released", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_stop_work_released")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_inspection_completed(self, inspection_id):
    """
    Send email notification when an inspection is completed
    """
    try:
        inspection = get_object_or_404(Inspection, pk=inspection_id)
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_inspection_completed.html",
            {"name": user.name, "url": url, "serial_no": inspection.serial_no},
        )
        send_mail("Inspection Completed", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_inspection_completed")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_notice(self, notice_id):
    """
    Send email notification for a notice
    """
    try:
        notice = get_object_or_404(Notice, pk=notice_id)
        inspection = notice.inspection
        user = inspection.user
        url = f"{os.getenv('HOST_URL', '')}/ongoing_construction/notices/{notice_id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_notice.html",
            {
                "name": user.name,
                "url": url,
                "serial_no": inspection.serial_no,
                "subject": notice.subject,
                "content": notice.content,
                "notice_type": notice.get_notice_type_display(),
            },
        )
        send_mail(notice.subject, html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_notice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_payment(self, id):
    try:
        inspection = get_object_or_404(Inspection, pk=id)
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection.id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_payment.html",
            {"name": inspection.user.name, "reference": inspection.serial_no, "url": url},
        )
        send_mail("Penalty payment", html_content, [inspection.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_payment")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_conflict_of_interest(self, id, user_id):
    try:
        inspection = get_object_or_404(Inspection, pk=id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection.id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_conflict_of_interest.html",
            {"name": user.name, "reference": inspection.serial_no, "url": url},
        )
        send_mail("Penalty payment", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_payment")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_resubmitted(self, id, user_id):
    try:
        inspection = get_object_or_404(Inspection, pk=id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/ongoing-construction/{inspection.id}"
        html_content = render_to_string(
            "mailers/ongoing_construction/notify_resubmitted.html",
            {"name": user.name, "reference": inspection.serial_no, "url": url},
        )
        send_mail("Inspection Resubmitted", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_resubmitted")
