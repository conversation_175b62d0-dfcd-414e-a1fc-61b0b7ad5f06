# Generated by Django 4.1.7 on 2025-05-13 13:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm
import ongoing_construction.helpers.inspection
import ongoing_construction.transitions.inspection


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('building', '0039_permit_development_type_permit_purpose'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Inspection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('inspection_type', models.CharField(choices=[('building_layout', 'Building Layout'), ('foundation_basement', 'Foundation/Basement'), ('column_footings', 'Column and Footings'), ('slab_beam', 'Slab and beam'), ('roof_truss', 'Roof truss'), ('septic_tank', 'Septic tank and soak pit/sewerage connection'), ('others', 'Others')], max_length=50)),
                ('other_inspection_type', models.CharField(blank=True, max_length=100, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('state', django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('requested', 'Requested'), ('scheduled', 'Scheduled'), ('inspected', 'Inspected'), ('minor_issue', 'Minor Issue'), ('major_issue', 'Major Issue'), ('stop_work_issued', 'Stop Work Issued'), ('committee_meeting_requested', 'Committee Meeting Requested'), ('committee_decision', 'Committee Decision'), ('changes_required', 'Changes Required'), ('changes_verified', 'Changes Verified'), ('penalty_imposed', 'Penalty Imposed'), ('penalty_paid', 'Penalty Paid'), ('stop_work_released', 'Stop Work Released'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='initiated', max_length=50)),
                ('serial_no', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('scheduled_date', models.DateField(blank=True, null=True)),
                ('schedule_remarks', models.TextField(blank=True, null=True)),
                ('verification_remarks', models.TextField(blank=True, null=True)),
                ('changes_required', models.TextField(blank=True, null=True)),
                ('changes_verified', models.BooleanField(default=False)),
                ('committee_meeting_date', models.DateTimeField(blank=True, null=True)),
                ('committee_meeting_remarks', models.TextField(blank=True, null=True)),
                ('building_inspector', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_building_inspections', to=settings.AUTH_USER_MODEL)),
                ('by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_inspections', to=settings.AUTH_USER_MODEL)),
                ('permit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inspections', to='building.permit')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inspections', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'inspection',
                'verbose_name_plural': 'inspections',
                'ordering': ['-created_at'],
            },
            bases=(models.Model, ongoing_construction.helpers.inspection.InspectionHelper, ongoing_construction.transitions.inspection.InspectionTransition),
        ),
        migrations.CreateModel(
            name='StopWorkOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('issued_at', models.DateTimeField(auto_now_add=True)),
                ('released_at', models.DateTimeField(blank=True, null=True)),
                ('reminder_sent', models.BooleanField(default=False)),
                ('reminder_date', models.DateTimeField(blank=True, null=True)),
                ('reminder_remarks', models.TextField(blank=True, null=True)),
                ('inspection', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stop_work_orders', to='ongoing_construction.inspection')),
                ('issued_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='issued_stop_work_orders', to=settings.AUTH_USER_MODEL)),
                ('released_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='released_stop_work_orders', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'stop work order',
                'verbose_name_plural': 'stop work orders',
                'ordering': ['-issued_at'],
            },
        ),
        migrations.CreateModel(
            name='Penalty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('reason', models.TextField()),
                ('is_paid', models.BooleanField(default=False)),
                ('imposed_at', models.DateTimeField(auto_now_add=True)),
                ('paid_at', models.DateTimeField(blank=True, null=True)),
                ('payment_reference', models.CharField(blank=True, max_length=100, null=True)),
                ('payment_method', models.CharField(blank=True, max_length=50, null=True)),
                ('payment_remarks', models.TextField(blank=True, null=True)),
                ('imposed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='imposed_penalties', to=settings.AUTH_USER_MODEL)),
                ('inspection', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='penalties', to='ongoing_construction.inspection')),
            ],
            options={
                'verbose_name': 'penalty',
                'verbose_name_plural': 'penalties',
                'ordering': ['-imposed_at'],
            },
        ),
        migrations.CreateModel(
            name='Notice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notice_type', models.CharField(choices=[('stop_work_order', 'Stop Work Order'), ('recommendation', 'Recommendation'), ('reminder', 'Reminder'), ('committee_decision', 'Committee Decision'), ('release_stop_work', 'Release Stop Work Order')], max_length=50)),
                ('subject', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('issued_at', models.DateTimeField(auto_now_add=True)),
                ('is_read', models.BooleanField(default=False)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('response', models.TextField(blank=True, null=True)),
                ('response_at', models.DateTimeField(blank=True, null=True)),
                ('inspection', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notices', to='ongoing_construction.inspection')),
                ('issued_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='issued_notices', to=settings.AUTH_USER_MODEL)),
                ('responded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notice_responses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'notice',
                'verbose_name_plural': 'notices',
                'ordering': ['-issued_at'],
            },
        ),
        migrations.CreateModel(
            name='InspectionReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('issue_type', models.CharField(choices=[('no_issue', 'No Issue'), ('minor_issue', 'Minor Issue'), ('major_issue', 'Major Issue')], default='no_issue', max_length=20)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('recommendations', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('site_visit_date', models.DateField(blank=True, null=True)),
                ('site_visit_time', models.TimeField(blank=True, null=True)),
                ('site_visit_location', models.CharField(blank=True, max_length=255, null=True)),
                ('is_major_issue', models.BooleanField(default=False)),
                ('forwarded_to_me', models.BooleanField(default=False)),
                ('forwarded_to_committee', models.BooleanField(default=False)),
                ('inspection', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='ongoing_construction.inspection')),
                ('inspector', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inspection_reports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'inspection report',
                'verbose_name_plural': 'inspection reports',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CommitteeDecision',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('decision', models.TextField()),
                ('penalty_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('changes_required', models.TextField(blank=True, null=True)),
                ('decided_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('meeting_date', models.DateTimeField(blank=True, null=True)),
                ('meeting_location', models.CharField(blank=True, max_length=255, null=True)),
                ('attendees', models.TextField(blank=True, null=True)),
                ('is_implemented', models.BooleanField(default=False)),
                ('implementation_date', models.DateTimeField(blank=True, null=True)),
                ('implementation_remarks', models.TextField(blank=True, null=True)),
                ('inspection', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='committee_decisions', to='ongoing_construction.inspection')),
                ('recorded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recorded_committee_decisions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'committee decision',
                'verbose_name_plural': 'committee decisions',
                'ordering': ['-decided_at'],
            },
        ),
    ]
