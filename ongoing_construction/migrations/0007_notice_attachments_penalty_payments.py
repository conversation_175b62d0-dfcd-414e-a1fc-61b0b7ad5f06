# Generated by Django 4.1.7 on 2025-05-17 11:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('file', '0002_attachment_name_attachment_size_attachment_type'),
        ('payment', '0007_payment_agency_code_payment_content_type_and_more'),
        ('ongoing_construction', '0006_committeedecision_attachments'),
    ]

    operations = [
        migrations.AddField(
            model_name='notice',
            name='attachments',
            field=models.ManyToManyField(related_name='notice', to='file.attachment'),
        ),
        migrations.AddField(
            model_name='penalty',
            name='payments',
            field=models.ManyToManyField(related_name='penalty', to='payment.payment'),
        ),
    ]
