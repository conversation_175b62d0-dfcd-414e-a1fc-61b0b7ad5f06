# Generated by Django 4.1.7 on 2025-06-23 21:16

from django.db import migrations
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('ongoing_construction', '0010_remove_penalty_payments'),
    ]

    operations = [
        migrations.AlterField(
            model_name='inspection',
            name='state',
            field=django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('requested', 'Requested'), ('scheduled', 'Scheduled'), ('inspected', 'Inspected'), ('inspector_assigned', 'Inspector Assigned'), ('minor_issue', 'Minor Issue'), ('major_issue', 'Major Issue'), ('stop_work_issued', 'Stop Work Issued'), ('committee_meeting_requested', 'Committee Meeting Requested'), ('committee_decision', 'Committee Decision'), ('changes_required', 'Changes Required'), ('changes_verified', 'Changes Verified'), ('penalty_imposed', 'Penalty Imposed'), ('penalty_paid', 'Penalty Paid'), ('stop_work_released', 'Stop Work Released'), ('resubmitted', 'Re-submitted'), ('completed', 'Completed'), ('rejected', 'Rejected'), ('conflict_of_interest', 'Conflict of Interest')], default='initiated', max_length=50),
        ),
    ]
