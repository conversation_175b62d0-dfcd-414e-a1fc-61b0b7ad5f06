# Generated by Django 4.1.7 on 2025-05-16 12:12

from django.db import migrations, models
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('file', '0002_attachment_name_attachment_size_attachment_type'),
        ('ongoing_construction', '0003_rename_changes_required_committeedecision_committee_remarks'),
    ]

    operations = [
        migrations.AddField(
            model_name='inspectionreport',
            name='attachments',
            field=models.ManyToManyField(blank=True, related_name='inspection_report', to='file.attachment'),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='state',
            field=django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('requested', 'Requested'), ('scheduled', 'Scheduled'), ('inspected', 'Inspected'), ('inspector_assigned', 'Inspector Assigned'), ('minor_issue', 'Minor Issue'), ('major_issue', 'Major Issue'), ('stop_work_issued', 'Stop Work Issued'), ('committee_meeting_requested', 'Committee Meeting Requested'), ('committee_decision', 'Committee Decision'), ('changes_required', 'Changes Required'), ('changes_verified', 'Changes Verified'), ('penalty_imposed', 'Penalty Imposed'), ('penalty_paid', 'Penalty Paid'), ('stop_work_released', 'Stop Work Released'), ('resubmitted', 'Re-submitted'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='initiated', max_length=50),
        ),
    ]
