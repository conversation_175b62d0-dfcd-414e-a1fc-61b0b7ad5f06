import json
from django.urls import reverse
from rest_framework import status
from django.core.management import call_command
from user.factories import UserFactory, ProfileFactory
from address.factories import DzongkhagFactory, GewogFactory, ThromdeFactory, VillageFactory
from django.urls import path, include
from user.models import Role
from information.factories import InquiryFactory
from common.tests import BaseTest


class InquiryTransitionTest(BaseTest):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("information.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.dzo = DzongkhagFactory(name="Mongar")
        self.gewog = GewogFactory(name="Mongar", dzongkhag=self.dzo)
        self.thromde = ThromdeFactory(name="Mongar", dzongkhag=self.dzo)
        self.village = VillageFactory(name="Mongar", gewog=self.gewog)

        # Create regular user
        self.user = UserFactory(username="user", roles=Role.objects.filter(name__in=["user"]).values_list("id", flat=True))
        ProfileFactory(user=self.user, dzongkhag=self.dzo)

        # Create dzongkhag chief engineer
        self.dce = UserFactory(username="dce", roles=Role.objects.filter(name__in=["dce"]).values_list("id", flat=True))
        ProfileFactory(user=self.dce, dzongkhag=self.dzo)

        # Create thromde chief engineer
        self.tce = UserFactory(username="tce", roles=Role.objects.filter(name__in=["tce"]).values_list("id", flat=True))
        ProfileFactory(user=self.tce, dzongkhag=self.dzo, thromde=self.thromde)

        # Create chief urban planner
        self.cup = UserFactory(username="cup", roles=Role.objects.filter(name__in=["cup"]).values_list("id", flat=True))
        ProfileFactory(user=self.cup, dzongkhag=self.dzo, thromde=self.thromde)

        # Create municipal engineer
        self.me = UserFactory(username="dro", roles=Role.objects.filter(name__in=["dro"]).values_list("id", flat=True))
        ProfileFactory(user=self.me, dzongkhag=self.dzo)

        # Create urban planner
        self.up = UserFactory(username="up", roles=Role.objects.filter(name__in=["up"]).values_list("id", flat=True))
        ProfileFactory(user=self.up, dzongkhag=self.dzo, thromde=self.thromde)

        # Get token for ME
        res = self.client.post(reverse("token_obtain_pair"), {"username": self.me.username, "password": "Dcpl@123"})
        self.me_token = json.loads(res.content)["data"]["access"]

        # Get token for UP
        res = self.client.post(reverse("token_obtain_pair"), {"username": self.up.username, "password": "Dcpl@123"})
        self.up_token = json.loads(res.content)["data"]["access"]

        # Create inquiry for rural area
        self.rural_inquiry = InquiryFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog, village=self.village, land_location_flag="R", nature="rural")

        # Create inquiry for urban area
        self.urban_inquiry = InquiryFactory(user=self.user, dzongkhag=self.dzo, thromde=self.thromde, land_location_flag="U", nature="urban")

    def test_invalid_transition(self):
        """Test that invalid transitions return appropriate error"""
        data = {"action": "invalid_action"}
        url = reverse("inquiries_transition", kwargs={"pk": self.rural_inquiry.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.me_token}"
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertEqual(json.loads(response.content)["error"], "Invalid transition")

    def test_rural_inquiry_flow(self):
        """Test complete flow for rural inquiry"""
        # Test forwarding
        data = {"action": "forward", "remarks": "Forwarding to ME"}
        url = reverse("inquiries_transition", kwargs={"pk": self.rural_inquiry.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.me_token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "forwarded")

        # Test providing information
        data = {"action": "provide", "remarks": "Information provided"}
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "provided")

    def test_urban_inquiry_flow(self):
        """Test complete flow for urban inquiry"""
        # Test forwarding
        data = {"action": "forward", "remarks": "Forwarding to UP"}
        url = reverse("inquiries_transition", kwargs={"pk": self.urban_inquiry.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.up_token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "forwarded")

        # Test providing information
        data = {"action": "provide", "remarks": "Information provided"}
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "provided")

    def test_initial_state(self):
        """Test that inquiry is created in initial state"""
        inquiry = InquiryFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog, village=self.village, land_location_flag="R", nature="rural")
        self.assertEqual(inquiry.state, "requested")
