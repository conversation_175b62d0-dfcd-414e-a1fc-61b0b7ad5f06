from django.db.models.signals import post_save
from django.dispatch import receiver
from common.helpers import determine_assignee_user
from common.models import TaskPool
from information.models import Inquiry
from information.mailers import notify_requested, notify_forwarded, notify_provided
from notifications.signals import notify
from django_fsm.signals import post_transition
from rest_framework.exceptions import ValidationError
from cas_api.middleware.current_request import get_current_request


@receiver(post_save, sender=Inquiry)
def inquiry_callback(sender, instance, created, **kwargs):
    by = getattr(get_current_request(), "user", None)
    if created:
        instance.initial(by=instance.user)
        instance.save()
        if instance.thromde:
            user, role = determine_assignee_user("cup", dzo=instance.dzongkhag, thrm=instance.thromde)
            instance.nature = "urban"
        else:
            user, role = determine_assignee_user("dce", dzo=instance.dzongkhag)
            instance.nature = "rural"
        instance.save()
        instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
        notify_requested.delay(instance.id, user.id)
        create_notification(instance, user, "assigned")


@receiver(post_transition, sender=Inquiry)
def inquiry_transition_callback(sender, instance, name, source, target, **kwargs):
    by = getattr(get_current_request(), "user", None)
    if name not in ["initial"]:
        task = instance.task_pools.filter(user_id=getattr(by, "id", None), state="in_progress").first()
        if task:
            task.approve({}, by=by)
            task.save()
    if name == "forward":
        role_name = "up" if instance.nature == "urban" else "dro"
        user, role = determine_assignee_user(role_name, dzo=instance.dzongkhag, thrm=instance.thromde)
        instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
        notify_forwarded.delay(instance.id, user.id)
    if name == "provide":
        notify_provided.delay(instance.id)
    create_notification(instance, instance.user, target)


def create_notification(instance, recipient, action):
    url = f"/services/planning-information/{instance.id}"
    notify.send(getattr(instance, "by", instance.user), recipient=recipient, verb=action, action_object=instance, target=instance, url=url)
    notify.send(getattr(instance, "by", instance.user), recipient=instance.user, verb=action, action_object=instance, target=instance, url=url)
