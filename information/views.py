import time
from rest_framework import generics
from rest_framework.filters import SearchFilter
from django.db import transaction
from rest_framework.exceptions import ValidationError
from common.serializers import StateLogSerializer
from information.helpers import inquiry
from information.helpers.view_helper import perform_save
from information.models import Applicant, Inquiry, Checklist
from common.helpers import fetch_address
from address.models import <PERSON>hrom<PERSON>, Dzongkhag, Village
from information.serializers import InquiryListSerializer, InquirySerializer, ChecklistSerializer
from information.populators import Populator
from rest_framework.decorators import action
from rest_framework import viewsets, status
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django_fsm import can_proceed
from django_fsm_log.models import StateLog


class InquiryView(generics.ListCreateAPIView):
    filter_backends = [SearchFilter]
    search_fields = (
        "serial_no",
        "state",
        "user__first_name",
        "user__last_name",
        "user__email",
        "user__current_role__description",
        "dzongkhag__name",
        "thromde__name",
        "gewog__name",
        "village__name",
        "applicants__thram_no",
        "applicants__plot_no",
    )

    def get_serializer_class(self):
        if self.request.method == "GET":
            return InquiryListSerializer
        elif self.request.method == "POST":
            return InquirySerializer

    def fetch_thromde(self, dzo_thrm):
        return fetch_address(Thromde.objects.all(), dzo_thrm)

    def fetch_dzo(self, dzo_thrm):
        return fetch_address(Dzongkhag.objects.all(), dzo_thrm)

    def create(self, request, *args, **kwargs):
        data = request.data
        if data.get("state") == "draft":
            applicants_data = data.pop("applicants", [])
            inquiry_data = Inquiry(**data, user=request.user)
            inquiry = Inquiry.objects.bulk_create([inquiry_data])[0]
            instances = []
            for applicant_data in applicants_data:
                instances.append(Applicant(inquiry=inquiry, **applicant_data))
            Applicant.objects.bulk_create(instances)
            return Response(InquirySerializer(inquiry).data, status=status.HTTP_201_CREATED)
        else:
            return super().create(request, *args, **kwargs)

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                plot_nos = [obj.get("plot_no") for obj in serializer.validated_data.get("applicants", [])]
                inquiry = Inquiry.objects.filter(applicants__plot_no__in=plot_nos).first()
                if inquiry and inquiry.state not in ["provided", "rejected"]:
                    raise ValidationError({"error": "The application with the same plot no already active and in the process."})
                perform_save(self, serializer)
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e

    def get_queryset(self):
        return Populator(self.request).populate()


class InquiryPKView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = InquirySerializer
    queryset = Inquiry.objects.all()

    def perform_update(self, serializer):
        with transaction.atomic():
            try:
                perform_save(self, serializer)
                super().perform_update(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        data = request.data

        if data.get("state") == "draft":
            applicants_data = data.pop("applicants", [])
            Inquiry.objects.filter(id=instance.id).update(**data)
            existing_applicants = {a.id: a for a in instance.applicants.filter(id__in=[a["id"] for a in applicants_data if "id" in a])}
            applicants_to_update = []
            applicants_to_create = []
            for applicant_data in applicants_data:
                applicant_id = applicant_data.get("id")
                if applicant_id and applicant_id in existing_applicants:
                    applicant = existing_applicants[applicant_id]
                    for field, value in applicant_data.items():
                        setattr(applicant, field, value)
                    applicants_to_update.append(applicant)
                else:
                    applicants_to_create.append(Applicant(application=instance, **applicant_data))
            if applicants_to_create:
                Applicant.objects.bulk_create(applicants_to_create)
            if applicants_to_update:
                Applicant.objects.bulk_update(applicants_to_update, fields=[f.name for f in Applicant._meta.fields if f.name != "id"])
            return Response(InquirySerializer(instance).data, status=status.HTTP_200_OK)
        else:
            return super().update(request, *args, **kwargs)


class InquiryViewSet(viewsets.ModelViewSet):
    queryset = Inquiry.objects.all()
    serializer_class = InquirySerializer

    @action(detail=True, methods=["put"])
    def transition(self, request, **kwargs):
        with transaction.atomic():
            try:
                instance = get_object_or_404(Inquiry, pk=kwargs.get("pk"))
                action = request.data.pop("action")
                if not hasattr(instance, action) or not can_proceed(getattr(instance, action)):
                    return Response({"error": "Invalid transition"}, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
                if getattr(instance, "can_" + action)(request.user):
                    if not action == "start" and instance.task_pools.filter(user=request.user, state="assigned").exists():
                        raise ValidationError({"error": "You need to start your task first."})
                    getattr(instance, action)(request.data, by=request.user)
                    instance.save()
                    serializer = InquirySerializer(instance)
                    return Response(serializer.data, status=status.HTTP_200_OK)
                else:
                    return Response({"error": "You are not permited to perform this action."}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                transaction.set_rollback(True)
                raise e

    @action(detail=True, methods=["get"])
    def activity_logs(self, request, **kwargs):
        application = get_object_or_404(Inquiry, pk=kwargs.get("pk"))
        logs = StateLog.objects.for_(application)
        serializer = StateLogSerializer(logs, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"])
    def draft(self, request, **kwargs):
        user = request.user
        inquiry = user.inquiries.filter(state="draft").first()
        if not inquiry:
            return Response({"error": "No draft inquiry found."}, status=status.HTTP_404_NOT_FOUND)
        serializer = InquirySerializer(inquiry)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ChecklistView(generics.ListCreateAPIView):
    serializer_class = ChecklistSerializer
    queryset = Checklist.objects.all()


class ChecklistPKView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ChecklistSerializer
    queryset = Checklist.objects.all()
