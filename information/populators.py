from .models import Inquiry


class Populator:
    def __init__(self, request):
        self.records = Inquiry.objects.all().order_by("-created_at")
        self.current_user = request.user
        self.params = request.query_params

    def populate(self):
        state = self.params.get("state")
        active_task = self.params.get("active_task")
        role_name = self.current_user.current_role.name

        if role_name == "admin":
            self.records = self.records
        elif role_name == "user":
            self.records = self.records.filter(user=self.current_user)
        elif role_name in ["dce", "dadm", "dzongda"]:
            self.records = self.records.filter(dzongkhag=self.current_user.dzongkhag)
        elif role_name in ["tce", "tadm", "thrompon"]:
            self.records = self.records.filter(thromde=self.current_user.thromde)
        elif role_name in ["roidce", "roidadm"]:
            self.records = self.records.filter(region=self.current_user.region)
        elif role_name in ["gup"]:
            self.records = self.records.filter(gewog=self.current_user.gewog)
        else:
            self.records = self.records.filter(task_pools__user=self.current_user)

        if active_task == "true":
            self.records = self.records.filter(task_pools__user=self.current_user, task_pools__state__in=["assigned", "in_progress"])
        elif active_task == "false":
            self.records = self.records.exclude(id__in=self.records.filter(task_pools__user=self.current_user, task_pools__state__in=["assigned", "in_progress"]))

        if state:
            self.records = self.records.filter(state=state)

        return self.records
