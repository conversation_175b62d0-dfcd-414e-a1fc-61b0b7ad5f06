import factory
from factory.django import DjangoModelFactory
from information.models import Inquiry
from user.factories import UserFactory
from address.factories import DzongkhagFactory, GewogFactory, ThromdeFactory, VillageFactory


class InquiryFactory(DjangoModelFactory):
    class Meta:
        model = Inquiry

    name = factory.Sequence(lambda n: f"Inquiry {n}")
    thram_no = factory.Sequence(lambda n: f"THRAM-{n}")
    plot_no = factory.Sequence(lambda n: f"PLOT-{n}")
    plot_map = []
    dzo_thromde = factory.Sequence(lambda n: f"DzoThromde {n}")
    gewog_thromde = factory.Sequence(lambda n: f"GewogThromde {n}")
    land_location_flag = "R"  # Default to Rural
    land_status = "Available"
    remarks = "Test remarks"
    user = factory.SubFactory(UserFactory)
    nature = "rural"
    dzongkhag = factory.SubFactory(DzongkhagFactory)
    gewog = factory.SubFactory(GewogFactory)
    village = factory.SubFactory(VillageFactory)
    serial_no = factory.Sequence(lambda n: f"SERIAL-{n}")
    checklist = {"items": []}
