# Generated by Django 4.1.7 on 2025-03-10 20:21

from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import django_fsm
import information.helpers.inquiry
import information.transitions


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("address", "0006_thromde_code"),
    ]

    operations = [
        migrations.CreateModel(
            name="Inquiry",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("name", models.CharField(max_length=100)),
                ("thram_no", models.Char<PERSON>ield(max_length=100)),
                ("plot_no", models.Char<PERSON>ield(max_length=100)),
                ("plot_map", django.contrib.postgres.fields.ArrayField(base_field=models.Char<PERSON>ield(max_length=50), blank=True, default=list, size=None)),
                ("dzo_thromde", models.Char<PERSON>ield(max_length=100)),
                ("gewog_thromde", models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ("land_location_flag", models.CharField(max_length=5)),
                ("land_status", models.CharField(max_length=100)),
                ("remarks", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("state", django_fsm.FSMField(choices=[("requested", "Requested"), ("forwarded", "Forwarded"), ("PROVIDED", "Provided")], default="requested", max_length=20)),
                ("nature", models.CharField(choices=[("urban", "Urban"), ("rural", "Rural")], default="rural", max_length=50)),
                ("serial_no", models.CharField(max_length=50)),
                ("checklist", models.JSONField(null=True)),
                ("dzongkhag", models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="address.dzongkhag")),
                ("gewog", models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="address.gewog")),
                ("thromde", models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="address.thromde")),
                ("user", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ("village", models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="address.village")),
            ],
            bases=(models.Model, information.transitions.Transition, information.helpers.inquiry.Helper),
        ),
    ]
