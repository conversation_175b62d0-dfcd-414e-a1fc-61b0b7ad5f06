# Generated by Django 4.1.7 on 2025-07-18 19:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('information', '0015_remove_inquiry_cid_remove_inquiry_dzo_thromde_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='inquiry',
            name='state',
            field=django_fsm.FSMField(choices=[('draft', 'Draft'), ('initiated', 'Initiated'), ('requested', 'Requested'), ('forwarded', 'Forwarded'), ('provided', 'Provided')], default='initiated', max_length=20),
        ),
        migrations.AlterField(
            model_name='inquiry',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inquiries', to=settings.AUTH_USER_MODEL),
        ),
    ]
