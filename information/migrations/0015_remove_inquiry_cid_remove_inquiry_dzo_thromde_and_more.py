# Generated by Django 4.1.7 on 2025-06-23 19:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('information', '0014_inquiry_region'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='inquiry',
            name='cid',
        ),
        migrations.RemoveField(
            model_name='inquiry',
            name='dzo_thromde',
        ),
        migrations.RemoveField(
            model_name='inquiry',
            name='gewog_thromde',
        ),
        migrations.RemoveField(
            model_name='inquiry',
            name='land_location_flag',
        ),
        migrations.RemoveField(
            model_name='inquiry',
            name='land_status',
        ),
        migrations.RemoveField(
            model_name='inquiry',
            name='name',
        ),
        migrations.RemoveField(
            model_name='inquiry',
            name='plot_map',
        ),
        migrations.RemoveField(
            model_name='inquiry',
            name='plot_no',
        ),
        migrations.RemoveField(
            model_name='inquiry',
            name='thram_no',
        ),
        migrations.CreateModel(
            name='Applicant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cid', models.CharField(max_length=20, null=True)),
                ('thram_no', models.CharField(max_length=20, null=True)),
                ('plot_no', models.CharField(max_length=20, null=True)),
                ('plot_area_unit', models.CharField(max_length=20, null=True)),
                ('plot_net_area', models.FloatField(max_length=20, null=True)),
                ('plot_status', models.CharField(blank=True, max_length=30, null=True)),
                ('plot_status_reason', models.TextField(blank=True, null=True)),
                ('ownership_type', models.CharField(max_length=50, null=True)),
                ('owner_name', models.CharField(max_length=50, null=True)),
                ('land_type', models.CharField(max_length=50, null=True)),
                ('prescient_code', models.CharField(max_length=50, null=True)),
                ('land_location_flag', models.CharField(max_length=5, null=True)),
                ('demkhong_name', models.CharField(blank=True, max_length=50, null=True)),
                ('gewog_thromde', models.CharField(max_length=50, null=True)),
                ('dzongkhag_thromde', models.CharField(max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('phone_no', models.CharField(max_length=20, null=True)),
                ('plot_map', models.JSONField(blank=True, default=list)),
                ('inquiry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applicants', to='information.inquiry')),
            ],
        ),
    ]
