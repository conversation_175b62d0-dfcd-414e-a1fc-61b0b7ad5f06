from django.db import models
from django_fsm import <PERSON><PERSON><PERSON><PERSON>
from address.models import Dzongkhag, Gewog, Thromde, Village
from common.enums import LandNature
from information.enums import InquiryState
from django.contrib.contenttypes.fields import GenericRelation
from information.helpers.inquiry import Helper
from information.transitions import Transition
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from planning.enums import ApplicantType, ChecklistType


class Inquiry(models.Model, Transition, Helper):
    applicant_type = models.CharField(max_length=50, null=False, choices=ApplicantType.choices)
    remarks = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    state = FSMField(max_length=20, null=False, choices=InquiryState.choices, default=InquiryState.INITIATED)
    task_pools = GenericRelation("common.TaskPool", related_query_name="inquiry")
    user = models.ForeignKey("user.User", on_delete=models.CASCADE, related_name="inquiries")
    nature = models.CharField(max_length=50, null=False, choices=LandNature.choices, default=LandNature.RURAL)
    region = models.ForeignKey("address.Region", null=True, on_delete=models.SET_NULL, blank=True, unique=False)
    dzongkhag = models.ForeignKey(Dzongkhag, null=True, on_delete=models.SET_NULL, blank=True, unique=False)
    thromde = models.ForeignKey(Thromde, null=True, on_delete=models.SET_NULL, blank=True, unique=False)
    gewog = models.ForeignKey(Gewog, null=True, on_delete=models.SET_NULL, blank=True, unique=False)
    village = models.ForeignKey(Village, null=True, on_delete=models.SET_NULL, blank=True, unique=False)
    serial_no = models.CharField(max_length=50)
    construction_id = models.CharField(max_length=100, null=True, blank=True, help_text="Unique identifier for tracking construction across all modules")
    edge_conditions = models.JSONField(null=True)
    checklists = models.ManyToManyField("Checklist", blank=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        indexes = [
            models.Index(fields=["created_at"]),
            models.Index(fields=["state"]),
            models.Index(fields=["dzongkhag"]),
            models.Index(fields=["gewog"]),
            models.Index(fields=["construction_id"]),
            models.Index(fields=["applicant_type"]),
        ]


class Applicant(models.Model):
    cid = models.CharField(null=True, max_length=20)
    thram_no = models.CharField(null=True, max_length=20)
    plot_no = models.CharField(null=True, max_length=20)
    plot_area_unit = models.CharField(null=True, max_length=20)
    plot_net_area = models.FloatField(null=True, max_length=20)
    plot_status = models.CharField(null=True, blank=True, max_length=30)
    plot_status_reason = models.TextField(null=True, blank=True)
    ownership_type = models.CharField(null=True, max_length=50)
    owner_name = models.CharField(null=True, max_length=50)
    land_type = models.CharField(null=True, max_length=50)
    prescient_code = models.CharField(null=True, max_length=50)
    land_location_flag = models.CharField(null=True, max_length=5)
    demkhong_name = models.CharField(null=True, max_length=50, blank=True)
    gewog_thromde = models.CharField(null=True, max_length=50)
    dzongkhag_thromde = models.CharField(null=True, max_length=50)
    inquiry = models.ForeignKey(Inquiry, related_name="applicants", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    email = models.EmailField(null=True, blank=True)
    phone_no = models.CharField(null=True, max_length=20)
    plot_map = models.JSONField(default=list, blank=True)

    def __str__(self):
        return str(self.__dict__)


class Checklist(models.Model):
    title = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    type = models.CharField(max_length=50, choices=ChecklistType.choices, default=ChecklistType.TEXT)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
