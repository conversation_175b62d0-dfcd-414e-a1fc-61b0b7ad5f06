from django.urls import path
from information import views

urlpatterns = [
    path("inquiries/", views.InquiryView.as_view(), name="inquiries"),
    path("inquiries/<int:pk>/", views.InquiryPKView.as_view(), name="inquiries"),
    path("inquiries/<int:pk>/transition/", views.InquiryViewSet.as_view({"put": "transition"}), name="inquiries_transition"),
    path("inquiries/<int:pk>/activity_logs/", views.InquiryViewSet.as_view({"get": "activity_logs"}), name="inquiries_activity_logs"),
    path("inquiries/draft/", views.InquiryViewSet.as_view({"get": "draft"}), name="draft"),
    path("checklists/", views.ChecklistView.as_view(), name="checklists"),
    path("checklists/<int:pk>/", views.ChecklistPKView.as_view(), name="checklists"),
]
