from django_fsm import transition
from django_fsm_log.decorators import fsm_log_by
from rest_framework.exceptions import ValidationError
from django_fsm_log.decorators import fsm_log_description


class Transition:

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["initiated", "draft"], target="requested")
    def initial(self, by=None, description=None):
        self.by = by
        description.set(f"Inquiry requested by{getattr(self.thromde, 'name', self.dzongkhag.name)}.")

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="requested", target="forwarded")
    def forward(self, data, by=None, description=None):
        self.by = by
        description.set(data.get("remarks", f"Inquiry forwarded by{getattr(self.thromde, 'name', self.dzongkhag.name)}."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="forwarded", target="provided")
    def provide(self, data, by=None, description=None):
        self.by = by
        description.set("Information provided to your inquiry.")
        if not data.get("edge_conditions"):
            raise ValidationError({"error": "Edge conditions is required."})
        if not data.get("checklist_ids") or len(data.get("checklist_ids")) == 0:
            raise ValidationError({"error": "Inquiry checklists is required."})
        self.edge_conditions = data.get("edge_conditions")
        self.checklists.set(data.get("checklist_ids"))
        self.remarks = data.get("remarks")
