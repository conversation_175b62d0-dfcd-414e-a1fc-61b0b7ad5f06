from rest_framework import serializers
from .models import Applicant, Inquiry, Checklist
from drf_writable_nested.serializers import WritableNestedModelSerializer


class ChecklistSerializer(serializers.ModelSerializer):
    type = serializers.CharField(required=True)

    class Meta:
        model = Checklist
        fields = ["id", "title", "description", "type", "created_at"]


class ApplicantSerializer(serializers.ModelSerializer):
    plot_no = serializers.CharField(required=True)
    cid = serializers.CharField(required=True)
    thram_no = serializers.CharField(required=True)

    class Meta:
        model = Applicant
        fields = [
            "id",
            "cid",
            "thram_no",
            "plot_no",
            "plot_area_unit",
            "plot_net_area",
            "plot_status",
            "plot_status_reason",
            "ownership_type",
            "owner_name",
            "land_type",
            "prescient_code",
            "land_location_flag",
            "demkhong_name",
            "gewog_thromde",
            "dzongkhag_thromde",
            "inquiry_id",
            "created_at",
            "email",
            "phone_no",
            "plot_map",
        ]


class InquirySerializer(WritableNestedModelSerializer):
    applicants = ApplicantSerializer(many=True, required=True)
    region_name = serializers.CharField(source="region.name", read_only=True)
    dzongkhag_name = serializers.CharField(source="dzongkhag.name", read_only=True)
    thromde_name = serializers.CharField(source="thromde.name", read_only=True)
    gewog_name = serializers.CharField(source="gewog.name", read_only=True)
    village_name = serializers.CharField(source="village.name", read_only=True)
    serial_no = serializers.CharField(required=False)
    edge_conditions = serializers.JSONField(required=False)
    remarks = serializers.CharField(required=False)
    checklists = ChecklistSerializer(many=True, read_only=True)
    checklist_ids = serializers.PrimaryKeyRelatedField(queryset=Checklist.objects.all(), source="checklists", many=True, required=False, write_only=True)
    state_display = serializers.CharField(source="get_state_display", read_only=True)

    class Meta:
        model = Inquiry
        fields = [
            "id",
            "applicant_type",
            "remarks",
            "created_at",
            "state",
            "state_display",
            "user_id",
            "nature",
            "region_id",
            "region_name",
            "dzongkhag_id",
            "dzongkhag_name",
            "thromde_id",
            "thromde_name",
            "gewog_id",
            "gewog_name",
            "village_id",
            "village_name",
            "serial_no",
            "construction_id",
            "edge_conditions",
            "checklist_ids",
            "checklists",
            "turn_around_time",
            "construction_id",
            "applicants",
        ]


class InquiryListSerializer(serializers.ModelSerializer):
    region_name = serializers.CharField(source="region.name", read_only=True)
    dzongkhag_name = serializers.CharField(source="dzongkhag.name", read_only=True)
    thromde_name = serializers.CharField(source="thromde.name", read_only=True)
    gewog_name = serializers.CharField(source="gewog.name", read_only=True)
    village_name = serializers.CharField(source="village.name", read_only=True)
    state_display = serializers.CharField(source="get_state_display", read_only=True)

    class Meta:
        model = Inquiry
        fields = [
            "id",
            "applicant_type",
            "created_at",
            "state",
            "state_display",
            "user_id",
            "region_name",
            "dzongkhag_name",
            "thromde_name",
            "gewog_name",
            "village_name",
            "serial_no",
            "construction_id",
            "turn_around_time",
        ]
