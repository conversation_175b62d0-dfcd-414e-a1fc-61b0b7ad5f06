from django.template.loader import render_to_string
from cas_api.celery import app
from information.models import Inquiry
from planning.models import Application
import os
from cas_api.services.mailer_service import send_mail
from user.models import User
from django.shortcuts import get_object_or_404


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_requested(self, inquiry_id, user_id):
    try:
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/planning-information/{inquiry_id}"
        context = {"name": user.name, "url": url}
        html_content = render_to_string("mailers/informations/notify_requested.html", context)
        send_mail("New inquiry", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_requested")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forwarded(self, inquiry_id, user_id):
    try:
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/planning-information/{inquiry_id}"
        context = {"name": user.name, "url": url}
        html_content = render_to_string("mailers/informations/notify_forwarded.html", context)
        send_mail("Inquiry forwarded", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_forwarded")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_provided(self, inquiry_id):
    try:
        inquiry = get_object_or_404(Inquiry, pk=inquiry_id)
        user = inquiry.user
        url = f"{os.getenv('HOST_URL', '')}/services/planning-information/{inquiry_id}"
        context = {"name": user.name, "url": url}
        html_content = render_to_string("mailers/informations/notify_provided.html", context)
        send_mail("Inquiry answered", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)
        except Exception:
            print("Max retries exceeded for notify_provided")
