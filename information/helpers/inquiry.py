class Helper:
    def can_forward(self, current_user):
        current_role = current_user.current_role
        if (current_role.name == "cup" and self.nature == "urban") or (current_role.name == "dce" and self.nature == "rural"):
            return True
        else:
            return False

    def can_provide(self, current_user):
        current_role = current_user.current_role
        if (current_role.name == "dro" and self.nature == "rural") or (current_role.name == "up" and self.nature == "urban"):
            return True
        else:
            return False

    @property
    def turn_around_time(self):
        from django.db import models
        import datetime

        turnaround_time = self.task_pools.aggregate(earliest_created_at=models.Min("created_at"), latest_created_at=models.Max("created_at"))
        return str((turnaround_time["latest_created_at"] or datetime.date.today()) - (turnaround_time["earliest_created_at"] or datetime.date.today()))
