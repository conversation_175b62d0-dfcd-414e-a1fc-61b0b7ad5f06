<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Construction Report - {{ module|title }}</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        color: #333;
      }
      
      .header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #0066cc;
        padding-bottom: 20px;
      }
      
      .header h1 {
        color: #0066cc;
        margin: 0;
        font-size: 28px;
      }
      
      .header h2 {
        color: #666;
        margin: 10px 0 0 0;
        font-size: 20px;
      }
      
      .info {
        margin-bottom: 30px;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        border-left: 4px solid #0066cc;
      }
      
      .info p {
        margin: 5px 0;
        font-size: 14px;
      }
      
      .info strong {
        color: #0066cc;
      }
      
      .section {
        margin-bottom: 20px;
        clear: both;
      }
      
      .section h3 {
        color: #0066cc;
        border-bottom: 1px solid #ddd;
        padding-bottom: 5px;
        margin-bottom: 10px;
        margin-top: 0;
        font-size: 18px;
      }
      
      /* Container for table to handle overflow */
      .table-container {
        width: 100%;
        overflow: hidden;
        margin-bottom: 15px;
        margin-top: 0;
      }
      
      table {
        width: 100%;
        border-collapse: collapse;
        font-size: 13px;
        table-layout: fixed;
        margin: 0;
        max-width: 100%;
      }
      
      th,
      td {
        border: 1px solid #ddd;
        padding: 6px;
        text-align: left;
        vertical-align: top;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        line-height: 1.3;
        font-size: 13px;
        white-space: normal;
      }
      
      th {
        background-color: #f2f2f2;
        font-weight: bold;
        color: #333;
        font-size: 12px;
        line-height: 1.3;
        padding: 6px;
        text-transform: uppercase;
      }
      
      tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      
      /* Flexible column widths that adapt to content */
      th,
      td {
        min-width: 60px;
        max-width: 200px;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }
      
      /* Specific column optimizations */
      th:nth-child(1),
      td:nth-child(1) {
        width: 5%;
        min-width: 30px;
        max-width: 50px;
      } /* ID */
      
      th:nth-child(2),
      td:nth-child(2) {
        width: 20%;
        min-width: 100px;
        max-width: 150px;
      } /* Serial No */
      
      th:nth-child(3),
      td:nth-child(3) {
        width: 8%;
        min-width: 60px;
        max-width: 80px;
      } /* State */
      
      th:nth-child(4),
      td:nth-child(4) {
        width: 15%;
        min-width: 80px;
        max-width: 120px;
      } /* Created At */
      
      th:nth-child(5),
      td:nth-child(5) {
        width: 15%;
        min-width: 80px;
        max-width: 120px;
      } /* Construction ID */
      
      /* Handle long content with readable formatting */
      .long-text {
        font-size: 12px;
        line-height: 1.3;
        word-break: break-word;
        overflow-wrap: break-word;
      }
      
      /* Date formatting - readable */
      .date-cell {
        font-size: 12px;
        line-height: 1.3;
        word-break: break-word;
        white-space: pre-line;
      }
      
      /* Serial number formatting - readable */
      .serial-cell {
        font-size: 12px;
        line-height: 1.3;
        word-break: break-word;
        overflow-wrap: break-word;
      }
      
      /* Readable text for all cells */
      td {
        font-size: 13px;
        line-height: 1.3;
        word-break: break-word;
        overflow-wrap: break-word;
      }
      
      /* Ensure no content overflows */
      * {
        box-sizing: border-box;
      }
      
      .footer {
        margin-top: 40px;
        text-align: center;
        font-size: 12px;
        color: #666;
        border-top: 1px solid #ddd;
        padding-top: 20px;
      }
      
      .no-data {
        text-align: center;
        color: #666;
        font-style: italic;
        padding: 20px;
      }
      
      .summary-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        gap: 10px;
        flex-wrap: wrap;
      }
      
      .stat-box {
        flex: 1;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        text-align: center;
        min-width: 150px;
        max-width: 200px;
      }
      
      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #0066cc;
      }
      
      .stat-label {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
      }
      
      @media print {
        body {
          margin: 0;
        }
      
        .summary-stats {
          display: flex !important;
          justify-content: space-between !important;
          gap: 10px !important;
        }
      
        .stat-box {
          flex: 1 !important;
          min-width: 120px !important;
          max-width: 180px !important;
        }
      }
      
      /* Additional PDF-specific optimizations */
      @page {
        size: A4 landscape;
        margin: 0.4in;
      }
      
      /* Ensure table fits on page */
      .table-container {
        max-width: 100%;
        overflow: hidden;
        page-break-inside: auto;
      }
      
      /* Force table to fit page width */
      table {
        max-width: 100%;
        width: 100% !important;
        page-break-inside: auto;
      }
      
      /* Prevent table rows from breaking across pages when possible */
      tr {
        page-break-inside: avoid;
      }
      
      /* Allow table headers to repeat on new pages */
      thead {
        display: table-header-group;
      }
      
      tbody {
        display: table-row-group;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>Construction Approval System</h1>
      <h2>{{ module|title }} Report</h2>
    </div>

    <div class="info">
      <p>
        <strong>Generated by:</strong> {{ user }}
      </p>
      <p>
        <strong>Generated on:</strong> {{ generated_at }}
      </p>
      <p>
        <strong>Total Records:</strong> {{ total_records }}
      </p>
      {% if filters %}
        <p>
          <strong>Filters Applied:</strong>
          {% if filters.start_date %}
            From: {{ filters.start_date }},
          {% endif %}
          {% if filters.end_date %}
            To: {{ filters.end_date }},
          {% endif %}
          {% if filters.status %}
            Status: {{ filters.status|title }},
          {% endif %}
          {% if filters.dzongkhag_id %}
            Dzongkhag ID: {{ filters.dzongkhag_id }}
          {% endif %}
        </p>
      {% endif %}
    </div>

    {% if summary_stats %}
      <div class="section">
        <h3>Summary Statistics</h3>
        <div class="summary-stats">
          {% for stat_name, stat_value in summary_stats.items %}
            <div class="stat-box">
              <div class="stat-number">{{ stat_value }}</div>
              <div class="stat-label">{{ stat_name|title }}</div>
            </div>
          {% endfor %}
        </div>
      </div>
    {% endif %}

    {% for table_name, table_data in tables.items %}
      <div class="section">
        <h3>{{ table_name|title }} Data</h3>

        {% if table_data %}
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  {% for header in table_data.headers %}
                    <th>{{ header }}</th>
                  {% endfor %}
                </tr>
              </thead>
              <tbody>
                {% for row in table_data.rows %}
                  <tr>
                    {% for cell in row %}
                      <td>{{ cell|default:'-'|safe }}</td>
                    {% endfor %}
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        {% else %}
          <div class="no-data">No data available for {{ table_name|title }}</div>
        {% endif %}
      </div>
    {% endfor %}

    <div class="footer">
      <p>Generated by Construction Approval System (CAS)</p>
      <p>Royal Government of Bhutan - Department of Human Settlement</p>
    </div>
  </body>
</html>
