{% extends '../shared/layout.html' %}
{% block content %}
  <p class="header-dzo">
    <b>{{ certificate.permit.dzongkhag.name|upper }} : BHUTAN</b>
  </p>
  {% load static %}
  <div class="font-14-3">
    <div class="title-container mt10">
      <span class="font-700 left0">MoIT/CAS/{{ finyear|date:'Y' }}-{{ certificate.created_at|date:'Y' }}/{{ certificate.id }}</span>
      <span class="font-700 right0">{{ certificate.created_at|date:'d/m/Y' }}</span>
    </div>
    <div class="sub-title text-center">
      <span class="font-700 font-size-18"><u>Occupancy Certificate</u></span>
    </div>
    <div class="space-between mt10">
      <span class="font-700">Certificate Details</span>
    </div>
    <table class="col-md-12">
      <tr>
        <td class="col-md-4">Certificate Number</td>
        <td>: {{ certificate.certificate_number }}</td>
      </tr>
      <tr>
        <td class="col-md-4">Building Number</td>
        <td>: {{ certificate.building_number }}</td>
      </tr>
      <tr>
        <td class="col-md-4">Owner/Proponent</td>
        <td>: {{ certificate.user.get_full_name }}</td>
      </tr>
      <tr>
        <td class="col-md-4">Building Permit No</td>
        <td>: {{ certificate.permit.serial_no }}</td>
      </tr>
      <tr>
        <td class="col-md-4">No. of floors</td>
        <td>: {{ certificate.permit.no_of_floors }}</td>
      </tr>
      <tr>
        <td class="col-md-4">Location</td>
        <td>: 
          {% if certificate.permit.nature == 'urban' %}
            {{ certificate.permit.thromde.name }}, 
          {% else %}
            {{ certificate.permit.gewog.name }}, 
          {% endif %}
          {{ certificate.permit.dzongkhag.name }}
        </td>
      </tr>
      <tr>
        <td class="col-md-4">Certificate Date</td>
        <td>: {{ certificate.certificate_date|date:'d/m/Y' }}</td>
      </tr>
    </table>
    
    <div class="mt10">
      <span class="font-700">Building Units</span>
    </div>
    <table class="col-md-12 border">
      <thead>
        <tr>
          <th>Unit Number</th>
          <th>Floor Number</th>
          <th>Area (sq.ft)</th>
          <th>QR Code</th>
        </tr>
      </thead>
      <tbody>
        {% for unit in building_units %}
        <tr>
          <td>{{ unit.unit_number }}</td>
          <td>{{ unit.floor_number }}</td>
          <td>{{ unit.area_sqft }}</td>
          <td>{{ unit.unit_qr_code }}</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
    
    <div class="mt10 justify">
      <p>This is to certify that the building constructed as per the approved drawings has been inspected and found to be in compliance with the approved plans and building regulations. The building is hereby granted an Occupancy Certificate and is fit for occupation.</p>
      <p>The building has been assigned the Building Number <strong>{{ certificate.building_number }}</strong> and QR Code <strong>{{ certificate.building_qr_code }}</strong>.</p>
      <p>This certificate is issued subject to the following conditions:</p>
      <ol type="1">
        <li>The building shall be used only for the purpose for which it was approved.</li>
        <li>Any subsequent modifications to the building shall require prior approval from the concerned authorities.</li>
        <li>The building owner shall maintain the building in good condition and ensure compliance with safety regulations.</li>
      </ol>
    </div>
    
    <div>
      <br />
      <br />
      Thanking You,
      <br />
      Yours faithfully,
    </div>
    <div>
      <img src="{% if seal %}
          {{ seal }}
        {% else %}
          {% static 'images/default-seal.png' %}
        {% endif %}"
        height="auto"
        width="100"
        alt="Seal"
        class="seal" />
    </div>
    <span>Dasho Dzongda</span>
    <br />
    <span>{{ certificate.permit.dzongkhag.name }} Dzongkhag</span>
    <br />
  </div>
{% endblock %}
