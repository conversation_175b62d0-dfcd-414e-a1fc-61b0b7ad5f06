# Generated by Django 4.1.7 on 2025-05-29 17:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('technical', '0003_clearance_construction_id'),
    ]

    operations = [
        migrations.AlterField(
            model_name='clearance',
            name='hotel_type',
            field=models.CharField(blank=True, choices=[('below_star_1', 'Below Star 1'), ('star_1_2', 'Star 1-2'), ('campsite', 'Campsite'), ('ecolodge', 'Ecolodge'), ('star_3_5', 'Star 3-5'), ('tented', 'Tented Accommodation')], default='below_star_1', max_length=100, null=True),
        ),
        migrations.AddIndex(
            model_name='clearance',
            index=models.Index(fields=['created_at'], name='technical_c_created_550ffa_idx'),
        ),
        migrations.AddIndex(
            model_name='clearance',
            index=models.Index(fields=['state'], name='technical_c_state_6d950c_idx'),
        ),
        migrations.AddIndex(
            model_name='clearance',
            index=models.Index(fields=['dzongkhag'], name='technical_c_dzongkh_751555_idx'),
        ),
        migrations.AddIndex(
            model_name='clearance',
            index=models.Index(fields=['gewog'], name='technical_c_gewog_i_791d0b_idx'),
        ),
        migrations.AddIndex(
            model_name='clearance',
            index=models.Index(fields=['construction_id'], name='technical_c_constru_fe369f_idx'),
        ),
        migrations.AddIndex(
            model_name='clearance',
            index=models.Index(fields=['hotel_type'], name='technical_c_hotel_t_29ee60_idx'),
        ),
    ]
