# Generated by Django 4.1.7 on 2025-04-23 21:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('planning', '0061_application_site_photos'),
        ('common', '0013_rename_cdb_no_designteam_bcta_no'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('address', '0010_region_dzongkhag_created_at_dzongkhag_updated_at_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Clearance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_no', models.CharField(blank=True, max_length=100, null=True)),
                ('hotel_type', models.CharField(blank=True, choices=[('below_star_1', 'Below Star 1'), ('star_1_2', 'Star 1-2'), ('star_3_5', 'Star 3-5'), ('campsite', 'Campsite'), ('ecolodge', 'Ecolodge')], default='below_star_1', max_length=100, null=True)),
                ('state', django_fsm.FSMField(choices=[('draft', 'Draft'), ('initiated', 'Initiated'), ('requested', 'Requested'), ('architect_assigned', 'Architect Assigned'), ('forwarded', 'Forwarded '), ('approved', 'Approved'), ('rejected', 'Rejected'), ('pending_change', 'Pending Change'), ('resubmitted', 'Re-submitted'), ('pending_payment', 'Pending Payment'), ('closed', 'Closed'), ('cancelled', 'Cancelled')], default='initiated', max_length=50)),
                ('nature', models.CharField(choices=[('urban', 'Urban'), ('rural', 'Rural')], default='rural', max_length=50, null=True)),
                ('sustainable', models.BooleanField(default=False)),
                ('sustainable_remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('application', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technical_clearance', to='planning.application')),
                ('building_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technical_clearance', to='common.buildingtype')),
                ('construction_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technical_clearance', to='common.constructiontype')),
                ('dzongkhag', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technical_clearance', to='address.dzongkhag')),
                ('gewog', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technical_clearance', to='address.gewog')),
                ('proposal_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technical_clearance', to='common.proposaltype')),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technical_clearance', to='address.region')),
                ('thromde', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technical_clearance', to='address.thromde')),
                ('use', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technical_clearance', to='common.use')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technical_clearances', to=settings.AUTH_USER_MODEL)),
                ('village', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='technical_clearance', to='address.village')),
            ],
            options={
                'verbose_name': 'clearance',
                'verbose_name_plural': 'clearances',
            },
        ),
    ]
