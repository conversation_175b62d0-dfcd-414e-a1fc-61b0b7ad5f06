from rest_framework import serializers
from common.models import BuildingType, ConstructionType, ProposalType, Use
from common.serializers import DesignTeamSearializer, SlabSerializer
from payment.serializers import PaymentSerializer
from planning.models import Application
from .models import Clearance
from drf_writable_nested.serializers import WritableNestedModelSerializer


class ClearanceSerializer(WritableNestedModelSerializer):
    construction_type_id = serializers.PrimaryKeyRelatedField(queryset=ConstructionType.objects.all(), source="construction_type", required=True)
    building_type_id = serializers.PrimaryKeyRelatedField(queryset=BuildingType.objects.all(), source="building_type", required=True)
    use_id = serializers.PrimaryKeyRelatedField(queryset=Use.objects.all(), source="use", required=True)
    proposal_type_id = serializers.PrimaryKeyRelatedField(queryset=ProposalType.objects.all(), source="proposal_type", required=True)
    design_teams = DesignTeamSearializer(many=True)
    payments = PaymentSerializer(many=True, read_only=True)
    application_id = serializers.PrimaryKeyRelatedField(queryset=Application.objects.all(), source="application", required=True)
    user_name = serializers.CharField(source="user.name", read_only=True)
    region_name = serializers.CharField(source="region.name", read_only=True)
    dzongkhag_name = serializers.CharField(source="dzongkhag.name", read_only=True)
    thromde_name = serializers.CharField(source="thromde.name", read_only=True)
    gewog_name = serializers.CharField(source="gewog.name", read_only=True)
    village_name = serializers.CharField(source="village.name", read_only=True)
    slabs = SlabSerializer(many=True, required=False, allow_empty=True, read_only=True)
    state_display = serializers.CharField(source="get_state_display", read_only=True)

    class Meta:
        model = Clearance
        fields = [
            "id",
            "hotel_type",
            "serial_no",
            "construction_id",
            "no_of_floors",
            "land_pooling",
            "fee",
            "state",
            "state_display",
            "created_at",
            "application_id",
            "user_id",
            "user_name",
            "region_id",
            "region_name",
            "dzongkhag_id",
            "dzongkhag_name",
            "thromde_id",
            "thromde_name",
            "gewog_id",
            "gewog_name",
            "village_id",
            "village_name",
            "construction_type_id",
            "building_type_id",
            "use_id",
            "proposal_type_id",
            "nature",
            "sustainable",
            "sustainable_remarks",
            "design_teams",
            "payments",
            "slabs",
        ]


class ClearanceListSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source="user.name", read_only=True)
    region_name = serializers.CharField(source="region.name", read_only=True)
    dzongkhag_name = serializers.CharField(source="dzongkhag.name", read_only=True)
    thromde_name = serializers.CharField(source="thromde.name", read_only=True)
    gewog_name = serializers.CharField(source="gewog.name", read_only=True)
    village_name = serializers.CharField(source="village.name", read_only=True)
    state_display = serializers.CharField(source="get_state_display", read_only=True)

    class Meta:
        model = Clearance
        fields = [
            "id",
            "serial_no",
            "construction_id",
            "state",
            "state_display",
            "created_at",
            "user_id",
            "user_name",
            "region_id",
            "region_name",
            "dzongkhag_id",
            "dzongkhag_name",
            "thromde_id",
            "thromde_name",
            "gewog_id",
            "gewog_name",
            "village_id",
            "village_name",
            "nature",
            "sustainable",
        ]
