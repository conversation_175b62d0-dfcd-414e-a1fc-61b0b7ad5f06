from django.db import models
from django.utils.translation import gettext_lazy as _


class ClearanceState(models.TextChoices):
    DRAFT = "draft", _("Draft")
    INITIATED = "initiated", _("Initiated")
    REQUESTED = "requested", _("Requested")
    ARCHITECT_ASSIGNED = "architect_assigned", _("Architect Assigned")
    FORWARDED = "forwarded", _("Forwarded ")
    APPROVED = "approved", _("Approved")
    REJECTED = "rejected", _("Rejected")
    PENDING_CHANGE = "pending_change", _("Pending Change")
    RESUBMITTED = "resubmitted", _("Re-submitted")
    PENDING_PAYMENT = "pending_payment", _("Pending Payment")
    CLOSED = "closed", _("Closed")
    CANCELLED = "cancelled", _("Cancelled")


class HotelType(models.TextChoices):
    BELOW_STAR_1 = "below_star_1", _("Below Star 1")
    STAR_1_2 = "star_1_2", _("Star 1-2")
    CAMPSITE = "campsite", _("Campsite")
    ECOLODGE = "ecolodge", _("Ecolodge")
    STAR_3_5 = "star_3_5", _("Star 3-5")
    TENTED = "tented", _("Tented Accommodation")
