from django.urls import path
from technical import views

urlpatterns = [
    path("clearances/", views.ClearanceView.as_view(), name="clearances"),
    path("clearances/<pk>/", views.ClearancePKView.as_view(), name="clearances"),
    path("clearances/<pk>/transition/", views.StateMachineViewSet.as_view({"put": "transition"}), name="transitions"),
    path("clearances/<pk>/activity_logs/", views.ClearanceViewSet.as_view({"get": "activity_logs"}), name="activity_logs"),
    path("clearances/<clearance_id>/certificate/", views.PdfView.as_view(), name="certificate"),
    path("clearances/draft", views.ClearanceViewSet.as_view({"get": "draft"}), name="draft"),
]
