from django.db.models.signals import post_save
from django.dispatch import receiver
from django_fsm.signals import post_transition
from cas_api.middleware.current_request import get_current_request
from common.helpers import determine_assignee_user, generate_payment
from common.models import TaskPool
from technical.enums import HotelType
from technical.models import Clearance
from technical.mailers import (
    notify_requested,
    notify_resubmitted,
    notify_architect_assigned,
    notify_approved,
    notify_rejected,
    notify_forwarded_to_dhs,
    notify_change,
    notify_payment,
)
from notifications.signals import notify
from common.helpers import is_task_completed
from common.helpers import create_slabs_set_fee


@receiver(post_save, sender=Clearance)
def clearance_post_save(sender, instance, created, **kwargs):
    by = getattr(get_current_request(), "user", None)
    if created:
        if instance.application and instance.application.construction_id:
            instance.construction_id = instance.application.construction_id
        instance.initial(by=instance.user)
        instance.save()
        if instance.nature == "urban":
            user, role = determine_assignee_user("tce", dzo=instance.dzongkhag, thrm=instance.thromde)
        else:
            if instance.hotel_type == HotelType.STAR_3_5:
                user, role = determine_assignee_user("dhsce")
            else:
                if instance.region:
                    user, role = determine_assignee_user("roidce", rgn=instance.region)
                else:
                    user, role = determine_assignee_user("dce", dzo=instance.dzongkhag)
        instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
        notify_requested.delay(instance.id, user.id)
        create_notification(instance, instance.user, "requested")
    elif not created:
        if getattr(by, "id", None) == instance.user_id and instance.state == "pending_change":
            change_tasks = instance.task_pools.filter(state="pending_change")
            instance.resubmit({}, by=instance.user)
            instance.save()
            for task in change_tasks:
                task.assign({}, by=instance.user)
                task.save()
                notify_resubmitted.delay(instance.id, task.user.id)
                create_notification(instance, task.user, "resubmited")


@receiver(post_transition, sender=Clearance)
def clearance_post_transition(sender, instance, name, source, target, **kwargs):
    by = getattr(get_current_request(), "user", None)
    if not name in ["payment", "change", "initial", "approve", "reject", "close"]:
        task = instance.task_pools.filter(user=by, state="in_progress").first()
        if task:
            task.approve({}, by=instance.by)
            task.save()
    if name == "approve":
        notify_approved.delay(instance.id)
    if name == "reject":
        notify_rejected.delay(instance.id)
    if name == "forward":
        user, role = determine_assignee_user("dhsce")
        instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
        notify_forwarded_to_dhs.delay(instance.id, user.id)
    if name == "assign_architect":
        role_name = by.current_role.name
        if role_name == "dce":
            user, role = determine_assignee_user("dar", user_id=instance.architect_id)
        if role_name == "tce":
            user, role = determine_assignee_user("tar", user_id=instance.architect_id)
        if role_name == "dhsce":
            user, role = determine_assignee_user("dhsar", user_id=instance.architect_id)
        if role_name == "roidce":
            user, role = determine_assignee_user("roidar", user_id=instance.architect_id)
        instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
        notify_architect_assigned.delay(instance.id, user.id)
    if name == "change":
        notify_change.delay(instance.id)
    if name == "payment":
        if instance.fee > 0:
            generate_payment("tc", instance.fee, instance)
            notify_payment.delay(instance.id)
        else:
            instance.pay()
    create_notification(instance, instance.user, target)


@receiver(post_transition, sender=TaskPool)
def task_pool_transition(sender, instance, name, source, target, **kwargs):
    if not isinstance(instance.poolable, Clearance):
        return
    clearance = instance.poolable
    by = getattr(get_current_request(), "user", None)
    role_name = instance.role.name
    if name == "approve":
        if role_name == "dar":
            task = clearance.task_pools.filter(role__name="dce").first()
            task.assign({}, by=instance.user)
            task.save()
            create_slabs_set_fee(clearance, getattr(instance, "data", {}))
            create_notification(clearance, task.user, target)
        if role_name == "tar":
            task = clearance.task_pools.filter(role__name="tce").first()
            task.assign({}, by=instance.user)
            task.save()
            create_slabs_set_fee(clearance, getattr(instance, "data", {}))
            create_notification(clearance, task.user, target)
        if role_name == "roidar":
            task = clearance.task_pools.filter(role__name="roidce").first()
            task.assign({}, by=instance.user)
            task.save()
            create_slabs_set_fee(clearance, getattr(instance, "data", {}))
            create_notification(clearance, task.user, target)
        if role_name == "dhsar":
            task = clearance.task_pools.filter(role__name="dhsce").first()
            task.assign({}, by=instance.user)
            task.save()
            create_slabs_set_fee(clearance, getattr(instance, "data", {}))
            create_notification(clearance, task.user, target)
        if role_name == "dce" and is_task_completed(clearance, "dar"):
            clearance.payment(instance.data, by=by)
            clearance.save()
        if role_name == "tce" and is_task_completed(clearance, "tar"):
            clearance.payment(instance.data, by=by)
            clearance.save()
        if role_name == "dhsce" and is_task_completed(clearance, "dhsar"):
            clearance.payment(instance.data, by=by)
            clearance.save()
        if role_name == "roidce" and is_task_completed(clearance, "roidar"):
            clearance.payment(instance.data, by=by)
            clearance.save()
    elif name == "change":
        clearance.change(instance.data, by=by)
        clearance.save()
    elif name == "reject":
        clearance.reject({"remarks": instance.remarks}, by=by)
        clearance.save()
    create_notification(clearance, instance.user, target)


def create_notification(clearance, recipient, action):
    url = f"/services/technical-approval/{clearance.id}"
    notify.send(getattr(clearance, "by", clearance.user), recipient=recipient, verb=action, action_object=clearance, target=clearance, url=url)
    notify.send(getattr(clearance, "by", clearance.user), recipient=clearance.user, verb=action, action_object=clearance, target=clearance, url=url)
