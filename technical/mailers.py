from django.template.loader import render_to_string
from cas_api.celery import app
from technical.models import Clearance
import os
from cas_api.services.mailer_service import send_mail
from user.models import User
from django.shortcuts import get_object_or_404
from django_fsm_log.models import StateLog


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_requested(self, clearance_id, user_id):
    try:
        clearance = get_object_or_404(Clearance, pk=clearance_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/technical-clearance/{clearance_id}"
        html_content = render_to_string(
            "mailers/technicals/notify_requested.html",
            {"name": user.name, "url": url, "serial_no": clearance.serial_no},
        )
        send_mail("New Task Assigned", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_requested")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_rejected(self, clearance_id):
    try:
        clearance = get_object_or_404(Clearance, pk=clearance_id)
        logs = StateLog.objects.for_(clearance)
        url = f"{os.getenv('HOST_URL', '')}/services/technical-clearance/{clearance_id}"
        html_content = render_to_string(
            "mailers/technicals/notify_rejected.html",
            {"name": clearance.user.name, "reason": getattr(logs.last(), "description", ""), "url": url},
        )
        send_mail("Technical clearance is rejected", html_content, [clearance.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_rejected")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_closed(self, clearance_id):
    try:
        clearance = get_object_or_404(Clearance, pk=clearance_id)
        url = f"{os.getenv('HOST_URL', '')}/services/technical-clearance/{clearance_id}"
        logs = StateLog.objects.for_(clearance)
        html_content = render_to_string(
            "mailers/technicals/notify_closed.html",
            {"name": clearance.user.name, "reason": getattr(logs.last(), "description", ""), "url": url},
        )
        send_mail("Technical clearance is closed", html_content, [clearance.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_closed")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_approved(self, clearance_id):
    try:
        clearance = get_object_or_404(Clearance, pk=clearance_id)
        url = f"{os.getenv('HOST_URL', '')}/services/technical-clearance/{clearance_id}"
        html_content = render_to_string("mailers/technicals/notify_approved.html", {"name": clearance.user.name, "url": url})
        send_mail("Technical clearance is approved", html_content, [clearance.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_approved")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forwarded_to_dhs(self, clearance_id, user_id):
    try:
        clearance = get_object_or_404(Clearance, pk=clearance_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/technical-clearance/{clearance_id}"
        html_content = render_to_string(
            "mailers/technicals/notify_forwarded_to_dhs.html",
            {"name": user.name, "reference": clearance.serial_no, "url": url},
        )
        send_mail("Technical clearance forwarded to Dzongkhag for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_forwarded_to_dhs")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forwarded_to_roid(self, clearance_id, user_id):
    try:
        clearance = get_object_or_404(Clearance, pk=clearance_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/technical-clearance/{clearance_id}"
        html_content = render_to_string(
            "mailers/technicals/notify_forwarded_to_roid.html",
            {"name": user.name, "reference": clearance.serial_no, "url": url},
        )
        send_mail("Technical clearance forwarded to ROID for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_forwarded_to_roid")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_architect_assigned(self, clearance_id, user_id):
    try:
        clearance = get_object_or_404(Clearance, pk=clearance_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/technical-clearance/{clearance_id}"
        html_content = render_to_string(
            "mailers/technicals/notify_architect_assigned.html",
            {"name": user.name, "reference": clearance.serial_no, "url": url},
        )
        send_mail("Technical clearance is assigned to you for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_architect_assigned")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_resubmitted(self, clearance_id, user_id):
    try:
        clearance = get_object_or_404(Clearance, pk=clearance_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/technical-clearance/{clearance.id}"
        html_content = render_to_string(
            "mailers/technicals/notify_resubmitted.html",
            {"name": user.name, "reference": clearance.serial_no, "url": url},
        )
        send_mail("Technical clearance is resubmitted for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_resubmitted")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_change(self, clearance_id):
    try:
        clearance = get_object_or_404(Clearance, pk=clearance_id)
        url = f"{os.getenv('HOST_URL', '')}/services/technical-clearance/{clearance.id}"
        html_content = render_to_string(
            "mailers/technicals/notify_change.html",
            {"name": clearance.user.name, "reference": clearance.serial_no, "url": url},
        )
        send_mail("Technical clearance is changed for approval", html_content, [clearance.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_change")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_payment(self, clearance_id):
    try:
        clearance = get_object_or_404(Clearance, pk=clearance_id)
        url = f"{os.getenv('HOST_URL', '')}/services/technical-clearance/{clearance.id}"
        html_content = render_to_string(
            "mailers/technicals/notify_payment.html",
            {"name": clearance.user.name, "reference": clearance.serial_no, "url": url},
        )
        send_mail("Technical clearance payment is pending", html_content, [clearance.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_payment")
