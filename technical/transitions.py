from django_fsm import transition
from django_fsm_log.decorators import fsm_log_by
from rest_framework.exceptions import ValidationError
from django_fsm_log.decorators import fsm_log_description


class Transition:
    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="initiated", target="requested")
    def initial(self, by=None, description=None):
        description.set("Technical clearance initiated.")
        self.by = by

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="pending_payment", target="approved")
    def approve(self, data, by=None, description=None):
        self.by = by
        self.data = data
        description.set(data.get("remarks", "Technical clearance approved."))
        self.save()

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="pending_change", target="resubmitted")
    def resubmit(self, data, by=None, description=None):
        description.set(data.get("remarks", "Application re-submitted"))
        self.by = by

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="rejected")
    def reject(self, data, by=None, description=None):
        if not data.get("remarks"):
            raise ValidationError({"error": "You should provide rejection remarks."})
        self.by = by
        description.set(data.get("remarks", "Application rejected."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="closed")
    def close(self, data, by=None, description=None):
        self.by = by
        description.set(data.get("remarks", "Technical clearance is closed."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["forwarded_to_roid", "forwarded_to_dhs", "requested", "resubmitted"], target="architect_assigned")
    def assign_architect(self, data, by=None, description=None):
        architect_id = data.get("architect_id")
        if not architect_id:
            raise ValidationError({"error": "Architect ID is required."})
        self.by = by
        self.architect_id = architect_id
        description.set(data.get("remarks", f"Technical clearance assigned to Architect by {by.name}"))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["requested", "resubmitted"], target="forwarded")
    def forward(self, data, by=None, description=None):
        self.by = by
        description.set(data.get("remarks", f"Technical clearance forwarded by {by.name}"))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="pending_change")
    def change(self, data, by=None, description=None):
        self.by = by
        self.data = data
        description.set(data.get("remarks", "Request for change"))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["architect_assigned", "forwarded", "requested", "resubmitted"], target="pending_payment")
    def payment(self, data, by=None, description=None):
        self.by = by
        self.data = data
        description.set(data.get("remarks", "Pending payment for Technical clearance."))
