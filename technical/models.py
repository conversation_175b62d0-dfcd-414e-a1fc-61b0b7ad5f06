from django.db import models
from django_fsm import F<PERSON><PERSON>ield
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.fields import GenericRelation
from common.enums import LandNature
from technical.enums import ClearanceState, HotelType
from technical.helpers.clearance import Helper
from technical.transitions import Transition


class Clearance(models.Model, Helper, Transition):
    application = models.ForeignKey("planning.Application", on_delete=models.SET_NULL, null=True, related_name="technical_clearance")
    user = models.ForeignKey("user.User", on_delete=models.SET_NULL, null=True, related_name="technical_clearances")
    serial_no = models.CharField(max_length=100, null=True, blank=True)
    construction_id = models.CharField(max_length=100, null=True, blank=True, help_text="Unique identifier for tracking construction across all modules")
    no_of_floors = models.IntegerField(null=True, blank=True)
    fee = models.FloatField(null=True, blank=True)
    land_pooling = models.BooleanField(default=False)
    hotel_type = models.CharField(max_length=100, null=True, blank=True, choices=HotelType.choices, default=HotelType.BELOW_STAR_1)
    construction_type = models.ForeignKey("common.ConstructionType", on_delete=models.SET_NULL, null=True, related_name="technical_clearance")
    building_type = models.ForeignKey("common.BuildingType", on_delete=models.SET_NULL, null=True, related_name="technical_clearance")
    use = models.ForeignKey("common.Use", on_delete=models.SET_NULL, null=True, related_name="technical_clearance")
    proposal_type = models.ForeignKey("common.ProposalType", on_delete=models.SET_NULL, null=True, related_name="technical_clearance")
    state = FSMField(max_length=50, null=False, choices=ClearanceState.choices, default=ClearanceState.INITIATED)
    region = models.ForeignKey("address.Region", null=True, on_delete=models.SET_NULL, blank=True, related_name="technical_clearance")
    dzongkhag = models.ForeignKey("address.Dzongkhag", null=True, on_delete=models.SET_NULL, blank=True, related_name="technical_clearance")
    thromde = models.ForeignKey("address.Thromde", null=True, on_delete=models.SET_NULL, blank=True, related_name="technical_clearance")
    gewog = models.ForeignKey("address.Gewog", null=True, on_delete=models.SET_NULL, blank=True, related_name="technical_clearance")
    village = models.ForeignKey("address.Village", null=True, on_delete=models.SET_NULL, blank=True, related_name="technical_clearance")
    task_pools = GenericRelation("common.TaskPool", related_query_name="technical_clearance")
    design_teams = GenericRelation("common.DesignTeam", related_query_name="technical_clearance")
    payments = GenericRelation("payment.Payment", related_query_name="technical_clearance")
    slabs = GenericRelation("common.Slab", related_query_name="design_permit")
    nature = models.CharField(max_length=50, null=True, choices=LandNature.choices, default=LandNature.RURAL)
    sustainable = models.BooleanField(default=False)
    sustainable_remarks = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("clearance")
        verbose_name_plural = _("clearances")
        indexes = [
            models.Index(fields=["created_at"]),
            models.Index(fields=["state"]),
            models.Index(fields=["dzongkhag"]),
            models.Index(fields=["gewog"]),
            models.Index(fields=["construction_id"]),
            models.Index(fields=["hotel_type"]),
        ]

    def __str__(self):
        return str(self.__dict__)
