import time
from technical.models import Clearance


def perform_save(self, serializer):
    clearance_id = self.kwargs.get("pk")
    clearance = Clearance.objects.get(id=clearance_id) if clearance_id else None
    application = serializer.validated_data.get("application") or clearance.application
    if not getattr(clearance, "dzongkhag_id", None) and application:
        serializer.validated_data["region_id"] = application.dzongkhag.region_id
        serializer.validated_data["dzongkhag_id"] = application.dzongkhag_id
        serializer.validated_data["thromde_id"] = application.thromde_id
        serializer.validated_data["gewog_id"] = application.gewog_id
        serializer.validated_data["village_id"] = application.village_id
        serializer.validated_data["nature"] = application.nature
    if not getattr(clearance, "serial_no", None):
        serializer.validated_data["serial_no"] = f"CAST{int(time.time() * 1000)}"
    if not getattr(clearance, "user_id", None):
        serializer.validated_data["user_id"] = self.request.user.id
