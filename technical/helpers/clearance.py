from common.enums import LandNature


class Helper:
    def can_approve(self, current_user):
        return True

    def can_reject(self, current_user):
        return True

    def can_review(self, current_user):
        return True

    def can_close(self, current_user):
        return True

    def can_change(self, current_user):
        return True

    def can_forward(self, current_user):
        current_role = current_user.current_role
        if (current_role.name == "tce" and self.nature == "urban") or (current_role.name == "dce" and self.nature == "rural"):
            return True
        else:
            return False

    def can_assign_architect(self, current_user):
        current_role = current_user.current_role
        return self.can_forward(current_user) or current_role.name in ["roidce", "dhsce"]

    @property
    def turn_around_time(self):
        from django.db import models
        import datetime

        turnaround_time = self.task_pools.aggregate(earliest_created_at=models.Min("created_at"), latest_created_at=models.Max("created_at"))
        return str((turnaround_time["latest_created_at"] or datetime.date.today()) - (turnaround_time["earliest_created_at"] or datetime.date.today()))

    @property
    def nature_text(self):
        return dict(LandNature.choices)[self.nature]

    def pay(self, payment=None):
        self.approve({}, by=self.user)
        self.save()
