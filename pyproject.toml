[tool.poetry]
name = "cas_api"
version = "0.1.0"
description = "Construction Approval System API"
authors = ["<PERSON><PERSON> Dendup <<EMAIL>>"]
license = "MIT"
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
djangorestframework = "3.14.0"
drf-writable-nested = "0.7.0"
faker = "14.2.0"
django = "4.1.7"
django-cors-headers = "3.13.0"
pillow = "9.4.0"
python-dotenv = "0.21.0"
nested-multipart-parser = "1.4.1"
djangorestframework-simplejwt = "5.2.2"
django-fsm = "2.8.1"
django-fsm-log = "3.0.0"
celery = "5.2.7"
redis = "4.5.4"
factory-boy = "3.2.1"
pylint = "2.17.4"
psycopg2-binary = "^2.9.6"
django-notifications-hq = "^1.8.2"
python-levenshtein = "^0.23.0"
django-weasyprint = "^2.2.1"
gunicorn = "^21.2.0"
inflect = "^7.5.0"
requests = "^2.32.3"
drf-yasg = "^1.21.10"
django-pandas = "^0.6.6"
pandas = "^2.0.0"
openpyxl = "^3.1.5"

[tool.poetry.dev-dependencies]
ipdb = "0.13.11"
django-mail-viewer = "^2.2.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
