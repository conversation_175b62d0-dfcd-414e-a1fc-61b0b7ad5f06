`git fetch && git checkout feature/payment`

1. What do I understand the task goals are?
   - As an applicant, I should get an email notification as soon as my application gets approved by a different agency to make a final payment.
2. How many hours are necessary to complete the task?
   - 8 hours
3. What date do I estimate the task will be ready for QA?
   - Dec 15, 2023.
4. When should the team expect the Merge Request to be delivered?
   - Dec 17, 2023.
5. Who will be reviewing the Merge Request?
   - @all
6. What files will be affected/created during the development of tasks?
   - The following new files will be created:
     1. `museum/serializers.py`
     2. `payment/models.py`
     3. `payment/views.py`
     4. `payment/urls.py`
     5. `payment/app.py`
     6. `payment/admin.py`
   - The following file will be changed
     1. `.env.example`
     2. `cas_api/libs/ndi_token.py`
     3. `cas_api/libs/token.py`
7. What libraries are necessary to include? (Gems or npm packages)
   - N\A.
8. If applicable, what new tables or columns will be included in the database?
   - Yes, a new table `payments` will be created.
9. If it is a bug ticket, what ticket introduced this issue?
   - N\A.
10. What negative behaviour am I expecting and how will I address them?
    - This task should be straight forward solution for CRUD operations and should not encounter any negative behavior.
