# Name of nodes to start
# here we have a single node
CELERYD_NODES="worker"
# or we could have three nodes:
#CELERYD_NODES="w1 w2 w3"

# Absolute or relative path to the 'celery' command:
CELERY_BIN="/home/<USER>/Documents/cas/cas-api/cas-env/bin/celery"
#CELERY_BIN="/virtualenvs/def/bin/celery"

# App instance to use
# comment out this line if you don't use an app
CELERY_APP="cas_api"
# or fully qualified:
#CELERY_APP="proj.tasks:app"

# How to call manage.py
CELERYD_MULTI="multi"

# Extra command-line arguments to the worker
CELERYD_OPTS="--time-limit=300 --concurrency=8"

# - %n will be replaced with the first part of the nodename.
# - %I will be replaced with the current child process index
#   and is important when using the prefork pool to avoid race conditions.
CELERYD_PID_FILE="/home/<USER>/Documents/cas/cas-api/run/celery/%n.pid"
CELERYD_LOG_FILE="/home/<USER>/Documents/cas/cas-api/log/celery/%n%I.log"
CELERYD_LOG_LEVEL="INFO"

# you may wish to add these options for Celery Beat
CELERYBEAT_PID_FILE="/home/<USER>/Documents/cas/cas-api/run/celery/beat.pid"
CELERYBEAT_LOG_FILE="/home/<USER>/Documents/cas/cas-api/log/celery/beat.log"
