# Comments are provided throughout this file to help you get started.
# If you need more help, visit the Docker compose reference guide at
# https://docs.docker.com/compose/compose-file/

# Here the instructions define your application as a service called "server".
# This service is built from the Dockerfile in the current directory.
# You can add other services your application may depend on here, such as a
# database or a cache. For examples, see the Awesome Compose repository:
# https://github.com/docker/awesome-compose
# services:
#   server:
#     build:
#       context: .
#     ports:
#       - 8000:8000

# The commented out section below is an example of how to define a PostgreSQL
# database that your application can use. `depends_on` tells Docker Compose to
# start the database before your application. The `db-data` volume persists the
# database data between container restarts. The `db-password` secret is used
# to set the database password. You must create `db/password.txt` and add
# a password of your choosing to it before running `docker compose up`.
#     depends_on:
#       db:
#         condition: service_healthy
#   db:
#     image: postgres
#     restart: always
#     user: postgres
#     secrets:
#       - db-password
#     volumes:
#       - db-data:/var/lib/postgresql/data
#     environment:
#       - POSTGRES_DB=example
#       - POSTGRES_PASSWORD_FILE=/run/secrets/db-password
#     expose:
#       - 5432
#     healthcheck:
#       test: [ "CMD", "pg_isready" ]
#       interval: 10s
#       timeout: 5s
#       retries: 5
# volumes:
#   db-data:
# secrets:
#   db-password:
#     file: db/password.txt


version: "3.9"

services:
  db:
    image: postgres
    restart: always
    volumes:
      - ./tmp/db:/var/lib/postgresql/data
      - ./initdb:/docker-entrypoint-initdb.d
    environment:
      - POSTGRES_DB=cas_api_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    env_file:
      - .env
    ports:
      - "5432:5432"
  api:
    build: .
    command: /bin/sh -c "poetry run python manage.py migrate && poetry run gunicorn cas_api.wsgi:application --bind 0.0.0.0:8000 --workers 3 --timeout 60 --log-level=error"
    restart: always
    volumes:
      - .:/api
      - file_server_upload:/api/uploads/
    ports:
      - "8000:8000"
    depends_on:
      - db
    extra_hosts:
      - "somehost:**************"
      - "otherhost:*************"
  nginx:
    image: nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./data/nginx:/etc/nginx/conf.d
      - ./data/certbot/conf:/etc/letsencrypt
    command: "/bin/sh -c 'while :; do sleep 6h & wait $${!}; nginx -s reload; done & nginx -g \"daemon off;\"'"
  certbot:
    image: certbot/certbot
    restart: always
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"
  redis:
    image: redis
    command: sh -c '[[ "$USE_ELASTICACHE" == true ]] || redis-server --requirepass Cas2023!'
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - .:/redis
    env_file:
      - .env
  celery:
    build:
      context: .
    volumes:
      - ./api:/app
    command: /root/.local/bin/poetry run celery -A cas_api worker --loglevel=INFO --time-limit=300 --concurrency=8
    restart: always
    depends_on:
      - redis
      - api
    links:
      - redis
    env_file:
      - .env
volumes:
  file_server_upload:
    driver: local
    driver_opts:
      type: none
      device: /home/<USER>/storage/uploads
      o: bind