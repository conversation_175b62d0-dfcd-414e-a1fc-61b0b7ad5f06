# Import necessary libraries
import pygame
import sys
import time
import random

# Initialize Pygame
pygame.init()

# Set up some constants
WIDTH, HEIGHT = 800, 600
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)

# Set up the display
win = pygame.display.set_mode((WIDTH, HEIGHT))

# Set up the font
font = pygame.font.Font(None, 36)

# Set up the snake and food
snake = [(200, 200), (220, 200), (240, 200)]
food = (400, 300)

# Set up the direction
direction = "RIGHT"

# Game loop
while True:
    # Handle events
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            pygame.quit()
            sys.exit()
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_UP and direction != "DOWN":
                direction = "UP"
            elif event.key == pygame.K_DOWN and direction != "UP":
                direction = "DOWN"
            elif event.key == pygame.K_LEFT and direction != "RIGHT":
                direction = "LEFT"
            elif event.key == pygame.K_RIGHT and direction != "LEFT":
                direction = "RIGHT"

    # Move the snake
    head = snake[-1]
    if direction == "UP":
        new_head = (head[0], head[1] - 20)
    elif direction == "DOWN":
        new_head = (head[0], head[1] + 20)
    elif direction == "LEFT":
        new_head = (head[0] - 20, head[1])
    elif direction == "RIGHT":
        new_head = (head[0] + 20, head[1])

    snake.append(new_head)

    # Check if the snake ate the food
    if snake[-1] == food:
        food = (random.randint(0, WIDTH - 20) // 20 * 20, random.randint(0, HEIGHT - 20) // 20 * 20)
    else:
        snake.pop(0)

    # Check if the snake ran into the edge of the window or its own tail
    if snake[-1][0] < 0 or snake[-1][0] >= WIDTH or snake[-1][1] < 0 or snake[-1][1] >= HEIGHT or snake[-1] in snake[:-1]:
        print("Game over!")
        pygame.quit()
        sys.exit()

    # Draw everything
    win.fill(BLACK)
    for pos in snake:
        pygame.draw.rect(win, GREEN, (pos[0], pos[1], 20, 20))
    pygame.draw.rect(win, RED, (food[0], food[1], 20, 20))
    text = font.render(f"Score: {len(snake)}", True, WHITE)
    win.blit(text, (10, 10))

    # Update the display
    pygame.display.update()

    # Cap the frame rate
    time.sleep(1 / 10)
