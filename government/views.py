from rest_framework import generics
from rest_framework.filters import SearchFilter
from government.models import Structure
from government.serializers import StructureSerializer, StructureListSerializer


class StructureView(generics.ListCreateAPIView):
    serializer_class = StructureSerializer
    filter_backends = [SearchFilter]
    search_fields = (
        "serial_no",
        "organization_name",
        "department_name",
        "organization_type",
        "status",
        "user__first_name",
        "user__last_name",
        "user__email",
        "dzongkhag__name",
        "thromde__name",
        "gewog__name",
        "village__name",
    )

    def get_serializer_class(self):
        if self.request.method == "GET":
            return StructureListSerializer
        elif self.request.method == "POST":
            return StructureSerializer

    def get_queryset(self):
        return Structure.objects.all()

    def perform_create(self, serializer):
        serializer.validated_data["user_id"] = self.request.user.id
        serializer.save(user=self.request.user)


class StructurePKView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = StructureSerializer
    queryset = Structure.objects.all()
