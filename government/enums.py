from django.db import models
from django.utils.translation import gettext_lazy as _


class OrganizationType(models.TextChoices):
    RBP = "rbp", _("Royal Bhutan Police")
    RBG = "rbg", _("Royal Bhutan Government")
    RBA = "rba", _("Royal Bhutan Army")
    MINISTRY = "ministry", _("Ministry")
    DEPARTMENT = "department", _("Department")
    AGENCY = "agency", _("Government Agency")
    CORPORATION = "corporation", _("Government Corporation")
    COMMISSION = "commission", _("Government Commission")
    COUNCIL = "council", _("Government Council")
    AUTHORITY = "authority", _("Government Authority")
    OTHER = "other", _("Other Government Structure")


class Status(models.TextChoices):
    ACTIVE = "active", _("Active")
    INACTIVE = "inactive", _("Inactive")
    UNDER_CONSTRUCTION = "under_construction", _("Under Construction")
    COMPLETED = "completed", _("Completed")
    DEMOLISHED = "demolished", _("Demolished")
    RENOVATED = "renovated", _("Renovated")
