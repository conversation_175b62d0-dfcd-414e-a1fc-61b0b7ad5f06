# Generated by Django 4.1.7 on 2025-05-25 12:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import government.helpers.structure


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('common', '0013_rename_cdb_no_designteam_bcta_no'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('address', '0010_region_dzongkhag_created_at_dzongkhag_updated_at_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Structure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('organization_type', models.CharField(choices=[('rbp', 'Royal Bhutan Police'), ('rbg', 'Royal Bhutan Government'), ('rba', 'Royal Bhutan Army'), ('ministry', 'Ministry'), ('department', 'Department'), ('agency', 'Government Agency'), ('corporation', 'Government Corporation'), ('commission', 'Government Commission'), ('council', 'Government Council'), ('authority', 'Government Authority'), ('other', 'Other Government Structure')], help_text='Type of government organization', max_length=50)),
                ('organization_name', models.CharField(help_text='Name of the government organization', max_length=200)),
                ('department_name', models.CharField(blank=True, help_text='Department or division name', max_length=200, null=True)),
                ('serial_no', models.CharField(blank=True, max_length=100, null=True)),
                ('construction_id', models.CharField(blank=True, help_text='Unique identifier for tracking construction across all modules', max_length=100, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('under_construction', 'Under Construction'), ('completed', 'Completed'), ('demolished', 'Demolished'), ('renovated', 'Renovated')], default='active', max_length=50)),
                ('no_of_floors', models.IntegerField(blank=True, null=True)),
                ('no_of_units', models.IntegerField(blank=True, null=True)),
                ('total_area', models.DecimalField(blank=True, decimal_places=2, help_text='Total area in square meters', max_digits=10, null=True)),
                ('built_up_area', models.DecimalField(blank=True, decimal_places=2, help_text='Built-up area in square meters', max_digits=10, null=True)),
                ('nature', models.CharField(choices=[('urban', 'Urban'), ('rural', 'Rural')], default='rural', max_length=50)),
                ('land_pooling', models.BooleanField(default=False)),
                ('construction_start_date', models.DateField(blank=True, null=True)),
                ('construction_completion_date', models.DateField(blank=True, null=True)),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=2, help_text='Estimated construction cost in BTN', max_digits=15, null=True)),
                ('purpose', models.TextField(blank=True, help_text='Purpose or function of the building', null=True)),
                ('description', models.TextField(blank=True, help_text='Additional description of the structure', null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('building_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='government_structures', to='common.buildingtype')),
                ('construction_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='government_structures', to='common.constructiontype')),
                ('dzongkhag', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='government_structures', to='address.dzongkhag')),
                ('gewog', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='government_structures', to='address.gewog')),
                ('proposal_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='government_structures', to='common.proposaltype')),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='government_structures', to='address.region')),
                ('thromde', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='government_structures', to='address.thromde')),
                ('use', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='government_structures', to='common.use')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='government_structures', to=settings.AUTH_USER_MODEL)),
                ('village', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='government_structures', to='address.village')),
            ],
            options={
                'verbose_name': 'government structure',
                'verbose_name_plural': 'government structures',
                'ordering': ['-created_at'],
            },
            bases=(models.Model, government.helpers.structure.Helper),
        ),
    ]
