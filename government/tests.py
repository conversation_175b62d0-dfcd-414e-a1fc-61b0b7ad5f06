from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from user.factories import UserFactory
from address.factories import DzongkhagFactory, GewogFactory, VillageFactory
from common.factories import ConstructionTypeFactory, BuildingTypeFactory, UseFactory
from government.models import Structure
from government.enums import OrganizationType, Status


class StructureModelTest(TestCase):
    """
    Test cases for Structure model
    """

    def setUp(self):
        self.user = UserFactory()
        self.dzongkhag = DzongkhagFactory()
        self.gewog = GewogFactory(dzongkhag=self.dzongkhag)
        self.village = VillageFactory(gewog=self.gewog)
        self.construction_type = ConstructionTypeFactory()
        self.building_type = BuildingTypeFactory()
        self.use = UseFactory()

    def test_structure_creation(self):
        """
        Test creating a government structure
        """
        structure = Structure(
            organization_type=OrganizationType.RBP,
            organization_name="Royal Bhutan Police Headquarters",
            department_name="Administration",
            user=self.user,
            dzongkhag=self.dzongkhag,
            gewog=self.gewog,
            village=self.village,
            construction_type=self.construction_type,
            building_type=self.building_type,
            use=self.use,
            no_of_floors=3,
            total_area=1500.50,
            status=Status.ACTIVE,
        )
        structure.save()

        self.assertEqual(structure.organization_name, "Royal Bhutan Police Headquarters")
        self.assertEqual(structure.organization_type, OrganizationType.RBP)
        self.assertEqual(structure.status, Status.ACTIVE)
        self.assertIsNotNone(structure.serial_no)
        self.assertIsNotNone(structure.construction_id)

    def test_structure_str_method(self):
        """
        Test string representation of structure
        """
        structure = Structure.objects.create(organization_type=OrganizationType.RBA, organization_name="Royal Bhutan Army Base", user=self.user)

        expected_str = "Royal Bhutan Army Base - Royal Bhutan Army"
        self.assertEqual(str(structure), expected_str)

    def test_location_display(self):
        """
        Test location display helper method
        """
        structure = Structure.objects.create(
            organization_type=OrganizationType.RBG, organization_name="Government Office", user=self.user, dzongkhag=self.dzongkhag, gewog=self.gewog, village=self.village
        )

        location_display = structure.get_location_display()
        self.assertIn(str(self.village.name), location_display)
        self.assertIn(str(self.gewog.name), location_display)
        self.assertIn(str(self.dzongkhag.name), location_display)

    def test_construction_duration(self):
        """
        Test construction duration calculation
        """
        from datetime import date

        structure = Structure.objects.create(
            organization_type=OrganizationType.MINISTRY,
            organization_name="Ministry Building",
            user=self.user,
            construction_start_date=date(2023, 1, 1),
            construction_completion_date=date(2023, 12, 31),
        )

        duration = structure.get_construction_duration()
        self.assertEqual(duration, 364)  # 2023 is not a leap year


class StructureAPITest(APITestCase):
    """
    Test cases for Structure API endpoints
    """

    def setUp(self):
        self.user = UserFactory()
        self.client.force_authenticate(user=self.user)
        self.dzongkhag = DzongkhagFactory()
        self.construction_type = ConstructionTypeFactory()
        self.building_type = BuildingTypeFactory()
        self.use = UseFactory()

    def test_create_structure(self):
        """
        Test creating a structure via API
        """
        url = reverse("structures")
        data = {
            "organization_type": OrganizationType.RBP,
            "organization_name": "Police Station",
            "department_name": "Traffic Division",
            "dzongkhag_id": self.dzongkhag.id,
            "construction_type_id": self.construction_type.id,
            "building_type_id": self.building_type.id,
            "use_id": self.use.id,
            "no_of_floors": 2,
            "total_area": "500.00",
            "status": Status.ACTIVE,
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Structure.objects.count(), 1)

        structure = Structure.objects.first()
        self.assertEqual(structure.organization_name, "Police Station")
        self.assertEqual(structure.user, self.user)

    def test_list_structures(self):
        """
        Test listing structures via API
        """
        # Create test structures
        Structure.objects.create(organization_type=OrganizationType.RBP, organization_name="Police HQ", user=self.user)
        Structure.objects.create(organization_type=OrganizationType.RBA, organization_name="Army Base", user=self.user)

        url = reverse("structures")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["data"]), 2)

    def test_create_structure_with_slabs(self):
        """
        Test creating a structure with slabs via API
        """
        from common.factories import FloorTypeFactory, UseFactory

        floor_type = FloorTypeFactory()
        usage = UseFactory()

        url = reverse("structures")
        data = {
            "organization_type": OrganizationType.RBP,
            "organization_name": "Police Station with Slabs",
            "dzongkhag_id": self.dzongkhag.id,
            "slabs": [{"floor_type": floor_type.id, "usage": usage.id, "area": "100.50", "fee": "5000.00"}],
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        structure = Structure.objects.first()
        self.assertEqual(structure.slabs.count(), 1)
        slab = structure.slabs.first()
        self.assertEqual(float(slab.area), 100.50)
        self.assertEqual(float(slab.fee), 5000.00)
