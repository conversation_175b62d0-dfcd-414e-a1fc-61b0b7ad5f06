# Government Structures App

This Django app is designed to capture and manage building information for government structures including RBP (Royal Bhutan Police), RBG (Royal Bhutan Government), RBA (Royal Bhutan Army), and other government organizations.

## Features

- **Data Collection**: Capture comprehensive building information for government structures
- **Organization Types**: Support for various government organization types (RBP, RBG, RBA, Ministries, Departments, etc.)
- **Building Details**: Store construction type, building type, usage, specifications, and location information
- **Status Tracking**: Track the status of structures (Active, Under Construction, Completed, etc.)
- **API Endpoints**: RESTful API for CRUD operations and statistics
- **Admin Interface**: Django admin interface for easy management

## Models

### Structure

The main model that captures all building information:

- **Organization Information**: Type, name, department
- **Location**: Region, Dzongkhag, Thromde, Gewog, Village
- **Building Details**: Construction type, building type, use, proposal type
- **Specifications**: Number of floors, units, total area, built-up area
- **Construction Details**: Start/completion dates, estimated cost
- **Status**: Current status of the structure

## API Endpoints

- `GET /api/v1/government/structures/` - List all structures
- `POST /api/v1/government/structures/` - Create a new structure
- `GET /api/v1/government/structures/{id}/` - Retrieve a specific structure
- `PUT /api/v1/government/structures/{id}/` - Update a structure
- `DELETE /api/v1/government/structures/{id}/` - Delete a structure

## Organization Types

- RBP (Royal Bhutan Police)
- RBG (Royal Bhutan Government)
- RBA (Royal Bhutan Army)
- Ministry
- Department
- Government Agency
- Government Corporation
- Government Commission
- Government Council
- Government Authority
- Other Government Structure

## Status Options

- Active
- Inactive
- Under Construction
- Completed
- Demolished
- Renovated

## Usage

### Creating a Structure

```python
from government.models import Structure
from government.enums import OrganizationType, Status

structure = Structure.objects.create(
    organization_type=OrganizationType.RBP,
    organization_name="Police Headquarters",
    department_name="Administration",
    user=user,
    status=Status.ACTIVE,
    no_of_floors=3,
    total_area=1500.00
)
```

### API Usage

```bash
# Create a new structure
curl -X POST /api/v1/government/structures/ \
  -H "Content-Type: application/json" \
  -d '{
    "organization_type": "rbp",
    "organization_name": "Police Station",
    "status": "active",
    "no_of_floors": 2
  }'

# Get statistics
curl /api/v1/government/structures/statistics/
```

## Testing

Run the tests with:

```bash
python manage.py test government
```

## Installation

1. The app is already included in `INSTALLED_APPS` in settings.py
2. Run migrations: `python manage.py migrate government`
3. The API endpoints are available at `/api/v1/government/`

## Admin Interface

Access the Django admin interface to manage structures at `/admin/government/structure/`
