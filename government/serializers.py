from rest_framework import serializers
from address.models import Dzongkhag, Gewog, Region, Thromde, Village
from common.models import BuildingType, ConstructionType, ProposalType, Use
from common.serializers import SlabSerializer
from government.models import Structure
from drf_writable_nested.serializers import WritableNestedModelSerializer


class StructureSerializer(WritableNestedModelSerializer):
    """
    Serializer for Government Structure model
    """

    # File fields for related models
    construction_type_file = serializers.FileField(source="construction_type.file", read_only=True)
    building_type_file = serializers.FileField(source="building_type.file", read_only=True)
    use_file = serializers.FileField(source="use.file", read_only=True)
    proposal_type_file = serializers.FileField(source="proposal_type.file", read_only=True)

    # Nested serializers
    slabs = SlabSerializer(many=True, required=False)

    # Display fields
    organization_type_display = serializers.CharField(source="get_organization_type_display", read_only=True)
    status_display = serializers.Char<PERSON>ield(source="get_status_display", read_only=True)
    nature_display = serializers.CharField(source="get_nature_display", read_only=True)

    # Helper method fields
    location_display = serializers.CharField(source="get_location_display", read_only=True)
    building_info_display = serializers.CharField(source="get_building_info_display", read_only=True)
    construction_duration = serializers.IntegerField(source="get_construction_duration", read_only=True)
    is_construction_completed = serializers.BooleanField(read_only=True)

    region_id = serializers.PrimaryKeyRelatedField(queryset=Region.objects.all(), source="region", required=False, allow_null=True)
    dzongkhag_id = serializers.PrimaryKeyRelatedField(queryset=Dzongkhag.objects.all(), source="dzongkhag", required=False, allow_null=True)
    thromde_id = serializers.PrimaryKeyRelatedField(queryset=Thromde.objects.all(), source="thromde", required=False, allow_null=True)
    gewog_id = serializers.PrimaryKeyRelatedField(queryset=Gewog.objects.all(), source="gewog", required=False, allow_null=True)
    village_id = serializers.PrimaryKeyRelatedField(queryset=Village.objects.all(), source="village", required=False, allow_null=True)
    construction_type_id = serializers.PrimaryKeyRelatedField(queryset=ConstructionType.objects.all(), source="construction_type", required=False, allow_null=True)
    building_type_id = serializers.PrimaryKeyRelatedField(queryset=BuildingType.objects.all(), source="building_type", required=False, allow_null=True)
    use_id = serializers.PrimaryKeyRelatedField(queryset=Use.objects.all(), source="use", required=False, allow_null=True)
    proposal_type_id = serializers.PrimaryKeyRelatedField(queryset=ProposalType.objects.all(), source="proposal_type", required=False, allow_null=True)

    region_name = serializers.CharField(source="region.name", read_only=True)
    dzongkhag_name = serializers.CharField(source="dzongkhag.name", read_only=True)
    thromde_name = serializers.CharField(source="thromde.name", read_only=True)
    gewog_name = serializers.CharField(source="gewog.name", read_only=True)
    village_name = serializers.CharField(source="village.name", read_only=True)
    construction_type_name = serializers.CharField(source="construction_type.name", read_only=True)
    building_type_name = serializers.CharField(source="building_type.name", read_only=True)
    use_name = serializers.CharField(source="use.name", read_only=True)
    proposal_type_name = serializers.CharField(source="proposal_type.name", read_only=True)

    class Meta:
        model = Structure
        fields = [
            "id",
            "organization_type",
            "organization_type_display",
            "organization_name",
            "department_name",
            "user_id",
            "serial_no",
            "construction_id",
            "status",
            "status_display",
            "region_id",
            "region_name",
            "dzongkhag_id",
            "dzongkhag_name",
            "thromde_id",
            "thromde_name",
            "gewog_id",
            "gewog_name",
            "village_id",
            "village_name",
            "construction_type_id",
            "construction_type_name",
            "building_type_id",
            "building_type_name",
            "use_id",
            "use_name",
            "proposal_type_id",
            "proposal_type_name",
            "construction_type_file",
            "building_type_file",
            "use_file",
            "proposal_type_file",
            "no_of_floors",
            "no_of_units",
            "total_area",
            "built_up_area",
            "nature",
            "nature_display",
            "land_pooling",
            "construction_start_date",
            "construction_completion_date",
            "estimated_cost",
            "purpose",
            "description",
            "remarks",
            "location_display",
            "building_info_display",
            "construction_duration",
            "is_construction_completed",
            "slabs",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["serial_no", "construction_id", "created_at", "updated_at"]

    # def create(self, validated_data):
    #     """
    #     Create structure with slabs
    #     """
    #     slabs_data = validated_data.pop("slabs", [])
    #     structure = Structure.objects.create(**validated_data)

    #     # Create slabs
    #     for slab_data in slabs_data:
    #         # Set the generic foreign key fields
    #         from django.contrib.contenttypes.models import ContentType

    #         slab_data["content_type"] = ContentType.objects.get_for_model(structure)
    #         slab_data["object_id"] = structure.id

    #         Slab.objects.create(**slab_data)

    #     return structure

    # def update(self, instance, validated_data):
    #     """
    #     Update structure with slabs
    #     """
    #     slabs_data = validated_data.pop("slabs", [])

    #     # Update structure fields
    #     for attr, value in validated_data.items():
    #         setattr(instance, attr, value)
    #     instance.save()

    #     # Update slabs - delete existing and create new ones
    #     if slabs_data:
    #         instance.slabs.all().delete()
    #         for slab_data in slabs_data:
    #             # Set the generic foreign key fields
    #             from django.contrib.contenttypes.models import ContentType

    #             slab_data["content_type"] = ContentType.objects.get_for_model(instance)
    #             slab_data["object_id"] = instance.id

    #             Slab.objects.create(**slab_data)

    #     return instance


class StructureListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for listing government structures
    """

    organization_type_display = serializers.CharField(source="get_organization_type_display", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    location_display = serializers.CharField(source="get_location_display", read_only=True)

    class Meta:
        model = Structure
        fields = [
            "id",
            "organization_type",
            "organization_type_display",
            "organization_name",
            "department_name",
            "serial_no",
            "construction_id",
            "status",
            "status_display",
            "location_display",
            "no_of_floors",
            "total_area",
            "created_at",
            "updated_at",
        ]
