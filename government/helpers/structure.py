import uuid
from datetime import datetime


class Helper:
    """
    Helper class for Government Structure model
    """

    def save(self, *args, **kwargs):
        """
        Override save method to generate serial number and construction_id
        """
        if not self.serial_no:
            # Generate serial number with format: GS-YYYY-XXXXXX
            year = datetime.now().year
            random_part = str(uuid.uuid4())[:6].upper()
            self.serial_no = f"GS-{year}-{random_part}"
        super().save(*args, **kwargs)

    def get_location_display(self):
        """
        Get formatted location display
        """
        location_parts = []
        if self.village and hasattr(self.village, "name"):
            location_parts.append(str(self.village.name))
        if self.gewog and hasattr(self.gewog, "name"):
            location_parts.append(str(self.gewog.name))
        if self.thromde and hasattr(self.thromde, "name"):
            location_parts.append(str(self.thromde.name))
        if self.dzongkhag and hasattr(self.dzongkhag, "name"):
            location_parts.append(str(self.dzongkhag.name))
        return ", ".join(location_parts) if location_parts else "Location not specified"

    def get_building_info_display(self):
        """
        Get formatted building information display
        """
        info_parts = []
        if self.construction_type and hasattr(self.construction_type, "name"):
            info_parts.append(f"Construction: {str(self.construction_type.name)}")
        if self.building_type and hasattr(self.building_type, "name"):
            info_parts.append(f"Building: {str(self.building_type.name)}")
        if self.use and hasattr(self.use, "name"):
            info_parts.append(f"Use: {str(self.use.name)}")
        if self.no_of_floors:
            info_parts.append(f"Floors: {self.no_of_floors}")
        return " | ".join(info_parts) if info_parts else "Building info not specified"

    def is_construction_completed(self):
        """
        Check if construction is completed
        """
        return self.status == "completed" and self.construction_completion_date is not None

    def get_construction_duration(self):
        """
        Get construction duration in days
        """
        if self.construction_start_date and self.construction_completion_date:
            return (self.construction_completion_date - self.construction_start_date).days
        return None
