import uuid
from datetime import datetime
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.fields import GenericRelation
from common.enums import LandNature
from government.enums import OrganizationType, Status
from government.helpers.structure import Helper


class Structure(models.Model, Helper):
    """
    Model for capturing building information of RBP, RBG, RBA and other government structures
    """

    # Basic Information
    organization_type = models.Char<PERSON>ield(max_length=50, choices=OrganizationType.choices, help_text="Type of government organization")
    organization_name = models.CharField(max_length=200, help_text="Name of the government organization")
    department_name = models.CharField(max_length=200, null=True, blank=True, help_text="Department or division name")

    # User and tracking
    user = models.ForeignKey("user.User", on_delete=models.CASCADE, related_name="government_structures")
    serial_no = models.Char<PERSON>ield(max_length=100, null=True, blank=True)
    construction_id = models.Char<PERSON><PERSON>(max_length=100, null=True, blank=True, help_text="Unique identifier for tracking construction across all modules")

    # Status
    status = models.Char<PERSON>ield(max_length=50, choices=Status.choices, default=Status.ACTIVE)

    # Location Information
    region = models.ForeignKey("address.Region", null=True, on_delete=models.SET_NULL, blank=True, related_name="government_structures")
    dzongkhag = models.ForeignKey("address.Dzongkhag", null=True, on_delete=models.SET_NULL, blank=True, related_name="government_structures")
    thromde = models.ForeignKey("address.Thromde", null=True, on_delete=models.SET_NULL, blank=True, related_name="government_structures")
    gewog = models.ForeignKey("address.Gewog", null=True, on_delete=models.SET_NULL, blank=True, related_name="government_structures")
    village = models.ForeignKey("address.Village", null=True, on_delete=models.SET_NULL, blank=True, related_name="government_structures")

    # Building Information
    construction_type = models.ForeignKey("common.ConstructionType", on_delete=models.SET_NULL, null=True, related_name="government_structures")
    building_type = models.ForeignKey("common.BuildingType", on_delete=models.SET_NULL, null=True, related_name="government_structures")
    use = models.ForeignKey("common.Use", on_delete=models.SET_NULL, null=True, related_name="government_structures")
    proposal_type = models.ForeignKey("common.ProposalType", on_delete=models.SET_NULL, null=True, related_name="government_structures")

    # Building specifications
    no_of_floors = models.IntegerField(null=True, blank=True)
    no_of_units = models.IntegerField(null=True, blank=True)
    total_area = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Total area in square meters")
    built_up_area = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Built-up area in square meters")

    # Land information
    nature = models.CharField(max_length=50, choices=LandNature.choices, default=LandNature.RURAL)
    land_pooling = models.BooleanField(default=False)

    # Construction details
    construction_start_date = models.DateField(null=True, blank=True)
    construction_completion_date = models.DateField(null=True, blank=True)
    estimated_cost = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True, help_text="Estimated construction cost in BTN")

    # Additional information
    purpose = models.TextField(null=True, blank=True, help_text="Purpose or function of the building")
    description = models.TextField(null=True, blank=True, help_text="Additional description of the structure")
    remarks = models.TextField(null=True, blank=True)

    # Generic relations for dynamic associations
    slabs = GenericRelation("common.Slab", related_query_name="government_structure")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        """
        Override save method to generate serial number and construction_id
        """
        if not self.serial_no:
            # Generate serial number with format: GS-YYYY-XXXXXX
            year = datetime.now().year
            random_part = str(uuid.uuid4())[:6].upper()
            self.serial_no = f"GS-{year}-{random_part}"
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.organization_name} - {self.get_organization_type_display()}"

    class Meta:
        verbose_name = _("government structure")
        verbose_name_plural = _("government structures")
        ordering = ["-created_at"]
