from django.contrib import admin
from government.models import Structure


@admin.register(Structure)
class StructureAdmin(admin.ModelAdmin):
    """
    Admin interface for Government Structure model
    """

    list_display = ["organization_name", "organization_type", "department_name", "serial_no", "construction_id", "status", "dzongkhag", "no_of_floors", "total_area", "created_at"]

    list_filter = ["organization_type", "status", "dzongkhag", "thromde", "construction_type", "building_type", "nature", "created_at"]

    search_fields = ["organization_name", "department_name", "serial_no", "construction_id", "purpose", "description"]

    readonly_fields = ["serial_no", "construction_id", "created_at", "updated_at"]

    fieldsets = (
        ("Basic Information", {"fields": ("organization_type", "organization_name", "department_name", "user", "serial_no", "construction_id", "status")}),
        ("Location", {"fields": ("region", "dzongkhag", "thromde", "gewog", "village")}),
        (
            "Building Information",
            {"fields": ("construction_type", "building_type", "use", "proposal_type", "no_of_floors", "no_of_units", "total_area", "built_up_area", "nature", "land_pooling")},
        ),
        ("Construction Details", {"fields": ("construction_start_date", "construction_completion_date", "estimated_cost")}),
        ("Additional Information", {"fields": ("purpose", "description", "remarks")}),
        ("Timestamps", {"fields": ("created_at", "updated_at")}),
    )

    ordering = ["-created_at"]
