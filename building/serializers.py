from rest_framework import serializers
from building.models import Permit
from drf_writable_nested.serializers import WritableNestedModelSerializer
from common.serializers import DesignTeamSearializer
from payment.serializers import PaymentSerializer
from rest_framework.validators import UniqueValidator
from common.models import ConstructionType, BuildingType, Use, ProposalType
from design.models import Permit as DesignPermit
from common.serializers import SlabSerializer


class PermitSerializer(WritableNestedModelSerializer):
    design_teams = DesignTeamSearializer(many=True, required=True)
    payments = PaymentSerializer(many=True, required=False, read_only=True, allow_empty=True)
    slabs = SlabSerializer(many=True, required=False, allow_empty=True, read_only=True)
    design_permit_id = serializers.PrimaryKeyRelatedField(
        queryset=DesignPermit.objects.all(), source="design_permit", required=True, validators=[UniqueValidator(queryset=Permit.objects.all())]
    )
    construction_type_id = serializers.PrimaryKeyRelatedField(queryset=ConstructionType.objects.all(), source="construction_type", required=True)
    building_type_id = serializers.PrimaryKeyRelatedField(queryset=BuildingType.objects.all(), source="building_type", required=True)
    use_id = serializers.PrimaryKeyRelatedField(queryset=Use.objects.all(), source="use", required=True)
    proposal_type_id = serializers.PrimaryKeyRelatedField(queryset=ProposalType.objects.all(), source="proposal_type", required=True)
    construction_type_file = serializers.FileField(source="construction_type.file", read_only=True)
    building_type_file = serializers.FileField(source="building_type.file", read_only=True)
    use_file = serializers.FileField(source="use.file", read_only=True)
    proposal_type_file = serializers.FileField(source="proposal_type.file", read_only=True)
    serial_no = serializers.CharField(required=False)
    state_display = serializers.CharField(source="get_state_display", read_only=True)
    application_id = serializers.IntegerField(read_only=True, source="design_permit.application_id")
    inquiry_id = serializers.IntegerField(read_only=True, source="design_permit.application.inquiry_id")

    class Meta:
        model = Permit
        ref_name = "BuildingPermit"
        fields = [
            "id",
            "design_permit_id",
            "application_id",
            "inquiry_id",
            "user_id",
            "serial_no",
            "construction_id",
            "state",
            "state_display",
            "region_id",
            "dzongkhag_id",
            "gewog_id",
            "thromde_id",
            "village_id",
            "star",
            "construction_type_id",
            "building_type_id",
            "use_id",
            "proposal_type_id",
            "construction_type_file",
            "building_type_file",
            "use_file",
            "proposal_type_file",
            "no_of_floors",
            "no_of_units",
            "created_at",
            "fee",
            "nature",
            "land_pooling",
            "turn_around_time",
            "purpose",
            "development_type",
            "design_teams",
            "slabs",
            "payments",
        ]


class PermitListSerializer(serializers.ModelSerializer):
    state_display = serializers.CharField(source="get_state_display", read_only=True)

    class Meta:
        model = Permit
        ref_name = "BuildingPermit"
        fields = [
            "id",
            "design_permit_id",
            "user_id",
            "serial_no",
            "construction_id",
            "state",
            "state_display",
            "created_at",
            "construction_type_id",
            "star",
            "building_type_id",
            "use_id",
            "proposal_type_id",
            "no_of_floors",
            "fee",
            "land_pooling",
            "no_of_units",
            "turn_around_time",
        ]
