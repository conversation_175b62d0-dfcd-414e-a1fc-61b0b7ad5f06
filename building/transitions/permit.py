from django_fsm import transition
from django_fsm_log.decorators import fsm_log_by
from rest_framework.exceptions import ValidationError
from django_fsm_log.decorators import fsm_log_description


class PermitTransition:
    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="initiated", target="requested")
    def initial(self, by=None, description=None):
        description.set("Building permit initiated.")
        self.by = by

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["requested", "resubmitted"], target="forwarded_to_roid")
    def forward_to_roid(self, data, by=None, description=None):
        description.set(data.get("remarks", "Forwarded to ROID"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["requested", "forwarded_to_roid", "resubmitted"], target="forwarded_to_dhs")
    def forward_to_dhs(self, data, by=None, description=None):
        description.set(data.get("remarks", "Forwarded to DHS"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["forwarded_to_dhs", "forwarded_to_roid", "requested", "resubmitted"], target="architect_assigned")
    def assign_architect(self, data, by=None, description=None):
        if not data.get("architect_id"):
            raise ValidationError({"error": "You have not selected any architect."})
        description.set("Architect is assigned")
        self.architect_id = data.get("architect_id")
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="rejected")
    def reject(self, data, by=None, description=None):
        if not data.get("reject_remarks"):
            raise ValidationError({"error": "You should provide rejection remarks."})
        description.set(data.get("reject_remarks"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["requested", "resubmitted", "forwarded_to_roid", "forwarded_to_dhs"], target="pending_change")
    def change(self, data, by=None, description=None):
        remarks = data.get("remarks")
        if not remarks:
            raise ValidationError({"error": "You should provide remarks."})
        description.set(remarks)
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="pending_change", target="resubmitted")
    def resubmit(self, data, by=None, description=None):
        description.set(data.get("remarks", "Application re-submitted"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="closed")
    def close(self, data, by=None, description=None):
        description.set(data.get("remarks", "Application closed"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="pending_payment", target="approved")
    def approve(self, data, by=None, description=None):
        description.set(data.get("remarks", "Application approved"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=("architect_assigned", "resubmitted"), target="pending_payment")
    def payment(self, data, by=None, description=None):
        description.set(data.get("remarks", "The application is waiting for the final payment."))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="pending_payment", target="approved")
    def approve(self, data, by=None, description=None):
        description.set(data.get("remarks", "Your application is approved."))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=("architect_assigned", "resubmitted", "forwarded_to_tcb"), target="forwarded_to_bpc")
    def forward_to_bpc(self, data, by=None, description=None):
        description.set(data.get("remarks", "Forwarded to BPC"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=("architect_assigned", "resubmitted"), target="forwarded_to_tcb")
    def forward_to_tcb(self, data, by=None, description=None):
        description.set(data.get("remarks", "Forwarded to TCB"))
        self.by = by
        self.data = data
