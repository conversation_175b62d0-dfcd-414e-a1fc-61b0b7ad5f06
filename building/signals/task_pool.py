from django.dispatch import receiver
from django.db.models.signals import post_save
from django_fsm.signals import post_transition
from building.models import Permit
from building.mailers import notify_ar_approved, notify_task_pool_approved, notify_bpc, notify_tcb
from rest_framework.exceptions import ValidationError
from notifications.signals import notify
from common.helpers import determine_assignee_user
from common.models import TaskPool
from common.constants import DHS_ROLES, ROID_ROLES, DZO_ROLES, THRM_ROLES, OTHER_ENG_ROLES, CHIEF_ROLES
from cas_api.middleware.current_request import get_current_request
from building.enums import PermitStatus
from common.helpers import create_slabs_set_fee


@receiver(post_transition, sender=TaskPool)
def after_order_transition(sender, instance, name, source, target, **kwargs):
    if not isinstance(instance.poolable, Permit):
        return
    role_name = instance.role.name
    permit = instance.poolable
    by = getattr(get_current_request(), "user", None)
    instance.save()
    if name == "approve":
        if instance.state == "completed":
            if role_name in ("tar", "dar", "roidar", "dhsar"):
                user_roles = get_user_roles(instance)
                user_filters = get_user_filters(instance)
                create_slabs_set_fee(permit, getattr(instance, "data", {}))
                create_task_pools(permit, user_roles, user_filters, by)
            elif permit.state == PermitStatus.ARCHITECT_ASSIGNED and role_name in OTHER_ENG_ROLES:
                task_pools = permit.task_pools.filter(role__name__in=OTHER_ENG_ROLES)
                if task_pools.exists() and all(task.state == "completed" for task in task_pools):
                    task = permit.task_pools.filter(role__name__in=CHIEF_ROLES).last()
                    task.assign(getattr(instance, "data", {}), by=by)
                    task.save()
            elif role_name == "dhsce":
                task_pools = permit.task_pools.filter(role__name__in=DHS_ROLES)
                if task_pools.exists() and all(task.state == "completed" for task in task_pools):
                    task = permit.task_pools.filter(role__name="dce").first()
                    if not task:
                        user, role = determine_assignee_user("dce", dzo=permit.dzongkhag)
                        permit.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
                    else:
                        task.assign(getattr(instance, "data", {}), by=by)
                        task.save()
            elif role_name == "roidce":
                task_pools = permit.task_pools.filter(role__name__in=ROID_ROLES)
                if task_pools.exists() and all(task.state == "completed" for task in task_pools):
                    user, role = determine_assignee_user("dce", dzo=permit.dzongkhag)
                    permit.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
            elif role_name == "dce":
                task_pools = permit.task_pools.filter(role__name="dce")
                if not permit.state == PermitStatus.REQUESTED and task_pools.exists() and all(task.state == "completed" for task in task_pools):
                    permit.payment(getattr(instance, "data", {}), by=by)
                    permit.save()
            elif role_name == "tce":
                task_pools = permit.task_pools.all()
                if not permit.state == PermitStatus.REQUESTED and task_pools.exists() and all(task.state == "completed" for task in task_pools):
                    permit.payment(getattr(instance, "data", {}), by=by)
                    permit.save()
            notify_task_pool_approved.delay(instance.id)
    elif name == "forward_to_bpc":
        enlist = ("tee", "dee", "dhsee", "roidee")
        if role_name in enlist and instance.state == "forwarded_to_bpc":
            if role_name in ("tee", "dee", "dhsee", "roidee"):
                bpc_user, role = determine_assignee_user("bpc", dzo=permit.dzongkhag)
            else:
                bpc_user, role = determine_assignee_user("bpc")
            permit.task_pools.create(user=bpc_user, role=role, by=by, by_role=getattr(by, "current_role", None))
            instance.approve(getattr(instance, "data", {}), by=by)
            instance.save()
            notify_bpc.delay(permit.id, bpc_user.id)
    elif name == "forward_to_tcb":
        arlist = ("dhsar", "roidar", "dar", "tar")
        if role_name in arlist and instance.state == "forwarded_to_tcb":
            tcb_user, role = determine_assignee_user("tcb")
            permit.task_pools.create(user=tcb_user, role=role, by=by, by_role=getattr(by, "current_role", None))
            instance.approve(getattr(instance, "data", {}), by=by)
            instance.save()
            notify_tcb.delay(permit.id, tcb_user.id)
    elif name == "reject":
        permit.reject({"reject_remarks": instance.reject_remarks}, by=by)
        permit.save()
    elif name == "change":
        permit.change(instance.data, by=by)
        permit.save()
    create_notification(instance, target)


def create_task_pools(permit, user_roles, user_filters, by):
    try:
        for role_name, user_filter in zip(user_roles, user_filters):
            user, role = determine_assignee_user(role_name, **user_filter)
            permit.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
            notify_ar_approved.delay(permit.id, user.id)
    except ValidationError as e:
        raise e


def get_user_roles(instance):
    current_role = instance.role.name
    design_teams = instance.poolable.design_teams.values_list("team_type", flat=True)
    if current_role == "dhsar":
        return [f"dhs{x}" for x in design_teams]
    elif current_role == "roidar":
        return [f"roid{x}" for x in design_teams]
    elif current_role == "dar":
        return [f"d{x}" for x in design_teams]
    elif current_role == "tar":
        return [f"t{x}" for x in design_teams]
    else:
        return []


def get_user_filters(instance):
    current_role = instance.role.name
    dzongkhag = instance.user.dzongkhag
    thromde = instance.user.thromde
    region = instance.user.region
    if current_role == "tar":
        se_filters = {"dzo": dzongkhag, "thrm": thromde}
        ee_filters = {"dzo": dzongkhag, "thrm": thromde}
        wsf_filters = {"dzo": dzongkhag, "thrm": thromde}
    elif current_role == "dar":
        se_filters = {"dzo": dzongkhag}
        ee_filters = {"dzo": dzongkhag}
        wsf_filters = {"dzo": dzongkhag}
    elif current_role == "roidar":
        se_filters = {"rgn": region}
        ee_filters = {"rgn": region}
        wsf_filters = {"rgn": region}
    else:
        se_filters = {}
        ee_filters = {}
        wsf_filters = {}
    return [se_filters, ee_filters, wsf_filters]


def create_notification(instance, action):
    url = f"/services/building-permit/{instance.poolable.id}"
    notify.send(instance.by, recipient=instance.poolable.user, verb=action, action_object=instance.poolable, target=instance, url=url)
    notify.send(instance.by, recipient=instance.user, verb=action, action_object=instance.poolable, target=instance, url=url)
