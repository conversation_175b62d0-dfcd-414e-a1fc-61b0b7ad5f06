# Generated by Django 4.1.7 on 2023-06-13 19:34

from django.db import migrations, models
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ("building", "0007_permit_thromde_alter_permit_serial_no"),
    ]

    operations = [
        migrations.AddField(
            model_name="permit",
            name="fee",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="buildingtaskpool",
            name="state",
            field=django_fsm.FSMField(
                choices=[
                    ("assigned", "Assigned"),
                    ("in_progress", "In Progress"),
                    ("forwarded_to_bpc", "Forwareded to Bhutan Power Coperation"),
                    ("forwarded_to_tcb", "Forwareded to Tourism Counsil of Bhutan"),
                    ("reviewing", "Reviewing"),
                    ("completed", "Completed"),
                    ("rejected", "Rejected"),
                ],
                default="assigned",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="permit",
            name="serial_no",
            field=models.CharField(default=1686684885831, max_length=50),
        ),
        migrations.AlterField(
            model_name="permit",
            name="state",
            field=django_fsm.FSMField(
                choices=[
                    ("requested", "Requested"),
                    ("forwarded_to_dzo", "Forwarded to Dzongkhag"),
                    ("forwarded_to_moit", "Forwarded to MoIT"),
                    ("fee_set", "Fee Set"),
                    ("fee_paid", "Fee Paid"),
                    ("fee_payment_failed", "Fee Payment Failed"),
                    ("architect_assigned", "Architect Assigned"),
                    ("approved", "Approved"),
                    ("rejected", "Rejected"),
                    ("closed", "Closed"),
                ],
                default="requested",
                max_length=20,
            ),
        ),
    ]
