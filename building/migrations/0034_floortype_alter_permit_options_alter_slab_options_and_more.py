# Generated by Django 4.1.7 on 2025-04-04 07:37

from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0007_use_proposaltype_constructiontype_buildingtype'),
        ('building', '0033_remove_permit_application_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='FloorType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'floor_type',
                'verbose_name_plural': 'floor_types',
            },
        ),
        migrations.AlterModelOptions(
            name='permit',
            options={'verbose_name': 'permit', 'verbose_name_plural': 'permits'},
        ),
        migrations.AlterModelOptions(
            name='slab',
            options={'verbose_name': 'slab', 'verbose_name_plural': 'slabs'},
        ),
        migrations.RemoveField(
            model_name='permit',
            name='building_topology',
        ),
        migrations.RemoveField(
            model_name='permit',
            name='building_topology_others',
        ),
        migrations.RemoveField(
            model_name='permit',
            name='building_type_others',
        ),
        migrations.AddField(
            model_name='permit',
            name='proposal_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='building_permits', to='common.proposaltype'),
        ),
        migrations.AddField(
            model_name='permit',
            name='use',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='building_permits', to='common.use'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='building_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='building_permits', to='common.buildingtype'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='construction_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='building_permits', to='common.constructiontype'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='nature',
            field=models.CharField(choices=[('urban', 'Urban'), ('rural', 'Rural')], default='rural', max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='permit',
            name='serial_no',
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='permit',
            name='state',
            field=django_fsm.FSMField(choices=[('draft', 'Draft'), ('initiated', 'Initiated'), ('requested', 'Requested'), ('forwarded_to_dzo', 'Forwarded to Dzongkhag'), ('forwarded_to_moit', 'Forwarded to MoIT'), ('architect_assigned', 'Architect Assigned'), ('resubmitted', 'Re-submitted'), ('pending_payment', 'Pending Payment'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('closed', 'Closed')], default='initiated', max_length=20),
        ),
        migrations.AlterField(
            model_name='slab',
            name='floor_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='slabs', to='building.floortype'),
        ),
    ]
