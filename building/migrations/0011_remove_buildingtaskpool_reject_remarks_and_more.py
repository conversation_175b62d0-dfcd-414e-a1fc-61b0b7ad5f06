# Generated by Django 4.1.7 on 2023-06-15 20:58

from django.db import migrations, models
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ("building", "0010_alter_permit_building_topology_others_and_more"),
    ]

    operations = [
        migrations.RemoveField(model_name="buildingtaskpool", name="reject_remarks",),
        migrations.RemoveField(model_name="buildingtaskpool", name="remarks",),
        migrations.AlterField(
            model_name="permit",
            name="serial_no",
            field=models.CharField(default=1686862729794, max_length=50),
        ),
        migrations.AlterField(
            model_name="permit",
            name="state",
            field=django_fsm.FSMField(
                choices=[
                    ("requested", "Requested"),
                    ("forwarded_to_dzo", "Forwarded to Dzongkhag"),
                    ("forwarded_to_moit", "Forwarded to MoIT"),
                    ("fee_set", "Fee Set"),
                    ("fee_paid", "Fee Paid"),
                    ("fee_payment_failed", "Fee Payment Failed"),
                    ("architect_assigned", "Architect Assigned"),
                    ("resubmitted", "Re-submitted"),
                    ("approved", "Approved"),
                    ("rejected", "Rejected"),
                    ("closed", "Closed"),
                ],
                default="requested",
                max_length=20,
            ),
        ),
    ]
