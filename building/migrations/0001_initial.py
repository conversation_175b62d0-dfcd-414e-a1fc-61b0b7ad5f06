# Generated by Django 4.1.7 on 2023-05-24 19:34

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("address", "0003_alter_gewog_dzongkhag_alter_thromde_dzongkhag_and_more"),
        ("file", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Permit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("serial_no", models.CharField(default=1684956857243, max_length=50)),
                (
                    "state",
                    django_fsm.FSMField(
                        choices=[
                            ("requested", "Requested"),
                            ("reviewed", "Reviewed"),
                            ("scheduled", "Scheduled"),
                            ("visited", "Visited"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("closed", "Closed"),
                        ],
                        default="requested",
                        max_length=20,
                    ),
                ),
                (
                    "construction_type",
                    models.CharField(
                        choices=[
                            ("permanent", "Permanent"),
                            ("semi_permanent", "Semi-permanent"),
                            ("temporary", "Temporary"),
                        ],
                        max_length=50,
                    ),
                ),
                ("star", models.IntegerField(default=0, null=True)),
                (
                    "building_type",
                    models.CharField(
                        choices=[
                            ("traditional", "Permanent"),
                            ("concrete", "Reinforced Concrete Cement"),
                            ("others", "Others"),
                        ],
                        max_length=50,
                    ),
                ),
                ("building_type_others", models.CharField(max_length=100, null=True)),
                ("no_of_floors", models.IntegerField()),
                ("no_of_units", models.IntegerField()),
                (
                    "building_topology",
                    models.CharField(
                        choices=[
                            ("residential", "Residential"),
                            ("commercial", "Commercial"),
                            ("institutional", "Institutional"),
                            ("industrial", "Industrial"),
                            ("recreational", "Recreational"),
                            ("hospitality", "Hospitality"),
                            ("religious", "Religious"),
                            ("others", "Others"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "building_topology_others",
                    models.CharField(max_length=100, null=True),
                ),
                (
                    "architectural_drawing",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="architectural_drawing",
                        to="file.attachment",
                    ),
                ),
                (
                    "dzongkhag",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="address.dzongkhag",
                    ),
                ),
                (
                    "electrical_drawing",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="electrical_drawing",
                        to="file.attachment",
                    ),
                ),
                (
                    "gewog",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="address.gewog",
                    ),
                ),
                (
                    "plumbing_sanitation_drawing",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="plumbing_sanitation_drawing",
                        to="file.attachment",
                    ),
                ),
                (
                    "structural_drawing",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="structural_drawing",
                        to="file.attachment",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="permits",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "village",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="address.village",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DesignTeam",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("architect_cid", models.CharField(max_length=11)),
                ("architect_name", models.CharField(max_length=100)),
                ("electrical_engineer_cid", models.CharField(max_length=11)),
                ("electrical_engineer_name", models.CharField(max_length=100)),
                ("structural_engineer_cid", models.CharField(max_length=11)),
                ("structural_engineer_name", models.CharField(max_length=100)),
                (
                    "architect_certificate",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="architect_certificate",
                        to="file.attachment",
                    ),
                ),
                (
                    "design_team_certificate",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="design_team_certificate",
                        to="file.attachment",
                    ),
                ),
                (
                    "electrical_engineer_certificate",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="electrical_engineer_certificate",
                        to="file.attachment",
                    ),
                ),
                (
                    "permit",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="design_team",
                        to="building.permit",
                    ),
                ),
                (
                    "structural_engineer_certificate",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="structural_engineer_certificate",
                        to="file.attachment",
                    ),
                ),
            ],
        ),
    ]
