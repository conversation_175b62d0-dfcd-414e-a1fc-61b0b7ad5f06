# Generated by Django 4.1.7 on 2025-05-29 17:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('building', '0040_permit_construction_id'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='permit',
            index=models.Index(fields=['created_at'], name='building_pe_created_93e403_idx'),
        ),
        migrations.AddIndex(
            model_name='permit',
            index=models.Index(fields=['state'], name='building_pe_state_632da4_idx'),
        ),
        migrations.AddIndex(
            model_name='permit',
            index=models.Index(fields=['dzongkhag'], name='building_pe_dzongkh_102695_idx'),
        ),
        migrations.AddIndex(
            model_name='permit',
            index=models.Index(fields=['gewog'], name='building_pe_gewog_i_d6a209_idx'),
        ),
        migrations.AddIndex(
            model_name='permit',
            index=models.Index(fields=['construction_id'], name='building_pe_constru_28d260_idx'),
        ),
        migrations.AddIndex(
            model_name='permit',
            index=models.Index(fields=['building_type'], name='building_pe_buildin_cbe70e_idx'),
        ),
        migrations.AddIndex(
            model_name='permit',
            index=models.Index(fields=['use'], name='building_pe_use_id_26cdaf_idx'),
        ),
    ]
