# Generated by Django 4.1.7 on 2023-07-27 11:26

from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('file', '0002_attachment_name_attachment_size_attachment_type'),
        ('building', '0016_alter_slab_unique_together_permit_land_pooling_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='permit',
            name='other_drawing',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='other_drawing', to='file.attachment'),
        ),
        migrations.AlterField(
            model_name='permit',
            name='building_topology',
            field=models.CharField(choices=[('residential', 'Residential'), ('commercial', 'Commercial'), ('institutional', 'Institutional'), ('industrial', 'Industrial'), ('recreational', 'Recreational'), ('hospitality', 'Hospitality'), ('religious', 'Religious'), ('mixed_used', 'Mixed Used'), ('others', 'Others')], max_length=50),
        ),
        migrations.AlterField(
            model_name='permit',
            name='building_type',
            field=models.CharField(choices=[('traditional', 'Traditional Bhutanese House'), ('concrete', 'Reinforced Concrete Cement'), ('load_bearing', 'Load Bearing'), ('stell_structure', 'Stell Structure'), ('prefab_structure', 'Pre-Fab Structure'), ('precast_structure', 'Pre-Cast Concrete Structure'), ('timber_structure', 'Timber Structure')], max_length=50),
        ),
        migrations.AlterField(
            model_name='permit',
            name='serial_no',
            field=models.CharField(default=1690457197009, max_length=50),
        ),
        migrations.AlterField(
            model_name='permit',
            name='state',
            field=django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('requested', 'Requested'), ('forwarded_to_dzo', 'Forwarded to Dzongkhag'), ('forwarded_to_moit', 'Forwarded to MoIT'), ('fee_set', 'Fee Set'), ('fee_paid', 'Fee Paid'), ('fee_payment_failed', 'Fee Payment Failed'), ('architect_assigned', 'Architect Assigned'), ('resubmitted', 'Re-submitted'), ('awaiting_payment', 'Awaiting Payment'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('closed', 'Closed')], default='initiated', max_length=20),
        ),
    ]
