# Generated by Django 4.1.7 on 2023-05-29 06:36

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("building", "0002_permit_application_alter_permit_building_type_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="designteam",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="designteam",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name="permit",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="permit",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="permit",
            name="serial_no",
            field=models.Char<PERSON><PERSON>(default=1685342157419, max_length=50),
        ),
    ]
