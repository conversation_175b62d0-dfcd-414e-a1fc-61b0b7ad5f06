# Generated by Django 4.1.7 on 2023-09-18 13:04

from django.db import migrations, models
import django.utils.timezone
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('building', '0020_buildingtaskpool_remarks_alter_permit_serial_no'),
    ]

    operations = [
        migrations.AddField(
            model_name='buildingtaskpool',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='buildingtaskpool',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='permit',
            name='serial_no',
            field=models.CharField(default=1695042259288, max_length=50),
        ),
        migrations.AlterField(
            model_name='permit',
            name='state',
            field=django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('requested', 'Requested'), ('forwarded_to_dzo', 'Forwarded to Dzongkhag'), ('forwarded_to_moit', 'Forwarded to MoIT'), ('architect_assigned', 'Architect Assigned'), ('resubmitted', 'Re-submitted'), ('awaiting_payment', 'Awaiting Payment'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('closed', 'Closed')], default='initiated', max_length=20),
        ),
    ]
