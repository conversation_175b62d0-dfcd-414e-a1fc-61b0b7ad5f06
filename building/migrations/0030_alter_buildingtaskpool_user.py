# Generated by Django 5.0.3 on 2024-05-15 19:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("building", "0029_buildingtaskpool_by_permit_by"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name="buildingtaskpool",
            name="user",
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name="task_pools", to=settings.AUTH_USER_MODEL),
        ),
    ]
