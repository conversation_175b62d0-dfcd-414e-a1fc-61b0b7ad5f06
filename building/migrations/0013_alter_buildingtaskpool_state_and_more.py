# Generated by Django 4.1.7 on 2023-06-23 08:08

from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ("building", "0012_remove_permit_rejection_remarks_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="buildingtaskpool",
            name="state",
            field=django_fsm.FSMField(
                choices=[
                    ("initiated", "Initiated"),
                    ("assigned", "Assigned"),
                    ("in_progress", "In Progress"),
                    ("forwarded_to_bpc", "Forwareded to Bhutan Power Coperation"),
                    ("forwarded_to_tcb", "Forwareded to Tourism Counsil of Bhutan"),
                    ("reviewing", "Reviewing"),
                    ("completed", "Completed"),
                    ("rejected", "Rejected"),
                ],
                default="initiated",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="permit",
            name="application",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="permit",
                to="planning.application",
            ),
        ),
        migrations.AlterField(
            model_name="permit",
            name="serial_no",
            field=models.CharField(default=1687507713743, max_length=50),
        ),
        migrations.AlterField(
            model_name="permit",
            name="state",
            field=django_fsm.FSMField(
                choices=[
                    ("initiated", "Initiated"),
                    ("requested", "Requested"),
                    ("forwarded_to_dzo", "Forwarded to Dzongkhag"),
                    ("forwarded_to_moit", "Forwarded to MoIT"),
                    ("fee_set", "Fee Set"),
                    ("fee_paid", "Fee Paid"),
                    ("fee_payment_failed", "Fee Payment Failed"),
                    ("architect_assigned", "Architect Assigned"),
                    ("resubmitted", "Re-submitted"),
                    ("approved", "Approved"),
                    ("rejected", "Rejected"),
                    ("closed", "Closed"),
                ],
                default="initiated",
                max_length=20,
            ),
        ),
    ]
