# Generated by Django 4.1.7 on 2023-07-17 13:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('building', '0013_alter_buildingtaskpool_state_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='permit',
            name='serial_no',
            field=models.CharField(default=1689601271920, max_length=50),
        ),
        migrations.CreateModel(
            name='Slab',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('floor_type', models.CharField(choices=[('basement', 'Basement'), ('stilt_floor', 'Stilt floor'), ('ground_floor', 'Ground floor'), ('first_floor', 'First floor'), ('second_floor', 'Second floor'), ('third_floor', 'Third floor'), ('fourth_floor', 'Fourth floor'), ('fifth_floor', 'Fifth floor'), ('jamtho_floor', 'Jamtho Floor')], max_length=50)),
                ('area', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('usage', models.CharField(choices=[('commercial', 'Commercial'), ('residential', 'Residential')], max_length=50)),
                ('fee', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('permit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='slabs', to='building.permit')),
            ],
            options={
                'unique_together': {('usage', 'permit'), ('floor_type', 'permit')},
            },
        ),
    ]
