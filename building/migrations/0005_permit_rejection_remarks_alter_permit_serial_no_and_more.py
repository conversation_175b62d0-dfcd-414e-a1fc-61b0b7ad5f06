# Generated by Django 4.1.7 on 2023-06-05 19:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("building", "0004_alter_permit_serial_no_alter_permit_state"),
    ]

    operations = [
        migrations.AddField(
            model_name="permit",
            name="rejection_remarks",
            field=models.TextField(null=True),
        ),
        migrations.AlterField(
            model_name="permit",
            name="serial_no",
            field=models.CharField(default=1685993939069, max_length=50),
        ),
        migrations.CreateModel(
            name="BuildingTaskPool",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "state",
                    models.CharField(
                        choices=[
                            ("assigned", "Assigned"),
                            ("in_progress", "In Progress"),
                            ("forwarded_to_bpc", "Forwareded to BPC"),
                            ("forwarded_to_tourism", "Forwareded to Tourism"),
                            ("reviewing", "Reviewing"),
                            ("completed", "Completed"),
                        ],
                        default="assigned",
                        max_length=50,
                    ),
                ),
                ("remarks", models.CharField(blank=True, max_length=500, null=True)),
                (
                    "permit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="task_pools",
                        to="building.permit",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
