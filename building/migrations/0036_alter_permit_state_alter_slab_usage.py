# Generated by Django 4.1.7 on 2025-04-04 10:45

from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0007_use_proposaltype_constructiontype_buildingtype'),
        ('building', '0035_rename_permit_permit_design_permit'),
    ]

    operations = [
        migrations.AlterField(
            model_name='permit',
            name='state',
            field=django_fsm.FSMField(choices=[('draft', 'Draft'), ('initiated', 'Initiated'), ('requested', 'Requested'), ('forwarded_to_roid', 'Forwarded to ROID'), ('forwarded_to_dhs', 'Forwarded to DHS'), ('forwarded_to_bpc', 'Forwarded to BPC'), ('forwarded_to_tcb', 'Forwarded to TCB'), ('architect_assigned', 'Architect Assigned'), ('resubmitted', 'Re-submitted'), ('pending_change', 'Pending Change'), ('pending_payment', 'Pending Payment'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('closed', 'Closed')], default='initiated', max_length=20),
        ),
        migrations.AlterField(
            model_name='slab',
            name='usage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='slabs', to='common.use'),
        ),
    ]
