# Generated by Django 4.1.7 on 2024-01-10 10:06

from django.db import migrations, models
import django.db.models.deletion


def update_taskpool_roles(apps, schema_editor):
    TaskPool = apps.get_model("building", "BuildingTaskPool")

    for task_pool in TaskPool.objects.filter(role_id__isnull=True):
        if task_pool.user.current_role:
            task_pool.role = task_pool.user.current_role
            task_pool.save()


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0025_alter_user_username"),
        ("building", "0026_permit_nature"),
    ]

    operations = [
        migrations.AddField(
            model_name="buildingtaskpool",
            name="role",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="user.role",
            ),
        ),
        migrations.RunPython(update_taskpool_roles),
    ]
