# Generated by Django 4.1.7 on 2025-04-03 10:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('address', '0010_region_dzongkhag_created_at_dzongkhag_updated_at_and_more'),
        ('design', '0004_alter_permit_state'),
        ('building', '0032_alter_permit_state'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='permit',
            name='application',
        ),
        migrations.RemoveField(
            model_name='permit',
            name='architectural_drawing',
        ),
        migrations.RemoveField(
            model_name='permit',
            name='electrical_drawing',
        ),
        migrations.RemoveField(
            model_name='permit',
            name='other_drawing',
        ),
        migrations.RemoveField(
            model_name='permit',
            name='payment',
        ),
        migrations.RemoveField(
            model_name='permit',
            name='plumbing_sanitation_drawing',
        ),
        migrations.RemoveField(
            model_name='permit',
            name='structural_drawing',
        ),
        migrations.AddField(
            model_name='permit',
            name='permit',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='building_permits', to='design.permit'),
        ),
        migrations.AddField(
            model_name='permit',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='address.region'),
        ),
    ]
