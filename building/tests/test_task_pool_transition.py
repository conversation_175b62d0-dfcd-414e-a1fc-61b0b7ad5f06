import json
import time
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase
from django.core.management import call_command
from common.factories import DesignTeamFactory, TaskPoolFactory
from planning.models import *
from user.factories import SettingFactory, UserFactory, ProfileFactory
from building.factories import PermitFactory
from address.factories import DzongkhagFactory, GewogFactory, ThromdeFactory
from django.urls import path, include
from planning.factories import ApplicationFactory, ApplicantFactory
from user.models import Role


class TaskPoolTransitionTest(APITestCase, URLPatternsTestCase):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("building.urls")),
        path("api/v1/", include("common.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.dzo = DzongkhagFactory(name="Mongar")
        self.gewog = GewogFactory(name="Mongar", dzongkhag=self.dzo)

        self.user = UserFactory.create(
            username="user",
            roles=Role.objects.filter(name__in=["thrmwsf"]).values_list("id", flat=True),
        )
        ProfileFactory(user=self.user, dzongkhag=self.dzo)

        self.planner = UserFactory.create(
            username="planner",
            roles=Role.objects.filter(name__in=["dpd", "upd"]).values_list("id", flat=True),
        )
        ProfileFactory(user=self.planner, dzongkhag=self.dzo)

        self.gewog_adm = UserFactory.create(
            username="gewog_adm",
            roles=Role.objects.filter(name__in=["gadm"]).values_list("id", flat=True),
        )
        ProfileFactory(user=self.gewog_adm, dzongkhag=self.dzo, gewog=self.gewog)

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": self.gewog_adm.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        application = ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog)
        ApplicantFactory(application=application)
        self.permit = PermitFactory(
            user=self.user,
            application=application,
            dzongkhag=self.dzo,
            gewog=self.gewog,
            fee=2000,
            serial_no=int(time.time() * 1000),
        )
        DesignTeamFactory(teamable=self.permit)
        self.building_task_pool = TaskPoolFactory(poolable=self.permit, user=self.gewog_adm, role=self.gewog_adm.roles.first())
        self.building_task_pool.state = "in_progress"
        self.building_task_pool.save()

    def test_invalid_transition(self):
        data = {"action": "invalid"}
        url = reverse("task_pool_transitions", kwargs={"pk": self.building_task_pool.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertTrue("error" in response_data)
        self.assertEqual(response_data["error"], "Invalid transition")

    def test_approve(self):
        data = {"action": "approve"}
        url = reverse("task_pool_transitions", kwargs={"pk": self.building_task_pool.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "completed")

    def test_reject(self):
        data = {
            "action": "reject",
            "reject_remarks": "There is no proper drainage system.",
        }
        url = reverse("task_pool_transitions", kwargs={"pk": self.building_task_pool.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "rejected")

    def test_forward_to_bpc_by_dzo_electrical_engineer(self):
        dzo_ee = UserFactory.create(
            username="dzo_ee",
            roles=Role.objects.filter(name__in=["dee"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_ee, dzongkhag=self.dzo)

        dzo_ar = UserFactory.create(
            username="dzo_ar",
            roles=Role.objects.filter(name__in=["dar"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_ar, dzongkhag=self.dzo)

        bpc = UserFactory.create(
            username="bpc",
            roles=Role.objects.filter(name__in=["bpc"]).values_list("id", flat=True),
        )
        ProfileFactory(user=bpc, dzongkhag=self.dzo)

        btp = TaskPool.objects.create(poolable=self.permit, user=dzo_ar, role=dzo_ar.current_role)
        btp.state = "completed"
        btp.save()
        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=dzo_ee, role=dzo_ee.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()

        data = {"action": "forward_to_bpc"}
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dzo_ee.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "completed")
        self.assertEqual(TaskPool.objects.get(user=dzo_ee).state, "completed")

    def test_forward_to_bpc_by_thromde_electrical_engineer(self):
        thromde = ThromdeFactory(dzongkhag=self.dzo)
        self.permit.thromde = thromde
        self.permit.save()

        thrm_ee = UserFactory.create(
            username="thrm_ee",
            roles=Role.objects.filter(name__in=["thrmee"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_ee, dzongkhag=self.dzo, thromde=thromde)

        thrm_ar = UserFactory.create(
            username="thrm_ar",
            roles=Role.objects.filter(name__in=["thrmar"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_ar, dzongkhag=self.dzo, thromde=thromde)

        bpc = UserFactory.create(
            username="bpc",
            roles=Role.objects.filter(name__in=["bpc"]).values_list("id", flat=True),
        )
        ProfileFactory(user=bpc, dzongkhag=self.dzo)

        btp = TaskPool.objects.create(poolable=self.permit, user=thrm_ar, role=thrm_ar.current_role)
        btp.state = "completed"
        btp.save()
        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=thrm_ee, role=thrm_ee.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()

        data = {"action": "forward_to_bpc"}
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": thrm_ee.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "completed")
        self.assertEqual(TaskPool.objects.get(user=thrm_ar).state, "completed")

    def test_forward_to_tcb_by_thrmar(self):
        thromde = ThromdeFactory(dzongkhag=self.dzo)
        self.permit.thromde = thromde
        self.permit.save()

        thrm_se = UserFactory.create(
            username="thrm_se",
            roles=Role.objects.filter(name__in=["thrmse"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_se, dzongkhag=self.dzo, thromde=thromde)

        thrm_ee = UserFactory.create(
            username="thrm_ee",
            roles=Role.objects.filter(name__in=["thrmee"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_ee, dzongkhag=self.dzo, thromde=thromde)

        thrm_wsf = UserFactory.create(
            username="thrm_wsf",
            roles=Role.objects.filter(name__in=["thrmwsf"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_wsf, dzongkhag=self.dzo, thromde=thromde)

        thrm_ar = UserFactory.create(
            username="thrm_ar",
            roles=Role.objects.filter(name__in=["thrmar"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_ar, dzongkhag=self.dzo, thromde=thromde)

        thrm_adm = UserFactory.create(
            username="thrm_adm",
            roles=Role.objects.filter(name__in=["thrmadm"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_adm, dzongkhag=self.dzo, thromde=thromde)
        SettingFactory(user=thrm_adm, key="rate", value=16.14)

        tcb = UserFactory.create(
            username="tcb",
            roles=Role.objects.filter(name__in=["tcb"]).values_list("id", flat=True),
        )
        ProfileFactory(user=tcb, dzongkhag=self.dzo)

        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=thrm_ar, role=thrm_ar.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()

        data = {
            "action": "forward_to_tcb",
            "land_pooling": True,
            "no_of_floors": 4,
            "slabs": [
                {"floor_type": "basement", "area": 230, "usage": "commercial"},
                {"floor_type": "first_floor", "area": 250, "usage": "commercial"},
                {"floor_type": "second_floor", "area": 210, "usage": "residential"},
                {"floor_type": "jamtho_floor", "area": 200, "usage": "residential"},
            ],
        }
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": thrm_ar.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "completed")
        self.assertEqual(
            TaskPool.objects.filter(user__in=(thrm_ee, thrm_se, thrm_wsf)).count(),
            3,
        )

    def test_forward_to_tcb_by_dar(self):
        self.permit.state = "forwarded_to_dzo"
        self.permit.save()

        dzo_se = UserFactory.create(
            username="dzo_se",
            roles=Role.objects.filter(name__in=["dse"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_se, dzongkhag=self.dzo)

        dzo_ee = UserFactory.create(
            username="dzo_ee",
            roles=Role.objects.filter(name__in=["dee"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_ee, dzongkhag=self.dzo)

        dzo_wsf = UserFactory.create(
            username="dzo_wsf",
            roles=Role.objects.filter(name__in=["dwsf"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_wsf, dzongkhag=self.dzo)

        dzo_ar = UserFactory.create(
            username="dzo_ar",
            roles=Role.objects.filter(name__in=["dar"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_ar, dzongkhag=self.dzo)

        dzo_adm = UserFactory.create(
            username="dzo_adm",
            roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_adm, dzongkhag=self.dzo)
        SettingFactory(user=dzo_adm, key="rate", value=16.14)

        tcb = UserFactory.create(
            username="tcb",
            roles=Role.objects.filter(name__in=["tcb"]).values_list("id", flat=True),
        )
        ProfileFactory(user=tcb, dzongkhag=self.dzo)

        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=dzo_ar, role=dzo_ar.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()

        data = {
            "action": "forward_to_tcb",
            "land_pooling": True,
            "no_of_floors": 4,
            "slabs": [
                {"floor_type": "basement", "area": 230, "usage": "commercial"},
                {"floor_type": "first_floor", "area": 250, "usage": "commercial"},
                {"floor_type": "second_floor", "area": 210, "usage": "residential"},
                {"floor_type": "jamtho_floor", "area": 200, "usage": "residential"},
            ],
        }
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dzo_ar.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "completed")
        self.assertEqual(
            TaskPool.objects.filter(user__in=(dzo_ee, dzo_se, dzo_wsf)).count(),
            3,
        )

    def test_not_permited_to_perform_others_task(self):
        thromde = ThromdeFactory(dzongkhag=self.dzo)

        thrm_ar = UserFactory.create(
            username="thrm_ar",
            roles=Role.objects.filter(name__in=["thrmar"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_ar, dzongkhag=self.dzo, thromde=thromde)

        TaskPool.objects.create(poolable=self.permit, user=thrm_ar, role=thrm_ar.current_role)
        building_task_pool1 = TaskPool.objects.create(poolable=self.permit, user=self.gewog_adm, role=self.gewog_adm.current_role)
        building_task_pool1.state = "in_progress"
        building_task_pool1.save()

        data = {"action": "approve"}
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool1.id})
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": thrm_ar.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertTrue("error" in response_data)
        self.assertEqual(
            response_data["error"],
            f"{thrm_ar.current_role.description} is trying to perform action on behalf of other {building_task_pool1.user.current_role.description}",
        )

    def test_mark_in_progress(self):
        thromde = ThromdeFactory(dzongkhag=self.dzo)

        thrm_ar = UserFactory.create(
            username="thrm_ar",
            roles=Role.objects.filter(name__in=["thrmar"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_ar, dzongkhag=self.dzo, thromde=thromde)

        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=thrm_ar, role=thrm_ar.current_role)
        data = {"action": "start"}
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": thrm_ar.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "in_progress")
