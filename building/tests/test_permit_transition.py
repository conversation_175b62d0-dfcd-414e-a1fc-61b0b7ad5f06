import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase
from django.core.management import call_command
from common.factories import DesignTeamFactory
from planning.models import *
from user.factories import UserFactory, ProfileFactory
from building.factories import PermitFactory
from address.factories import DzongkhagFactory, GewogFactory
from django.urls import path, include
from planning.factories import ApplicationFactory, ApplicantFactory
from user.models import Role


class PermitTransitionTest(APITestCase, URLPatternsTestCase):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("building.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.dzo = DzongkhagFactory(name="Mongar")
        self.gewog = GewogFactory(name="Mongar", dzongkhag=self.dzo)

        self.user = UserFactory(username="user", roles=Role.objects.filter(name__in=["user"]).values_list("id", flat=True))
        ProfileFactory(user=self.user, dzongkhag=self.dzo)

        self.planner = UserFactory(username="planner", roles=Role.objects.filter(name__in=["dpd", "upd"]).values_list("id", flat=True))
        ProfileFactory(user=self.planner, dzongkhag=self.dzo)

        self.gewog_adm = UserFactory(username="gewog_adm", roles=Role.objects.filter(name__in=["gadm"]).values_list("id", flat=True))
        ProfileFactory(user=self.gewog_adm, dzongkhag=self.dzo, gewog=self.gewog)

        res = self.client.post(reverse("token_obtain_pair"), {"username": self.gewog_adm.username, "password": "Dcpl@123"})
        self.token = json.loads(res.content)["data"]["access"]
        application = ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog)
        ApplicantFactory(application=application)
        self.permit = PermitFactory(user=self.user, application=application, dzongkhag=self.dzo, gewog=self.gewog)
        DesignTeamFactory(teamable=self.permit)

    def test_invalid_transition(self):
        data = {"action": "schedule"}
        url = reverse("transitions", kwargs={"pk": self.permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertTrue("error" in response_data)
        self.assertEqual(response_data["error"], "Invalid transition")

    def test_reject_remarks(self):
        data = {"action": "reject"}
        url = reverse("transitions", kwargs={"pk": self.permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertTrue("error" in response_data)
        self.assertEqual(response_data["error"], "You should provide rejection remarks.")

    def test_reject(self):
        data = {"action": "reject", "reject_remarks": "Some documents are missing"}
        url = reverse("transitions", kwargs={"pk": self.permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "rejected")

    def test_close(self):
        data = {"action": "close"}
        url = reverse("transitions", kwargs={"pk": self.permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "closed")

    def test_forward_to_dzo(self):
        self.dzo_adm = UserFactory.create(
            username="dzo_adm",
            roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True),
        )
        ProfileFactory(user=self.dzo_adm, dzongkhag=self.dzo)

        data = {
            "action": "forward_to_dzo",
        }

        task_pool = self.permit.task_pools.first()
        task_pool.state = "in_progress"
        task_pool.save()

        url = reverse("transitions", kwargs={"pk": self.permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "forwarded_to_dzo")

    def test_forward_to_moit(self):
        self.dzo_adm = UserFactory.create(username="dzo_adm", roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True))
        ProfileFactory(user=self.dzo_adm, dzongkhag=self.dzo)

        self.moit_adm = UserFactory.create(username="moit_adm", roles=Role.objects.filter(name__in=["moitadm"]).values_list("id", flat=True))
        ProfileFactory(user=self.moit_adm, dzongkhag=self.dzo)

        task_pool = self.permit.task_pools.first()
        task_pool.state = "in_progress"
        task_pool.save()

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": self.dzo_adm.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        data = {
            "action": "forward_to_moit",
        }
        url = reverse("transitions", kwargs={"pk": self.permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        self.permit.state = "forwarded_to_dzo"
        self.permit.save()
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "forwarded_to_moit")

    def test_assign_architect_by_dzo(self):
        self.dzo_architect = UserFactory.create(
            username="dzo_architect",
            roles=Role.objects.filter(name__in=["dar"]).values_list("id", flat=True),
        )
        ProfileFactory(user=self.dzo_architect, dzongkhag=self.dzo)

        self.dzo_adm = UserFactory.create(
            username="dzo_adm",
            roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True),
        )
        ProfileFactory(user=self.dzo_adm, dzongkhag=self.dzo)

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": self.dzo_adm.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        data = {"action": "assign_architect", "architect_id": self.dzo_architect.id}
        url = reverse("transitions", kwargs={"pk": self.permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        self.permit.state = "forwarded_to_dzo"
        self.permit.save()
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "architect_assigned")

    def test_assign_architect_by_moit(self):
        moit_architect = UserFactory.create(username="moit_architect", roles=Role.objects.filter(name__in=["moitar"]).values_list("id", flat=True))
        ProfileFactory(user=moit_architect, dzongkhag=self.dzo)

        moit_adm = UserFactory.create(username="moit_adm", roles=Role.objects.filter(name__in=["moitadm"]).values_list("id", flat=True))
        ProfileFactory(user=moit_adm, dzongkhag=self.dzo)

        res = self.client.post(reverse("token_obtain_pair"), {"username": moit_adm.username, "password": "Dcpl@123"})
        token = json.loads(res.content)["data"]["access"]
        data = {"action": "assign_architect", "architect_id": moit_architect.id}
        url = reverse("transitions", kwargs={"pk": self.permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {token}"
        self.permit.state = "forwarded_to_moit"
        self.permit.save()
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "architect_assigned")

    def test_assign_architect_by_thromde(self):
        applicant = self.permit.application.applicant
        applicant.land_location_flag = "U"
        applicant.save()
        self.permit.nature = "urban"
        self.permit.save()

        thrm_architect = UserFactory.create(username="thrm_architect", roles=Role.objects.filter(name__in=["thrmar"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_architect, dzongkhag=self.dzo)

        thrm_adm = UserFactory.create(username="thrm_adm", roles=Role.objects.filter(name__in=["thrmadm"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_adm, dzongkhag=self.dzo)

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": thrm_adm.username, "password": "Dcpl@123"},
        )
        token = json.loads(res.content)["data"]["access"]
        data = {"action": "assign_architect", "architect_id": thrm_architect.id}
        url = reverse("transitions", kwargs={"pk": self.permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "architect_assigned")
