import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase
from django.core.management import call_command
from common.factories import DesignTeamFactory, TaskPoolFactory
from planning.models import *
from user.factories import SettingFactory, UserFactory, ProfileFactory
from building.factories import PermitFactory
from address.factories import DzongkhagFactory, GewogFactory, ThromdeFactory
from django.urls import path, include
from planning.factories import ApplicationFactory, ApplicantFactory
from user.models import Role


class PermitTransitionTest(APITestCase, URLPatternsTestCase):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("building.urls")),
        path("api/v1/", include("common.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.dzo = DzongkhagFactory(name="<PERSON>gar")
        self.gewog = GewogFactory(name="Mongar", dzongkhag=self.dzo)

        self.user = UserFactory.create(username="user", roles=Role.objects.filter(name__in=["gadm"]).values_list("id", flat=True))
        ProfileFactory(user=self.user, dzongkhag=self.dzo)

        self.planner = UserFactory.create(username="planner", roles=Role.objects.filter(name__in=["dpd", "upd"]).values_list("id", flat=True))
        ProfileFactory(user=self.planner, dzongkhag=self.dzo)

        self.gewog_adm = UserFactory.create(username="gewog_adm", roles=Role.objects.filter(name__in=["gadm"]).values_list("id", flat=True))
        ProfileFactory(user=self.gewog_adm, dzongkhag=self.dzo, gewog=self.gewog)

        res = self.client.post(reverse("token_obtain_pair"), {"username": self.gewog_adm.username, "password": "Dcpl@123"})
        self.token = json.loads(res.content)["data"]["access"]
        application = ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog)
        ApplicantFactory(application=application)
        self.permit = PermitFactory(user=self.user, application=application, dzongkhag=self.dzo, gewog=self.gewog)
        DesignTeamFactory(teamable=self.permit)
        self.task_pool = TaskPoolFactory(poolable=self.permit, user=self.gewog_adm, role=self.gewog_adm.roles.first())
        self.task_pool.state = "in_progress"
        self.task_pool.save()

    def test_slab_required(self):
        self.permit.state = "forwarded_to_dzo"
        self.permit.save()

        dzo_se = UserFactory.create(username="dzo_se", roles=Role.objects.filter(name__in=["dse"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_se, dzongkhag=self.dzo)

        dzo_ee = UserFactory.create(username="dzo_ee", roles=Role.objects.filter(name__in=["dee"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_ee, dzongkhag=self.dzo)

        dzo_wsf = UserFactory.create(username="dzo_wsf", roles=Role.objects.filter(name__in=["dwsf"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_wsf, dzongkhag=self.dzo)

        dzo_ar = UserFactory.create(username="dzo_ar", roles=Role.objects.filter(name__in=["dar"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_ar, dzongkhag=self.dzo)

        task_pool = TaskPool.objects.create(poolable=self.permit, user=dzo_ar, role=dzo_ar.current_role)
        task_pool.state = "in_progress"
        task_pool.save()
        data = {"action": "approve"}
        url = reverse("task_pool_transitions", kwargs={"pk": task_pool.id})
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dzo_ar.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response_data["error"], "You should provide slabs informations.")

    def test_all_slab_info_required(self):
        self.permit.state = "forwarded_to_dzo"
        self.permit.save()

        dzo_se = UserFactory.create(username="dzo_se", roles=Role.objects.filter(name__in=["dse"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_se, dzongkhag=self.dzo)

        dzo_ee = UserFactory.create(username="dzo_ee", roles=Role.objects.filter(name__in=["dee"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_ee, dzongkhag=self.dzo)

        dzo_wsf = UserFactory.create(username="dzo_wsf", roles=Role.objects.filter(name__in=["dwsf"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_wsf, dzongkhag=self.dzo)

        dzo_ar = UserFactory.create(username="dzo_ar", roles=Role.objects.filter(name__in=["dar"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_ar, dzongkhag=self.dzo)

        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=dzo_ar, role=dzo_ar.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()
        data = {
            "action": "approve",
            "no_of_floors": 4,
            "slabs": [{"floor_type": "basement", "area": 230, "usage": "commercial"}],
        }
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dzo_ar.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response_data["error"],
            f"You need to provide information for all {self.permit.no_of_floors} slabs.",
        )

    def test_no_adm(self):
        self.permit.state = "forwarded_to_dzo"
        self.permit.save()

        dzo_se = UserFactory.create(
            username="dzo_se",
            roles=Role.objects.filter(name__in=["dse"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_se, dzongkhag=self.dzo)

        dzo_ee = UserFactory.create(
            username="dzo_ee",
            roles=Role.objects.filter(name__in=["dee"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_ee, dzongkhag=self.dzo)

        dzo_wsf = UserFactory.create(
            username="dzo_wsf",
            roles=Role.objects.filter(name__in=["dwsf"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_wsf, dzongkhag=self.dzo)

        dzo_ar = UserFactory.create(
            username="dzo_ar",
            roles=Role.objects.filter(name__in=["dar"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_ar, dzongkhag=self.dzo)

        building_task_pool = TaskPoolFactory.create(poolable=self.permit, user=dzo_ar, role=dzo_ar.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()
        data = {
            "action": "approve",
            "no_of_floors": 4,
            "slabs": [
                {"floor_type": "basement", "area": 230, "usage": "commercial"},
                {"floor_type": "basement", "area": 250, "usage": "commercial"},
                {"floor_type": "basement", "area": 210, "usage": "residential"},
                {"floor_type": "basement", "area": 200, "usage": "residential"},
            ],
        }
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dzo_ar.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response_data["error"], f"There is no admin for {dzo_ar.current_role.description}")

    def test_rate_setting_notset(self):
        self.permit.state = "forwarded_to_dzo"
        self.permit.save()

        dzo_se = UserFactory.create(username="dzo_se", roles=Role.objects.filter(name__in=["dse"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_se, dzongkhag=self.dzo)

        dzo_ee = UserFactory.create(username="dzo_ee", roles=Role.objects.filter(name__in=["dee"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_ee, dzongkhag=self.dzo)

        dzo_wsf = UserFactory.create(username="dzo_wsf", roles=Role.objects.filter(name__in=["dwsf"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_wsf, dzongkhag=self.dzo)

        dzo_ar = UserFactory.create(username="dzo_ar", roles=Role.objects.filter(name__in=["dar"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_ar, dzongkhag=self.dzo)

        dzo_adm = UserFactory.create(username="dzo_adm", roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_adm, dzongkhag=self.dzo)

        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=dzo_ar, role=dzo_ar.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()
        data = {
            "action": "approve",
            "no_of_floors": 4,
            "slabs": [
                {"floor_type": "basement", "area": 230, "usage": "commercial"},
                {"floor_type": "basement", "area": 250, "usage": "commercial"},
                {"floor_type": "basement", "area": 210, "usage": "residential"},
                {"floor_type": "basement", "area": 200, "usage": "residential"},
            ],
        }
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": dzo_ar.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response_data["error"],
            "You have not set rate in the setting, please contact your respective administration to set the rate in their setting.",
        )

    def test_duplicate_floor_type(self):
        self.permit.state = "forwarded_to_dzo"
        self.permit.save()

        dzo_se = UserFactory.create(username="dzo_se", roles=Role.objects.filter(name__in=["dse"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_se, dzongkhag=self.dzo)

        dzo_ee = UserFactory.create(username="dzo_ee", roles=Role.objects.filter(name__in=["dee"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_ee, dzongkhag=self.dzo)

        dzo_wsf = UserFactory.create(username="dzo_wsf", roles=Role.objects.filter(name__in=["dwsf"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_wsf, dzongkhag=self.dzo)

        dzo_ar = UserFactory.create(username="dzo_ar", roles=Role.objects.filter(name__in=["dar"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_ar, dzongkhag=self.dzo)

        dzo_adm = UserFactory.create(username="dzo_adm", roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_adm, dzongkhag=self.dzo)
        SettingFactory(user=dzo_adm, key="rate", value=16.14)

        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=dzo_ar, role=dzo_ar.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()
        data = {
            "action": "approve",
            "land_pooling": True,
            "no_of_floors": 4,
            "slabs": [
                {"floor_type": "basement", "area": 230, "usage": "commercial"},
                {"floor_type": "basement", "area": 250, "usage": "commercial"},
                {"floor_type": "basement", "area": 210, "usage": "residential"},
                {"floor_type": "basement", "area": 200, "usage": "residential"},
            ],
        }
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})
        res = self.client.post(reverse("token_obtain_pair"), {"username": dzo_ar.username, "password": "Dcpl@123"})
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertTrue("already exists" in response_data["error"])

    def test_approve_by_dzo_ar(self):
        self.permit.state = "forwarded_to_dzo"
        self.permit.save()

        dzo_se = UserFactory.create(username="dzo_se", roles=Role.objects.filter(name__in=["dse"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_se, dzongkhag=self.dzo)

        dzo_ee = UserFactory.create(username="dzo_ee", roles=Role.objects.filter(name__in=["dee"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_ee, dzongkhag=self.dzo)

        dzo_wsf = UserFactory.create(username="dzo_wsf", roles=Role.objects.filter(name__in=["dwsf"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_wsf, dzongkhag=self.dzo)

        dzo_ar = UserFactory.create(username="dzo_ar", roles=Role.objects.filter(name__in=["dar"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_ar, dzongkhag=self.dzo)

        dzo_adm = UserFactory.create(username="dzo_adm", roles=Role.objects.filter(name__in=["dadm"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_adm, dzongkhag=self.dzo)
        SettingFactory(user=dzo_adm, key="rate", value=16.14)

        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=dzo_ar, role=dzo_ar.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()
        data = {
            "action": "approve",
            "land_pooling": True,
            "no_of_floors": 4,
            "slabs": [
                {"floor_type": "basement", "area": 230, "usage": "commercial"},
                {"floor_type": "first_floor", "area": 250, "usage": "commercial"},
                {"floor_type": "second_floor", "area": 210, "usage": "residential"},
                {"floor_type": "jamtho_floor", "area": 200, "usage": "residential"},
            ],
        }
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})
        res = self.client.post(reverse("token_obtain_pair"), {"username": dzo_ar.username, "password": "Dcpl@123"})
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "completed")
        self.assertEqual(self.permit.slabs.all().count(), len(data.get("slabs")))

    def test_approve_by_moit_ar(self):
        self.permit.state = "forwarded_to_moit"
        self.permit.save()

        moit_se = UserFactory.create(username="moit_se", roles=Role.objects.filter(name__in=["moitse"]).values_list("id", flat=True))
        ProfileFactory(user=moit_se, dzongkhag=self.dzo)

        moit_ee = UserFactory.create(username="moit_ee", roles=Role.objects.filter(name__in=["moitee"]).values_list("id", flat=True))
        ProfileFactory(user=moit_ee, dzongkhag=self.dzo)

        moit_wsf = UserFactory.create(username="moit_wsf", roles=Role.objects.filter(name__in=["moitwsf"]).values_list("id", flat=True))
        ProfileFactory(user=moit_wsf, dzongkhag=self.dzo)

        moit_ar = UserFactory.create(username="moit_ar", roles=Role.objects.filter(name__in=["moitar"]).values_list("id", flat=True))
        ProfileFactory(user=moit_ar, dzongkhag=self.dzo)

        moit_adm = UserFactory.create(username="moit_adm", roles=Role.objects.filter(name__in=["moitadm"]).values_list("id", flat=True))
        ProfileFactory(user=moit_adm, dzongkhag=self.dzo)
        SettingFactory(user=moit_adm, key="rate", value=16.14)

        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=moit_ar, role=moit_ar.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()
        data = {
            "action": "approve",
            "land_pooling": True,
            "no_of_floors": 4,
            "slabs": [
                {"floor_type": "basement", "area": 230, "usage": "commercial"},
                {"floor_type": "first_floor", "area": 250, "usage": "commercial"},
                {"floor_type": "second_floor", "area": 210, "usage": "residential"},
                {"floor_type": "jamtho_floor", "area": 200, "usage": "residential"},
            ],
        }
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})
        res = self.client.post(reverse("token_obtain_pair"), {"username": moit_ar.username, "password": "Dcpl@123"})
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "completed")
        self.assertEqual(self.permit.slabs.all().count(), len(data.get("slabs")))

    def test_approve_by_thrm_ar(self):
        thromde = ThromdeFactory(dzongkhag=self.dzo)
        self.permit.thromde = thromde
        self.permit.save()

        thrm_se = UserFactory.create(username="thrm_se", roles=Role.objects.filter(name__in=["thrmse"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_se, dzongkhag=self.dzo, thromde=thromde)

        thrm_ee = UserFactory.create(username="thrm_ee", roles=Role.objects.filter(name__in=["thrmee"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_ee, dzongkhag=self.dzo, thromde=thromde)

        thrm_wsf = UserFactory.create(username="thrm_wsf", roles=Role.objects.filter(name__in=["thrmwsf"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_wsf, dzongkhag=self.dzo, thromde=thromde)

        thrm_ar = UserFactory.create(username="thrm_ar", roles=Role.objects.filter(name__in=["thrmar"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_ar, dzongkhag=self.dzo, thromde=thromde)

        thrm_adm = UserFactory.create(username="thrm_adm", roles=Role.objects.filter(name__in=["thrmadm"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_adm, dzongkhag=self.dzo, thromde=thromde)
        SettingFactory(user=thrm_adm, key="rate", value=16.14)

        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=thrm_ar, role=thrm_ar.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()
        data = {
            "action": "approve",
            "land_pooling": True,
            "no_of_floors": 4,
            "slabs": [
                {"floor_type": "basement", "area": 230, "usage": "commercial"},
                {"floor_type": "first_floor", "area": 250, "usage": "commercial"},
                {"floor_type": "second_floor", "area": 210, "usage": "residential"},
                {"floor_type": "jamtho_floor", "area": 200, "usage": "residential"},
            ],
        }
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})
        res = self.client.post(reverse("token_obtain_pair"), {"username": thrm_ar.username, "password": "Dcpl@123"})
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "completed")
        self.assertEqual(self.permit.slabs.all().count(), len(data.get("slabs")))
