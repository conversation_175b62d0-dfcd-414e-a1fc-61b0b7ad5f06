import json, time
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from django.core.management import call_command
from common.factories import DesignTeamFactory
from planning.models import *
from user.factories import UserFactory, ProfileFactory
from building.factories import PermitFactory
from address.factories import DzongkhagFactory, GewogFactory
from file.factories import AttachmentFactory
from planning.factories import ApplicantFactory, ApplicationFactory
from user.models import Role


class CRUDTest(APITestCase):
    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.dzo = DzongkhagFactory(name="Mongar")
        self.gewog = GewogFactory(name="Mongar", dzongkhag=self.dzo)
        self.user = UserFactory.create(username="user", roles=Role.objects.filter(name__in=["user"]).values_list("id", flat=True))
        ProfileFactory(user=self.user, dzongkhag=self.dzo)

        self.planner = UserFactory.create(username="planner", roles=Role.objects.filter(name__in=["dpd", "upd"]).values_list("id", flat=True))
        ProfileFactory(user=self.planner, dzongkhag=self.dzo)

        self.gewog_adm = UserFactory.create(username="gewog_adm", roles=Role.objects.filter(name__in=["gadm"]).values_list("id", flat=True))
        ProfileFactory(user=self.gewog_adm, dzongkhag=self.dzo, gewog=self.gewog)

        res = self.client.post(reverse("token_obtain_pair"), {"username": self.user.username, "password": "Dcpl@123"})
        self.token = json.loads(res.content)["data"]["access"]
        application = ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog)
        ApplicantFactory(application=application)
        self.data = {
            "application_id": application.id,
            "construction_type": "permanent",
            "star": 2,  # Only required when construction type is hospitality
            "building_type": "traditional",
            # This is required only when building type is others
            "building_type_others": "Bago house",
            "no_of_floors": 4,
            "no_of_units": 8,
            "building_topology": "residential",
            "architectural_drawing_id": AttachmentFactory().id,
            "electrical_drawing_id": AttachmentFactory().id,
            "structural_drawing_id": AttachmentFactory().id,
            "plumbing_sanitation_drawing_id": AttachmentFactory().id,
            "design_team": {
                "architect_cid": "129399393",
                "architect_name": "Tashi Dendup",
                "architect_certificate_id": AttachmentFactory().id,
                "electrical_engineer_cid": "129399393",
                "electrical_engineer_name": "Tashi Dendup",
                "electrical_engineer_certificate_id": AttachmentFactory().id,
                "structural_engineer_cid": "129399393",
                "structural_engineer_name": "Tashi Dendup",
                "structural_engineer_certificate_id": AttachmentFactory().id,
                "design_team_certificate_id": AttachmentFactory().id,
            },
        }

    def test_permit_create(self):
        url = reverse("permits")
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.post(url, self.data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "requested")

    def test_permit_udpate(self):
        permit = PermitFactory(
            dzongkhag=self.dzo,
            gewog=self.gewog,
            user=self.user,
            application=ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog),
            serial_no=int(time.time() * 1000),
        )
        ApplicantFactory(application=permit.application)
        DesignTeamFactory(teamable=permit)

        url = reverse("permits", kwargs={"pk": permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, self.data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)

    def test_permit_resubmit(self):
        permit = PermitFactory(
            dzongkhag=self.dzo,
            gewog=self.gewog,
            user=self.user,
            application=ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog),
            serial_no=int(time.time() * 1000),
        )
        ApplicantFactory(application=permit.application)
        DesignTeamFactory(teamable=permit)

        dzongdag = UserFactory.create(username="dzongdag", roles=Role.objects.filter(name__in=["ddz"]).values_list("id", flat=True))
        ProfileFactory(user=dzongdag, dzongkhag=self.dzo)

        dzo_se = UserFactory.create(username="dzo_se", roles=Role.objects.filter(name__in=["dse"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_se, dzongkhag=self.dzo)

        dzo_ee = UserFactory.create(username="dzo_ee", roles=Role.objects.filter(name__in=["dee"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_ee, dzongkhag=self.dzo)

        dzo_wsf = UserFactory.create(username="dzo_wsf", roles=Role.objects.filter(name__in=["dwsf"]).values_list("id", flat=True))
        ProfileFactory(user=dzo_wsf, dzongkhag=self.dzo)

        dzo_ar = UserFactory.create(
            username="dzo_ar",
            roles=Role.objects.filter(name__in=["dar"]).values_list("id", flat=True),
        )
        ProfileFactory(user=dzo_ar, dzongkhag=self.dzo)

        TaskPool.objects.create(poolable=permit, user=dzo_ar, role=dzo_ar.current_role)
        TaskPool.objects.create(poolable=permit, user=dzo_se, role=dzo_se.current_role)
        TaskPool.objects.create(poolable=permit, user=dzo_wsf, role=dzo_wsf.current_role)
        btp = TaskPool.objects.create(poolable=permit, user=dzo_ee, role=dzo_ee.current_role)
        btp.state = "rejected"
        btp.save()

        permit.state = "rejected"
        permit.save()

        url = reverse("permits", kwargs={"pk": permit.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, self.data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "resubmitted")
        self.assertEqual(permit.task_pools.get(user=dzo_ee).state, "assigned")

    def test_permit_list(self):
        permit = PermitFactory(
            dzongkhag=self.dzo,
            gewog=self.gewog,
            user=self.user,
            application=ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog),
        )
        ApplicantFactory(application=permit.application)
        DesignTeamFactory(teamable=permit)
        permit1 = PermitFactory(
            dzongkhag=self.dzo,
            gewog=self.gewog,
            user=self.user,
            application=ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog),
        )
        ApplicantFactory(application=permit1.application)
        DesignTeamFactory(teamable=permit1)
        permit2 = PermitFactory(
            dzongkhag=self.dzo,
            gewog=self.gewog,
            user=self.user,
            application=ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog),
        )
        ApplicantFactory(application=permit2.application)
        DesignTeamFactory(teamable=permit2)

        url = reverse("permits")

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.get(url)
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(len(response_data["data"]), 3)

    def test_permit_detail(self):
        permit = PermitFactory(
            dzongkhag=self.dzo,
            gewog=self.gewog,
            user=self.user,
            application=ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog),
        )
        ApplicantFactory(application=permit.application)
        DesignTeamFactory(teamable=permit)

        url = reverse("permits", kwargs={"pk": permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.get(url)
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)

    def test_permit_destroy(self):
        permit = PermitFactory(
            dzongkhag=self.dzo,
            gewog=self.gewog,
            user=self.user,
            application=ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog),
        )
        ApplicantFactory(application=permit.application)
        DesignTeamFactory(teamable=permit)

        url = reverse("permits", kwargs={"pk": permit.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
