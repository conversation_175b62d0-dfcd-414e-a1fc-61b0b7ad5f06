import json
import time
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase
from django.core.management import call_command
from building.models import Permit
from common.factories import DesignTeamFactory
from planning.models import *
from user.factories import UserFactory, ProfileFactory
from building.factories import PermitFactory
from address.factories import DzongkhagFactory, GewogFactory, ThromdeFactory
from django.urls import path, include
from planning.factories import ApplicationFactory, ApplicantFactory
from user.models import Role


class TaskPoolApprovalTest(APITestCase, URLPatternsTestCase):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("building.urls")),
        path("api/v1/", include("common.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.dzo = DzongkhagFactory(name="<PERSON>gar")
        self.gewog = GewogFactory(name="Mongar", dzongkhag=self.dzo)

        self.user = UserFactory.create(
            username="user",
            roles=Role.objects.filter(name__in=["user"]).values_list("id", flat=True),
        )
        ProfileFactory(user=self.user, dzongkhag=self.dzo)

        self.planner = UserFactory.create(
            username="planner",
            roles=Role.objects.filter(name__in=["dpd", "upd"]).values_list("id", flat=True),
        )
        ProfileFactory(user=self.planner, dzongkhag=self.dzo)

        self.gewog_adm = UserFactory.create(
            username="gewog_adm",
            roles=Role.objects.filter(name__in=["gadm"]).values_list("id", flat=True),
        )
        ProfileFactory(user=self.gewog_adm, dzongkhag=self.dzo, gewog=self.gewog)

        application = ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog)
        ApplicantFactory(application=application)
        self.permit = PermitFactory(
            user=self.user,
            application=application,
            dzongkhag=self.dzo,
            gewog=self.gewog,
            serial_no=int(time.time() * 1000),
        )
        DesignTeamFactory(teamable=self.permit)

    def test_approve_by_bpc(self):
        thromde = ThromdeFactory(dzongkhag=self.dzo)
        self.permit.thromde = thromde
        self.permit.fee = 1000
        self.permit.save()

        bpc = UserFactory.create(username="bpc", roles=Role.objects.filter(name__in=["bpc"]).values_list("id", flat=True))
        ProfileFactory(user=bpc, dzongkhag=self.dzo, thromde=thromde)

        thrm_se = UserFactory.create(username="thrm_se", roles=Role.objects.filter(name__in=["thrmse"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_se, dzongkhag=self.dzo, thromde=thromde)

        thrm_ee = UserFactory.create(username="thrm_ee", roles=Role.objects.filter(name__in=["thrmee"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_ee, dzongkhag=self.dzo, thromde=thromde)

        thrm_wsf = UserFactory.create(username="thrm_wsf", roles=Role.objects.filter(name__in=["thrmwsf"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_wsf, dzongkhag=self.dzo, thromde=thromde)

        thrm_ar = UserFactory.create(username="thrm_ar", roles=Role.objects.filter(name__in=["thrmar"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_ar, dzongkhag=self.dzo, thromde=thromde)

        btp = TaskPool.objects.create(poolable=self.permit, user=thrm_ar, role=thrm_ar.current_role)
        btp.state = "completed"
        btp.save()
        btp1 = TaskPool.objects.create(poolable=self.permit, user=thrm_se, role=thrm_se.current_role)
        btp1.state = "completed"
        btp1.save()
        btp2 = TaskPool.objects.create(poolable=self.permit, user=thrm_wsf, role=thrm_wsf.current_role)
        btp2.state = "completed"
        btp2.save()
        btp3 = TaskPool.objects.create(poolable=self.permit, user=thrm_ee, role=thrm_ee.current_role)
        btp3.state = "completed"
        btp3.save()
        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=bpc, role=bpc.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()
        data = {"action": "approve"}
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})
        res = self.client.post(reverse("token_obtain_pair"), {"username": bpc.username, "password": "Dcpl@123"})
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "completed")

    def test_approve_by_tcb(self):
        thromde = ThromdeFactory(dzongkhag=self.dzo)
        self.permit.thromde = thromde
        self.permit.fee = 1000
        self.permit.save()

        tcb = UserFactory.create(
            username="tcb",
            roles=Role.objects.filter(name__in=["tcb"]).values_list("id", flat=True),
        )
        ProfileFactory(user=tcb, dzongkhag=self.dzo, thromde=thromde)

        thrm_se = UserFactory.create(
            username="thrm_se",
            roles=Role.objects.filter(name__in=["thrmse"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_se, dzongkhag=self.dzo, thromde=thromde)

        thrm_ee = UserFactory.create(
            username="thrm_ee",
            roles=Role.objects.filter(name__in=["thrmee"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_ee, dzongkhag=self.dzo, thromde=thromde)

        thrm_wsf = UserFactory.create(
            username="thrm_wsf",
            roles=Role.objects.filter(name__in=["thrmwsf"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_wsf, dzongkhag=self.dzo, thromde=thromde)

        thrm_ar = UserFactory.create(
            username="thrm_ar",
            roles=Role.objects.filter(name__in=["thrmar"]).values_list("id", flat=True),
        )
        ProfileFactory(user=thrm_ar, dzongkhag=self.dzo, thromde=thromde)

        btp = TaskPool.objects.create(poolable=self.permit, user=thrm_ar, role=thrm_ar.current_role)
        btp.state = "completed"
        btp.save()
        btp1 = TaskPool.objects.create(poolable=self.permit, user=thrm_se, role=thrm_se.current_role)
        btp1.state = "completed"
        btp1.save()
        btp2 = TaskPool.objects.create(poolable=self.permit, user=thrm_wsf, role=thrm_wsf.current_role)
        btp2.state = "completed"
        btp2.save()
        btp3 = TaskPool.objects.create(poolable=self.permit, user=thrm_ee, role=thrm_ee.current_role)
        btp3.state = "completed"
        btp3.save()
        building_task_pool = TaskPool.objects.create(poolable=self.permit, user=tcb, role=tcb.current_role)
        building_task_pool.state = "in_progress"
        building_task_pool.save()
        data = {"action": "approve"}
        url = reverse("task_pool_transitions", kwargs={"pk": building_task_pool.id})
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": tcb.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "completed")

    def test_await_payment(self):
        thromde = ThromdeFactory(dzongkhag=self.dzo)
        self.permit.thromde = thromde
        self.permit.fee = 2000
        self.permit.save()

        thrm_se = UserFactory.create(username="thrm_se", roles=Role.objects.filter(name__in=["thrmse"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_se, dzongkhag=self.dzo, thromde=thromde)

        thrm_ee = UserFactory.create(username="thrm_ee", roles=Role.objects.filter(name__in=["thrmee"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_ee, dzongkhag=self.dzo, thromde=thromde)

        thrm_wsf = UserFactory.create(username="thrm_wsf", roles=Role.objects.filter(name__in=["thrmwsf"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_wsf, dzongkhag=self.dzo, thromde=thromde)

        thrm_ar = UserFactory.create(username="thrm_ar", roles=Role.objects.filter(name__in=["thrmar"]).values_list("id", flat=True))
        ProfileFactory(user=thrm_ar, dzongkhag=self.dzo, thromde=thromde)

        btp = TaskPool.objects.create(poolable=self.permit, user=thrm_ar, role=thrm_ar.current_role)
        btp.state = "completed"
        btp.save()
        btp1 = TaskPool.objects.create(poolable=self.permit, user=thrm_se, role=thrm_se.current_role)
        btp1.state = "completed"
        btp1.save()
        btp2 = TaskPool.objects.create(poolable=self.permit, user=thrm_wsf, role=thrm_wsf.current_role)
        btp2.state = "completed"
        btp2.save()
        btp3 = TaskPool.objects.create(poolable=self.permit, user=thrm_ee, role=thrm_ee.current_role)
        btp3.state = "in_progress"
        btp3.save()

        data = {"action": "approve"}
        url = reverse("task_pool_transitions", kwargs={"pk": btp3.id})
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": thrm_ee.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "completed")
        self.assertEqual(Permit.objects.get(id=self.permit.id).state, "awaiting_payment")
