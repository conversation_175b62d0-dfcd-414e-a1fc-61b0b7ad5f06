import factory
from building.models import *
from address.factories import *
from file.factories import *
from user.factories import UserFactory


class PermitFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Permit

    dzongkhag = factory.SubFactory(DzongkhagFactory)
    gewog = factory.SubFactory(GewogFactory)
    village = factory.SubFactory(VillageFactory)
    construction_type = "permanent"
    star = 3
    building_type = "concrete"
    building_type_others = "Bago phai"
    no_of_floors = 4
    no_of_units = 8
    building_topology = "hospitality"
    building_topology_others = "Riga phai"
    architectural_drawing = factory.SubFactory(AttachmentFactory)
    electrical_drawing = factory.SubFactory(AttachmentFactory)
    structural_drawing = factory.SubFactory(AttachmentFactory)
    plumbing_sanitation_drawing = factory.SubFactory(AttachmentFactory)
