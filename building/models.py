from pathlib import PurePath
from django.db import models
from building.enums import PermitStatus
from common.enums import LandNature, Purpose, DevelopmentType
from user.models import User
from django_fsm import FSMField
from building.helpers.permit import PermitHelper
from building.transitions.permit import PermitTransition
from django.contrib.contenttypes.fields import GenericRelation
from django.utils.translation import gettext_lazy as _


class Permit(models.Model, PermitHelper, PermitTransition):
    by = models.ForeignKey(User, related_name="assigned_permits", null=True, blank=True, on_delete=models.SET_NULL)
    user = models.ForeignKey(User, null=False, related_name="permits", on_delete=models.CASCADE)
    design_permit = models.ForeignKey("design.Permit", null=True, related_name="building_permits", on_delete=models.CASCADE)
    nature = models.CharField(max_length=50, null=True, choices=LandNature.choices, default=LandNature.RURAL)
    serial_no = models.Char<PERSON>ield(max_length=50, null=True)
    construction_id = models.CharField(max_length=100, null=True, blank=True, help_text="Unique identifier for tracking construction across all modules")
    state = FSMField(max_length=20, null=False, choices=PermitStatus.choices, default=PermitStatus.INITIATED)
    region = models.ForeignKey("address.Region", null=True, on_delete=models.SET_NULL, blank=True)
    dzongkhag = models.ForeignKey("address.Dzongkhag", null=True, on_delete=models.SET_NULL, blank=True)
    thromde = models.ForeignKey("address.Thromde", null=True, on_delete=models.SET_NULL, blank=True)
    gewog = models.ForeignKey("address.Gewog", null=True, on_delete=models.SET_NULL, blank=True)
    village = models.ForeignKey("address.Village", null=True, on_delete=models.SET_NULL, blank=True)
    construction_type = models.ForeignKey("common.ConstructionType", on_delete=models.SET_NULL, null=True, related_name="building_permits")
    building_type = models.ForeignKey("common.BuildingType", on_delete=models.SET_NULL, null=True, related_name="building_permits")
    use = models.ForeignKey("common.Use", on_delete=models.SET_NULL, null=True, related_name="building_permits")
    proposal_type = models.ForeignKey("common.ProposalType", on_delete=models.SET_NULL, null=True, related_name="building_permits")
    star = models.IntegerField(null=True, default=0)
    no_of_floors = models.IntegerField(null=True, blank=True)
    no_of_units = models.IntegerField(null=True, blank=True)
    task_pools = GenericRelation("common.TaskPool", related_query_name="building_permit")
    design_teams = GenericRelation("common.DesignTeam", related_query_name="building_permit")
    payments = GenericRelation("payment.Payment", related_query_name="building_permit")
    slabs = GenericRelation("common.Slab", related_query_name="building_permit")
    fee = models.FloatField(null=True, blank=True)
    land_pooling = models.BooleanField(default=False)
    purpose = models.CharField(max_length=100, null=True, blank=True, choices=Purpose.choices, default=Purpose.BUILDING_CONSTRUCTION)
    development_type = models.CharField(max_length=100, null=True, blank=True, choices=DevelopmentType.choices, default=DevelopmentType.SMALL)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        verbose_name = _("permit")
        verbose_name_plural = _("permits")
        indexes = [
            models.Index(fields=["created_at"]),
            models.Index(fields=["state"]),
            models.Index(fields=["dzongkhag"]),
            models.Index(fields=["gewog"]),
            models.Index(fields=["construction_id"]),
            models.Index(fields=["building_type"]),
            models.Index(fields=["use"]),
        ]
