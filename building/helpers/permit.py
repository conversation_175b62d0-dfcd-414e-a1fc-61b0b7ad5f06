from rest_framework.exceptions import ValidationError
from common.enums import LandNature
from django.db import models
import datetime
from user.models import User


class PermitHelper:
    def can_forward_to_roid(self, current_user):
        current_role = current_user.current_role
        if current_role.name == "dce" and self.nature == "rural":
            return True
        else:
            return False

    def can_forward_to_dhs(self, current_user):
        current_role = current_user.current_role
        if current_role.name in ["dce", "roidce"]:
            return True
        else:
            return False

    def can_assign_architect(self, current_user):
        current_role = current_user.current_role
        if current_role.name in ["dce", "tce", "roidce", "dhsce"]:
            return True
        else:
            return False

    def can_reject(self, current_user):
        return True

    def can_approve(self, current_user):
        return True

    def can_close(self, current_user):
        return True

    def pay(self, payment=None):
        self.approve({}, by=self.user)
        self.save()

    @property
    def turn_around_time(self):
        turnaround_time = self.task_pools.aggregate(earliest_created_at=models.Min("created_at"), latest_created_at=models.Max("created_at"))
        return str((turnaround_time["latest_created_at"] or datetime.date.today()) - (turnaround_time["earliest_created_at"] or datetime.date.today()))

    @property
    def nature_text(self):
        return dict(LandNature.choices)[self.nature]
