import time
from building.models import Permit


def perform_save(self, serializer):
    permit_id = self.kwargs.get("pk")
    permit = Permit.objects.get(id=permit_id) if permit_id else None
    design_permit = serializer.validated_data["design_permit"] or permit.design_permit
    if not permit and design_permit:
        serializer.validated_data["region_id"] = design_permit.region_id
        serializer.validated_data["dzongkhag_id"] = design_permit.dzongkhag_id
        serializer.validated_data["thromde_id"] = design_permit.thromde_id
        serializer.validated_data["gewog_id"] = design_permit.gewog_id
        serializer.validated_data["village_id"] = design_permit.village_id
        serializer.validated_data["nature"] = design_permit.nature
    if not getattr(permit, "serial_no", None):
        serializer.validated_data["serial_no"] = f"CASB{int(time.time() * 1000)}"
    if not getattr(permit, "user_id", None):
        serializer.validated_data["user_id"] = self.request.user.id
