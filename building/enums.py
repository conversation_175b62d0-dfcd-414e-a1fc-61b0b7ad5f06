from django.db import models
from django.utils.translation import gettext_lazy as _


class PermitStatus(models.TextChoices):
    DRAFT = "draft", _("Draft")
    INITIATED = "initiated", _("Initiated")
    REQUESTED = "requested", _("Requested")
    FORWARDED_TO_ROID = "forwarded_to_roid", _("Forwarded to ROID")
    FORWARDED_TO_DHS = "forwarded_to_dhs", _("Forwarded to DHS")
    FORWARDED_TO_BPC = "forwarded_to_bpc", _("Forwarded to BPC")
    FORWARDED_TO_TCB = "forwarded_to_tcb", _("Forwarded to TCB")
    ARCHITECT_ASSIGNED = "architect_assigned", _("Architect Assigned")
    RESUBMITTED = "resubmitted", _("Re-submitted")
    PENDING_CHANGE = "pending_change", _("Pending Change")
    PENDING_PAYMENT = "pending_payment", _("Pending Payment")
    APPROVED = "approved", _("Approved")
    REJECTED = "rejected", _("Rejected")
    CLOSED = "closed", _("Closed")
