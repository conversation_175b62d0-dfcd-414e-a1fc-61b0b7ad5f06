from django.template.loader import render_to_string
from cas_api.celery import app
from building.models import Permit
import os
from cas_api.services.mailer_service import send_mail
from common.models import TaskPool
from user.models import User
from django.shortcuts import get_object_or_404


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_requested(self, permit_id, user_id):
    try:
        user = get_object_or_404(User, pk=user_id)
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit_id}"
        html_content = render_to_string(
            "mailers/buildings/notify_requested.html",
            {"name": user.name, "serial_no": permit.serial_no, "url": url},
        )
        send_mail("New Task Assigned", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_rejected(self, permit_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = permit.user
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit_id}"
        html_content = render_to_string(
            "mailers/buildings/notify_rejected.html",
            {"name": user.name, "reason": permit.rejection_remarks, "url": url},
        )
        send_mail("Building permit is rejected", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_closed(self, permit_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = permit.user
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit_id}"
        html_content = render_to_string(
            "mailers/buildings/notify_closed.html",
            {"name": user.name, "reason": permit.rejection_remarks, "url": url},
        )
        send_mail("Building permit is closed", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_approved(self, permit_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = permit.user
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit_id}"
        html_content = render_to_string("mailers/buildings/notify_approved.html", {"name": user.name, "url": url})
        send_mail("Building permit is approved", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forwarded_to_roid(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit_id}"
        html_content = render_to_string(
            "mailers/buildings/notify_forwarded_to_roid.html",
            {"name": user.name, "serial_no": permit.serial_no, "url": url},
        )
        send_mail(
            "Building permit forwarded to ROID for approval",
            html_content,
            [user.email],
        )
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forwarded_to_dhs(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit_id}"
        html_content = render_to_string(
            "mailers/buildings/notify_forwarded_to_dhs.html",
            {"name": user.name, "serial_no": permit.serial_no, "url": url},
        )
        send_mail("Building permit forwarded to DHS for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_architect_assigned(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit_id}"
        html_content = render_to_string(
            "mailers/buildings/notify_architect_assigned.html",
            {"name": user.name, "serial_no": permit.serial_no, "url": url},
        )
        send_mail("Building permit is assigned to you for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_ar_approved(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit_id}"
        html_content = render_to_string(
            "mailers/buildings/notify_ar_approved.html",
            {"name": user.name, "serial_no": permit.serial_no, "url": url},
        )
        send_mail("Building permit is assigned to you for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_task_pool_approved(self, task_pool_id):
    try:
        task_pool = get_object_or_404(TaskPool, pk=task_pool_id)
        permit = task_pool.permit
        user = permit.user
        approved_by = task_pool.user.current_role.description
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit.id}"
        html_content = render_to_string(
            "mailers/buildings/notify_task_pool_approved.html",
            {
                "name": user.name,
                "approved_by": approved_by,
                "serial_no": permit.serial_no,
                "url": url,
            },
        )
        send_mail(f"Building permit is approved by '{approved_by}'", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_bpc(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit.id}"
        html_content = render_to_string(
            "mailers/buildings/notify_bpc.html",
            {"name": user.name, "serial_no": permit.serial_no, "url": url},
        )
        send_mail("Building permit is assigned to you for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_tcb(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit.id}"
        html_content = render_to_string(
            "mailers/buildings/notify_tcb.html",
            {"name": user.name, "serial_no": permit.serial_no, "url": url},
        )
        send_mail("Building permit is assigned to you for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_resubmitted(self, permit_id, user_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        user = get_object_or_404(User, pk=user_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-permit/{permit.id}"
        html_content = render_to_string(
            "mailers/buildings/notify_resubmitted.html",
            {"name": user.name, "serial_no": permit.serial_no, "url": url},
        )
        send_mail("Building permit is resubmitted for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task generate_payment_advice")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_payment(self, permit_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/design-approval/{permit.id}"
        html_content = render_to_string(
            "mailers/designs/notify_payment.html",
            {"name": permit.user.name, "reference": permit.serial_no, "url": url},
        )
        send_mail("Design permit payment is pending", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_payment")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_change(self, permit_id):
    try:
        permit = get_object_or_404(Permit, pk=permit_id)
        url = f"{os.getenv('HOST_URL', '')}/services/building-approval/{permit.id}"
        html_content = render_to_string(
            "mailers/buildings/notify_change.html",
            {"name": permit.user.name, "reference": permit.serial_no, "url": url},
        )
        send_mail("Building permit is changed for approval", html_content, [permit.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_change")
