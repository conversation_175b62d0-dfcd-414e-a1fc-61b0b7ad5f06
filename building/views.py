from rest_framework import generics, viewsets, response, status
from building.models import Permit
from building.serializers import PermitSerializer, PermitListSerializer
from common.models import DesignTeam
from common.serializers import StateLogSerializer
from building.helpers.view_helper import perform_save
from .populators import Populator
from django_fsm import can_proceed
from django.shortcuts import get_object_or_404
from rest_framework.decorators import action
from django_fsm_log.models import StateLog
from rest_framework.filters import SearchFilter
from django.db import transaction
from django_weasyprint import WeasyTemplateResponseMixin
from rest_framework.exceptions import ValidationError
from datetime import timedelta
from common.helpers import adm_user


class PermitView(generics.ListCreateAPIView):
    serializer_class = PermitSerializer
    filter_backends = [SearchFilter]
    search_fields = (
        "serial_no",
        "state",
        "user__first_name",
        "user__last_name",
        "user__email",
        "dzongkhag__name",
        "thromde__name",
        "gewog__name",
        "village__name",
    )

    def get_serializer_class(self):
        if self.request.method == "GET":
            return PermitListSerializer
        elif self.request.method == "POST":
            return PermitSerializer

    def get_queryset(self):
        return Populator(self.request.user, self.request.query_params).populate().distinct()

    def create(self, request, *args, **kwargs):
        data = request.data
        if data.get("state") == "draft":
            design_teams = data.pop("design_teams", [])
            permit_data = Permit(**data, user=request.user)
            permit = Permit.objects.bulk_create([permit_data])[0]
            instances = []
            for design_team in design_teams:
                instances.append(DesignTeam(teamable=permit, **design_team))
            DesignTeam.objects.bulk_create(instances)
            return response.Response(PermitSerializer(permit).data, status=status.HTTP_201_CREATED)
        return super().create(request, *args, **kwargs)

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                perform_save(self, serializer)
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class PermitPKView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Permit.objects.all()
    serializer_class = PermitSerializer

    def perform_update(self, serializer):
        with transaction.atomic():
            try:
                perform_save(self, serializer)
                super().perform_update(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        data = request.data

        if data.get("state") == "draft":
            design_teams_data = data.pop("design_teams", [])
            Permit.objects.filter(id=instance.id).update(**data)
            existing_design_teams = {a.id: a for a in instance.design_teams.filter(id__in=[a["id"] for a in design_teams_data if "id" in a])}
            design_teams_to_update = []
            design_teams_to_create = []
            for design_team_data in design_teams_data:
                applicant_id = design_team_data.get("id")
                if applicant_id and applicant_id in existing_design_teams:
                    applicant = existing_design_teams[applicant_id]
                    for field, value in design_team_data.items():
                        setattr(applicant, field, value)
                    design_teams_to_update.append(applicant)
                else:
                    design_teams_to_create.append(DesignTeam(teamable=instance, **design_team_data))
            if design_teams_to_create:
                DesignTeam.objects.bulk_create(design_teams_to_create)
            if design_teams_to_update:
                DesignTeam.objects.bulk_update(design_teams_to_update, fields=[f.name for f in DesignTeam._meta.fields if f.name != "id"])
            return response.Response(PermitSerializer(instance).data, status=status.HTTP_200_OK)
        return super().update(request, *args, **kwargs)


class StateMachineViewSet(viewsets.ModelViewSet):
    queryset = Permit.objects.all()
    serializer_class = PermitSerializer

    @action(detail=True, methods=["put"])
    def transition(self, request, *args, **kwargs):
        with transaction.atomic():
            try:
                instance = get_object_or_404(Permit, pk=kwargs.get("pk"))
                action = request.data.pop("action")
                if not hasattr(instance, action) or not can_proceed(getattr(instance, action)):
                    return response.Response({"error": "Invalid transition"}, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
                if getattr(instance, "can_" + action)(request.user):
                    if not action == "start" and instance.task_pools.filter(user=request.user, state="assigned").exists():
                        raise ValidationError({"error": "You need to start your task first."})
                    getattr(instance, action)(request.data, by=request.user)
                    instance.save()
                    serializer = PermitSerializer(instance)
                    return response.Response(serializer.data, status=status.HTTP_200_OK)
                else:
                    return response.Response({"error": "You are not permited to perform this action."}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class PermitViewSet(viewsets.ModelViewSet):
    queryset = Permit.objects.all()
    serializer_class = PermitSerializer

    @action(detail=False, methods=["get"])
    def activity_logs(self, request, *args, **kwargs):
        permit = get_object_or_404(Permit, pk=kwargs.get("pk"))
        logs = StateLog.objects.for_(permit)
        serializer = StateLogSerializer(logs, many=True)
        return response.Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"])
    def draft(self, request, *args, **kwargs):
        user = request.user
        permit = user.building_permits.filter(state="draft").first()
        if not permit:
            return response.Response({"error": "No draft permit found."}, status=status.HTTP_404_NOT_FOUND)
        serializer = PermitSerializer(permit)
        return response.Response(serializer.data, status=status.HTTP_200_OK)


class PdfView(WeasyTemplateResponseMixin, generics.RetrieveAPIView):
    queryset = Permit.objects.all()
    serializer_class = PermitSerializer
    template_name = "documents/pdfs/buildings/certificate.html"

    def get(self, request, *args, **kwargs):
        permit = get_object_or_404(Permit, pk=kwargs["permit_id"])
        if permit.state != "approved":
            raise ValidationError({"error": "Application not approved."})
        finyear = permit.created_at - timedelta(days=365)
        setting = adm_user(permit).settings.filter(key="seal").first()
        seal = setting.file.file.url if setting else None
        context = {"permit": permit, "user": permit.user, "finyear": finyear, "seal": seal}
        return self.render_to_response(context)
