"""
URL configuration for report app
"""
from django.urls import path
from report import views

urlpatterns = [
    # Dashboard endpoints
    path('dashboard/', views.DashboardView.as_view(), name='dashboard'),
    
    # Statistics endpoints
    path('statistics/construction/', views.ConstructionStatisticsView.as_view(), name='construction-statistics'),
    path('statistics/users/', views.UserStatisticsView.as_view(), name='user-statistics'),
    path('statistics/workload/', views.WorkloadStatisticsView.as_view(), name='workload-statistics'),
    path('statistics/tourism/', views.TourismStatisticsView.as_view(), name='tourism-statistics'),
    path('statistics/geographic/', views.GeographicStatisticsView.as_view(), name='geographic-statistics'),
    
    # Export endpoints
    path('export/', views.ExportView.as_view(), name='export-data'),
]
