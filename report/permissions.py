"""
Custom permissions for report module
"""
from rest_framework.permissions import BasePermission
from report.utils import get_role_based_permissions, can_export_data


class CanViewReports(BasePermission):
    """
    Permission to check if user can view reports
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        user_role = request.user.current_role.name if request.user.current_role else None
        if not user_role:
            return False
        
        # All authenticated users with roles can view basic reports
        return True


class CanViewUserStatistics(BasePermission):
    """
    Permission to check if user can view user statistics
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        user_role = request.user.current_role.name if request.user.current_role else None
        if not user_role:
            return False
        
        permissions = get_role_based_permissions(user_role)
        return permissions.get('can_view_user_stats', False)


class CanExportData(BasePermission):
    """
    Permission to check if user can export data
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        user_role = request.user.current_role.name if request.user.current_role else None
        if not user_role:
            return False
        
        export_type = request.data.get('export_type', 'excel') if request.method == 'POST' else 'excel'
        return can_export_data(user_role, export_type)


class CanViewWorkloadStatistics(BasePermission):
    """
    Permission to check if user can view workload statistics
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        user_role = request.user.current_role.name if request.user.current_role else None
        if not user_role:
            return False
        
        # Only chiefs and admins can view workload statistics
        chief_roles = ['dce', 'tce', 'roidce', 'dhsce', 'admin', 'dhsadm']
        return user_role in chief_roles


class CanViewTourismStatistics(BasePermission):
    """
    Permission to check if user can view tourism statistics
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        user_role = request.user.current_role.name if request.user.current_role else None
        if not user_role:
            return False
        
        # Tourism department, admins, and DHS can view tourism statistics
        tourism_roles = ['tcb', 'admin', 'dhsadm', 'dhsce']
        return user_role in tourism_roles


class CanViewAllData(BasePermission):
    """
    Permission to check if user can view all data across jurisdictions
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        user_role = request.user.current_role.name if request.user.current_role else None
        if not user_role:
            return False
        
        permissions = get_role_based_permissions(user_role)
        return permissions.get('can_view_all_data', False)


class HasModuleAccess(BasePermission):
    """
    Permission to check if user has access to specific modules
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        user_role = request.user.current_role.name if request.user.current_role else None
        if not user_role:
            return False
        
        # Get requested module from query params or request data
        requested_module = (
            request.query_params.get('module', 'all') if request.method == 'GET'
            else request.data.get('module', 'all')
        )
        
        permissions = get_role_based_permissions(user_role)
        accessible_modules = permissions.get('modules_access', [])
        
        # If user has access to all modules or the specific module
        return 'all' in accessible_modules or requested_module in accessible_modules


class IsChiefOrAdmin(BasePermission):
    """
    Permission to check if user is a chief or admin
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        user_role = request.user.current_role.name if request.user.current_role else None
        if not user_role:
            return False
        
        chief_admin_roles = [
            'admin', 'dhsadm', 'dhsce', 'dce', 'tce', 'roidce',
            'thrompon', 'dzongda'
        ]
        return user_role in chief_admin_roles


class CanViewGeographicData(BasePermission):
    """
    Permission to check if user can view geographic data based on their jurisdiction
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        user_role = request.user.current_role.name if request.user.current_role else None
        if not user_role:
            return False
        
        permissions = get_role_based_permissions(user_role)
        geographic_scope = permissions.get('geographic_scope', 'none')
        
        # Users with no geographic scope cannot view geographic data
        return geographic_scope != 'none'
