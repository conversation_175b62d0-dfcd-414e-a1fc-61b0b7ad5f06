# Report Module

This Django app provides comprehensive reporting and dashboard functionality for the Construction Approval System (CAS). It aggregates data from all modules and provides role-based access to statistics, dashboards, and data export capabilities.

## Features

### Dashboard & Statistics
- **Role-based dashboards** with customized widgets and charts
- **Construction statistics** across all modules (Planning, Design, Building, Technical, Ongoing Construction, Occupancy, Modification)
- **User statistics** (Admin only) showing user distribution by role and location
- **Geographic statistics** with jurisdiction-based filtering
- **Workload statistics** for Chiefs to monitor staff performance
- **Tourism-specific statistics** for Department of Tourism

### Data Export
- **Excel export** with multiple sheets for different modules
- **CSV export** for data analysis
- **PDF export** (basic implementation, can be enhanced)
- **Role-based export permissions**

### Role-based Access Control
The module implements comprehensive role-based access control:

#### Super Admin (`admin`)
- Full access to all data across all jurisdictions
- User statistics and management reports
- All export capabilities
- Complete dashboard with all widgets

#### Agency Admin (`dhsadm`)
- Full access to construction data
- User statistics within their agency
- All export capabilities
- Comprehensive dashboard

#### DHS Director/Chief SRB<PERSON> (`dhsce`)
- Full access to construction data
- Workload statistics for staff management
- Export capabilities
- Management dashboard

#### Thromde Thrompon/Executive Secretary (`thrompon`, `tce`)
- Data filtered by their Thromde jurisdiction
- Limited export capabilities
- Thromde-specific dashboard

#### Dzongda/Dzongkhag Chief Engineer (`dzongda`, `dce`)
- Data filtered by their Dzongkhag jurisdiction
- Limited export capabilities
- Dzongkhag-specific dashboard

#### Gup (`gup`)
- Data filtered by their Gewog jurisdiction
- Basic dashboard and statistics

#### Department of Tourism (`tcb`)
- Tourism/hotel-specific construction data
- Hotel statistics and star ratings
- Tourism-focused dashboard

#### Other Agencies (`bpc`, etc.)
- Module-specific access based on their domain
- Limited geographic scope

## API Endpoints

### Dashboard
- `GET /api/v1/reports/dashboard/` - Get role-based dashboard data
  - Query parameters: `start_date`, `end_date`, `dzongkhag_id`, `gewog_id`, `thromde_id`, `status`, `module`

### Statistics
- `GET /api/v1/reports/statistics/construction/` - Construction statistics across modules
- `GET /api/v1/reports/statistics/users/` - User statistics (Admin only)
- `GET /api/v1/reports/statistics/workload/` - Workload statistics (Chiefs only)
- `GET /api/v1/reports/statistics/tourism/` - Tourism statistics (TCB, Admin)
- `GET /api/v1/reports/statistics/geographic/` - Geographic distribution statistics

### Export
- `POST /api/v1/reports/export/` - Export data in various formats
  - Request body:
    ```json
    {
      "export_type": "excel|csv|pdf",
      "module": "all|planning|design|building|technical|ongoing_construction|occupancy|modification|government",
      "filters": {
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "dzongkhag_id": 1,
        "status": "approved"
      }
    }
    ```

## Technology Stack

- **Django Pandas** - For data aggregation and analysis
- **Pandas** - Data manipulation and analysis
- **OpenPyXL** - Excel file generation
- **WeasyPrint** - PDF generation (already available in the system)
- **Django REST Framework** - API endpoints
- **Role-based permissions** - Custom permission classes

## Installation

1. The app is already included in `INSTALLED_APPS` in settings.py
2. Install dependencies: `poetry install` (django-pandas, pandas, openpyxl are added to pyproject.toml)
3. The API endpoints are available at `/api/v1/reports/`

## Usage Examples

### Get Dashboard Data
```bash
curl -H "Authorization: Bearer <token>" \
  "http://localhost:8000/api/v1/reports/dashboard/?start_date=2024-01-01&end_date=2024-12-31"
```

### Export Construction Data
```bash
curl -X POST -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"export_type": "excel", "module": "building"}' \
  "http://localhost:8000/api/v1/reports/export/"
```

### Get Tourism Statistics
```bash
curl -H "Authorization: Bearer <token>" \
  "http://localhost:8000/api/v1/reports/statistics/tourism/"
```

## Data Sources

The report module aggregates data from:
- **Information Service** (`information.Inquiry`)
- **Planning** (`planning.Application`)
- **Design** (`design.Permit`)
- **Building** (`building.Permit`)
- **Technical** (`technical.Clearance`)
- **Ongoing Construction** (`ongoing_construction.Inspection`)
- **Occupancy** (`occupancy.Certificate`)
- **Modification** (`modification.Permit`)
- **Government Structures** (`government.Structure`)
- **Users** (`user.User`)

## Security

- All endpoints require authentication
- Role-based access control ensures users only see data within their jurisdiction
- Export capabilities are restricted based on user roles
- Geographic filtering prevents unauthorized access to data outside user's jurisdiction

## Testing

Run tests with:
```bash
python manage.py test report
```

The test suite covers:
- API endpoint permissions
- Role-based access control
- Data filtering
- Export functionality
- Utility functions

## Future Enhancements

- Real-time dashboard updates using WebSockets
- Advanced PDF report templates
- Scheduled report generation
- Email report delivery
- Advanced analytics and forecasting
- Interactive charts and visualizations
- Report caching for performance optimization
