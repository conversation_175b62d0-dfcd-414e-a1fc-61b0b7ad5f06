"""
Utility functions for report module
"""
from django.db.models import Q
from django.contrib.auth import get_user_model

User = get_user_model()


def get_role_based_permissions(user_role):
    """
    Get permissions and access levels based on user role
    """
    role_permissions = {
        # Super Admin - Full access
        'admin': {
            'can_view_all_data': True,
            'can_view_user_stats': True,
            'can_export_all': True,
            'geographic_scope': 'all',
            'modules_access': ['all']
        },
        
        # DHS Admin - Full access
        'dhsadm': {
            'can_view_all_data': True,
            'can_view_user_stats': True,
            'can_export_all': True,
            'geographic_scope': 'all',
            'modules_access': ['all']
        },
        
        # DHS Chief Engineer - Full access
        'dhsce': {
            'can_view_all_data': True,
            'can_view_user_stats': False,
            'can_export_all': True,
            'geographic_scope': 'all',
            'modules_access': ['all']
        },
        
        # Thromde Chief Engineer
        'tce': {
            'can_view_all_data': False,
            'can_view_user_stats': False,
            'can_export_all': False,
            'geographic_scope': 'thromde',
            'modules_access': ['planning', 'design', 'building', 'technical', 'ongoing_construction', 'occupancy', 'modification']
        },
        
        # Dzongkhag Chief Engineer
        'dce': {
            'can_view_all_data': False,
            'can_view_user_stats': False,
            'can_export_all': False,
            'geographic_scope': 'dzongkhag',
            'modules_access': ['planning', 'design', 'building', 'technical', 'ongoing_construction', 'occupancy', 'modification']
        },
        
        # ROID Chief Engineer
        'roidce': {
            'can_view_all_data': False,
            'can_view_user_stats': False,
            'can_export_all': False,
            'geographic_scope': 'region',
            'modules_access': ['planning', 'design', 'building', 'technical', 'ongoing_construction', 'occupancy', 'modification']
        },
        
        # Tourism Council of Bhutan
        'tcb': {
            'can_view_all_data': False,
            'can_view_user_stats': False,
            'can_export_all': False,
            'geographic_scope': 'all',
            'modules_access': ['building', 'occupancy']  # Focus on hotel/tourism constructions
        },
        
        # Thrompon/Executive Secretary
        'thrompon': {
            'can_view_all_data': False,
            'can_view_user_stats': False,
            'can_export_all': False,
            'geographic_scope': 'thromde',
            'modules_access': ['planning', 'design', 'building', 'occupancy']
        },
        
        # Dzongda
        'dzongda': {
            'can_view_all_data': False,
            'can_view_user_stats': False,
            'can_export_all': False,
            'geographic_scope': 'dzongkhag',
            'modules_access': ['planning', 'design', 'building', 'occupancy']
        },
        
        # Gup
        'gup': {
            'can_view_all_data': False,
            'can_view_user_stats': False,
            'can_export_all': False,
            'geographic_scope': 'gewog',
            'modules_access': ['planning', 'design', 'building', 'occupancy']
        },
        
        # Other agencies (BPC, etc.)
        'bpc': {
            'can_view_all_data': False,
            'can_view_user_stats': False,
            'can_export_all': False,
            'geographic_scope': 'all',
            'modules_access': ['technical', 'building']
        }
    }
    
    # Default permissions for roles not explicitly defined
    default_permissions = {
        'can_view_all_data': False,
        'can_view_user_stats': False,
        'can_export_all': False,
        'geographic_scope': 'none',
        'modules_access': []
    }
    
    return role_permissions.get(user_role, default_permissions)


def get_geographic_filter(user, scope=None):
    """
    Get geographic filter based on user profile and scope
    """
    profile = getattr(user, 'profile', None)
    if not profile:
        return Q()
    
    user_role = user.current_role.name if user.current_role else None
    permissions = get_role_based_permissions(user_role)
    geographic_scope = scope or permissions.get('geographic_scope', 'none')
    
    filters = Q()
    
    if geographic_scope == 'all':
        return Q()  # No filter - can see all data
    elif geographic_scope == 'region' and profile.dzongkhag and profile.dzongkhag.region:
        filters = Q(dzongkhag__region=profile.dzongkhag.region)
    elif geographic_scope == 'dzongkhag' and profile.dzongkhag:
        filters = Q(dzongkhag=profile.dzongkhag)
    elif geographic_scope == 'thromde' and profile.thromde:
        filters = Q(thromde=profile.thromde)
    elif geographic_scope == 'gewog' and profile.gewog:
        filters = Q(gewog=profile.gewog)
    
    return filters


def get_module_access(user_role):
    """
    Get list of modules the user role can access
    """
    permissions = get_role_based_permissions(user_role)
    return permissions.get('modules_access', [])


def can_export_data(user_role, export_type='excel'):
    """
    Check if user role can export data
    """
    permissions = get_role_based_permissions(user_role)
    
    if permissions.get('can_export_all', False):
        return True
    
    # Some roles might have limited export capabilities
    limited_export_roles = ['tce', 'dce', 'roidce', 'thrompon', 'dzongda']
    if user_role in limited_export_roles and export_type in ['excel', 'csv']:
        return True
    
    return False


def get_dashboard_layout(user_role):
    """
    Get dashboard layout configuration based on user role
    """
    layouts = {
        'admin': {
            'widgets': [
                'user_statistics',
                'construction_overview',
                'geographic_distribution',
                'module_statistics',
                'recent_activities'
            ],
            'charts': ['construction_trends', 'approval_rates', 'geographic_heatmap']
        },
        'dhsadm': {
            'widgets': [
                'user_statistics',
                'construction_overview',
                'module_statistics',
                'workload_distribution'
            ],
            'charts': ['construction_trends', 'approval_rates']
        },
        'dhsce': {
            'widgets': [
                'construction_overview',
                'module_statistics',
                'workload_distribution',
                'pending_approvals'
            ],
            'charts': ['construction_trends', 'approval_rates']
        },
        'tce': {
            'widgets': [
                'thromde_overview',
                'pending_approvals',
                'workload_distribution'
            ],
            'charts': ['monthly_trends', 'status_distribution']
        },
        'dce': {
            'widgets': [
                'dzongkhag_overview',
                'pending_approvals',
                'workload_distribution'
            ],
            'charts': ['monthly_trends', 'status_distribution']
        },
        'tcb': {
            'widgets': [
                'tourism_overview',
                'hotel_statistics',
                'star_rating_distribution'
            ],
            'charts': ['tourism_trends', 'hotel_approvals']
        }
    }
    
    # Default layout for roles not explicitly defined
    default_layout = {
        'widgets': ['basic_overview', 'pending_tasks'],
        'charts': ['status_distribution']
    }
    
    return layouts.get(user_role, default_layout)


def format_statistics_for_chart(data, chart_type='bar'):
    """
    Format statistics data for different chart types
    """
    if chart_type == 'pie':
        return [{'name': k, 'value': v} for k, v in data.items() if v > 0]
    elif chart_type == 'bar':
        return {
            'labels': list(data.keys()),
            'datasets': [{
                'data': list(data.values()),
                'backgroundColor': generate_colors(len(data))
            }]
        }
    elif chart_type == 'line':
        return {
            'labels': list(data.keys()),
            'datasets': [{
                'data': list(data.values()),
                'borderColor': '#3498db',
                'fill': False
            }]
        }
    
    return data


def generate_colors(count):
    """
    Generate color palette for charts
    """
    colors = [
        '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
        '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
    ]
    
    # Repeat colors if we need more than available
    return (colors * ((count // len(colors)) + 1))[:count]
