"""
Serializers for report data
"""

from rest_framework import serializers


class DashboardStatisticsSerializer(serializers.Serializer):
    """
    Serializer for dashboard statistics data
    """

    total = serializers.IntegerField()
    by_status = serializers.DictField(required=False, default=dict)
    by_dzongkhag = serializers.DictField(required=False, default=dict)


class ModuleStatisticsSerializer(serializers.Serializer):
    """
    Serializer for individual module statistics
    """

    information = DashboardStatisticsSerializer(required=False)
    planning = DashboardStatisticsSerializer(required=False)
    design = DashboardStatisticsSerializer(required=False)
    building = DashboardStatisticsSerializer(required=False)
    technical = DashboardStatisticsSerializer(required=False)
    ongoing_construction = DashboardStatisticsSerializer(required=False)
    occupancy = DashboardStatisticsSerializer(required=False)
    modification = DashboardStatisticsSerializer(required=False)
    government = DashboardStatisticsSerializer(required=False)


class UserStatisticsSerializer(serializers.Serializer):
    """
    Serializer for user statistics data
    """

    total_users = serializers.IntegerField()
    by_role = serializers.DictField()
    by_dzongkhag = serializers.DictField()
    by_thromde = serializers.DictField()
    active_users = serializers.IntegerField()
    inactive_users = serializers.IntegerField()


class SummaryStatisticsSerializer(serializers.Serializer):
    """
    Serializer for summary statistics
    """

    total_applications = serializers.IntegerField()
    approved_applications = serializers.IntegerField()
    pending_applications = serializers.IntegerField()
    approval_rate = serializers.FloatField()


class DashboardDataSerializer(serializers.Serializer):
    """
    Main serializer for dashboard data
    """

    construction_statistics = ModuleStatisticsSerializer()
    user_statistics = UserStatisticsSerializer(required=False)
    summary = SummaryStatisticsSerializer()


class ReportFilterSerializer(serializers.Serializer):
    """
    Serializer for report filters
    """

    start_date = serializers.DateField(required=False)
    end_date = serializers.DateField(required=False)
    dzongkhag_id = serializers.IntegerField(required=False)
    gewog_id = serializers.IntegerField(required=False)
    thromde_id = serializers.IntegerField(required=False)
    village_id = serializers.IntegerField(required=False)
    status = serializers.CharField(required=False)
    module = serializers.ChoiceField(
        choices=[
            ("all", "All Modules"),
            ("information", "Information Service"),
            ("planning", "Planning"),
            ("design", "Design"),
            ("building", "Building"),
            ("technical", "Technical"),
            ("ongoing_construction", "Ongoing Construction"),
            ("occupancy", "Occupancy"),
            ("modification", "Modification"),
            ("government", "Government"),
        ],
        required=False,
        default="all",
    )


class ExportRequestSerializer(serializers.Serializer):
    """
    Serializer for export requests
    """

    export_type = serializers.ChoiceField(
        choices=[
            ("excel", "Excel"),
            ("csv", "CSV"),
            ("pdf", "PDF"),
        ],
        default="excel",
    )
    module = serializers.ChoiceField(
        choices=[
            ("all", "All Modules"),
            ("information", "Information Service"),
            ("planning", "Planning"),
            ("design", "Design"),
            ("building", "Building"),
            ("technical", "Technical"),
            ("ongoing_construction", "Ongoing Construction"),
            ("occupancy", "Occupancy"),
            ("modification", "Modification"),
            ("government", "Government"),
        ],
        default="all",
    )
    filters = ReportFilterSerializer(required=False)


class WorkloadStatisticsSerializer(serializers.Serializer):
    """
    Serializer for workload statistics (for Chiefs)
    """

    user_name = serializers.CharField()
    role = serializers.CharField()
    pending_tasks = serializers.IntegerField()
    completed_tasks = serializers.IntegerField()
    average_tat = serializers.FloatField()  # Turn Around Time
    workload_percentage = serializers.FloatField()


class TourismStatisticsSerializer(serializers.Serializer):
    """
    Serializer for tourism-specific statistics
    """

    total_hotels = serializers.IntegerField()
    by_status = serializers.DictField()
    by_dzongkhag = serializers.DictField()
    by_star_rating = serializers.DictField()
    occupancy_statistics = serializers.DictField(required=False)
    monthly_trends = serializers.DictField(required=False)
    summary = serializers.DictField(required=False)


class GeographicStatisticsSerializer(serializers.Serializer):
    """
    Serializer for geographic-based statistics
    """

    region_name = serializers.CharField()
    dzongkhag_name = serializers.CharField()
    total_constructions = serializers.IntegerField()
    approved_constructions = serializers.IntegerField()
    pending_constructions = serializers.IntegerField()
    rejected_constructions = serializers.IntegerField()
