"""
Report views for dashboard and export functionality
"""

import tempfile
from datetime import datetime
from django.http import FileResponse, HttpResponse
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from user.models import User
from report.services import ReportDataService, ExportService
from report.serializers import DashboardDataSerializer, ReportFilterSerializer, ExportRequestSerializer, WorkloadStatisticsSerializer, TourismStatisticsSerializer
from django.template.loader import render_to_string
from planning.models import Application
from building.models import Permit as BuildingPermit
from design.models import Permit as DesignPermit
from occupancy.models import Certificate
from technical.models import Clearance


class DashboardView(APIView):
    """
    API view for dashboard data based on user role
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get dashboard data for the current user
        """
        # Parse filters from query parameters
        filter_serializer = ReportFilterSerializer(data=request.query_params)
        if not filter_serializer.is_valid():
            return Response(filter_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        filters = filter_serializer.validated_data

        # Get dashboard data
        data_service = ReportDataService(request.user, filters)
        dashboard_data = data_service.get_dashboard_data()

        # Serialize and return data
        serializer = DashboardDataSerializer(dashboard_data)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ConstructionStatisticsView(APIView):
    """
    API view for construction statistics
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get construction statistics across all modules
        """
        filter_serializer = ReportFilterSerializer(data=request.query_params)
        if not filter_serializer.is_valid():
            return Response(filter_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        filters = filter_serializer.validated_data
        data_service = ReportDataService(request.user, filters)
        statistics = data_service.get_construction_statistics()

        return Response(statistics, status=status.HTTP_200_OK)


class UserStatisticsView(APIView):
    """
    API view for user statistics (Admin only)
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get user statistics
        """
        # Check if user has permission to view user statistics
        if not request.user.current_role or request.user.current_role.name not in ["admin", "dhsadm"]:
            return Response({"error": "Insufficient permissions to view user statistics"}, status=status.HTTP_403_FORBIDDEN)

        data_service = ReportDataService(request.user)
        statistics = data_service.get_user_statistics()

        return Response(statistics, status=status.HTTP_200_OK)


class ExportView(APIView):
    """
    API view for exporting data
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Export data based on request parameters
        """
        serializer = ExportRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        export_data = serializer.validated_data
        export_type = export_data.get("export_type", "excel")
        module = export_data.get("module", "all")
        filters = export_data.get("filters", {})

        # Create export service
        export_service = ExportService(request.user, export_type)

        try:
            # Get data for export
            dataframes = export_service.export_construction_data(module)

            if not dataframes:
                return Response({"error": "No data available for export"}, status=status.HTTP_404_NOT_FOUND)

            # Generate file
            if export_type == "excel":
                return self._export_excel(dataframes, module)
            elif export_type == "csv":
                return self._export_csv(dataframes, module)
            elif export_type == "pdf":
                return self._export_pdf(dataframes, module)

        except Exception as e:
            return Response({"error": f"Export failed: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _export_excel(self, dataframes, module):
        """
        Export data to Excel format
        """
        # Create workbook without pandas
        from openpyxl import Workbook

        wb = Workbook()
        wb.remove(wb.active)  # Remove default sheet

        for sheet_name, data in dataframes.items():
            ws = wb.create_sheet(title=sheet_name)

            if data:
                # Get headers from first row
                headers = list(data[0].keys()) if data else []
                ws.append(headers)

                # Add data rows
                for row in data:
                    ws.append([str(row.get(header, "")) for header in headers])

        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as tmp_file:
            wb.save(tmp_file.name)

            # Return file response
            response = FileResponse(open(tmp_file.name, "rb"), as_attachment=True, filename=f'construction_report_{module}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
            response["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            return response

    def _export_csv(self, dataframes, module):
        """
        Export data to CSV format (first dataframe only)
        """
        import csv
        import io

        # Get first dataset
        data = list(dataframes.values())[0] if dataframes else []

        if not data:
            return Response({"error": "No data to export"}, status=status.HTTP_404_NOT_FOUND)

        # Create CSV content
        output = io.StringIO()

        if data:
            headers = list(data[0].keys())
            writer = csv.DictWriter(output, fieldnames=headers)
            writer.writeheader()
            writer.writerows(data)

        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".csv", mode="w", encoding="utf-8") as tmp_file:
            tmp_file.write(output.getvalue())
            tmp_file.flush()

            # Return file response
            response = FileResponse(open(tmp_file.name, "rb"), as_attachment=True, filename=f'construction_report_{module}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv')
            response["Content-Type"] = "text/csv"
            return response

    def _export_pdf(self, dataframes, module):
        """
        Export data to PDF format using Django template
        """
        # Convert data to structured format for template
        tables = {}
        for name, data in dataframes.items():
            if data:
                headers = list(data[0].keys()) if data else []

                # Format table name and headers for display
                formatted_name = name.replace("_", " ").title()
                formatted_headers = [header.replace("_", " ").title() for header in headers]

                # Format rows with CSS classes for better PDF layout
                formatted_rows = []
                for row in data:
                    formatted_row = []
                    for header in headers:
                        cell_value = str(row.get(header, ""))

                        # Format content for readable PDF display
                        if "serial" in header.lower():
                            # Break long serial numbers into chunks for better wrapping
                            if len(cell_value) > 15:
                                # Insert soft breaks every 15 characters
                                formatted_serial = ""
                                for i in range(0, len(cell_value), 15):
                                    chunk = cell_value[i : i + 15]
                                    formatted_serial += chunk
                                    if i + 15 < len(cell_value):
                                        formatted_serial += "<br/>"
                                cell_value = f'<span class="serial-cell">{formatted_serial}</span>'
                        elif "created_at" in header.lower() or "date" in header.lower():
                            # Format dates readably
                            if "T" in cell_value:
                                # Split datetime and format readably
                                date_part = cell_value.split("T")[0]
                                time_part = cell_value.split("T")[1].split(".")[0][:5] if "T" in cell_value else ""  # Only HH:MM
                                cell_value = f'<span class="date-cell">{date_part}<br/>{time_part}</span>'
                            else:
                                cell_value = f'<span class="date-cell">{cell_value}</span>'
                        elif len(cell_value) > 20:
                            # Break long text into smaller chunks with line breaks
                            if len(cell_value) > 40:
                                # For very long text, truncate and add ellipsis
                                cell_value = cell_value[:37] + "..."
                            cell_value = f'<span class="long-text">{cell_value}</span>'

                        formatted_row.append(cell_value)
                    formatted_rows.append(formatted_row)

                tables[formatted_name] = {"headers": formatted_headers, "rows": formatted_rows}

        # Get filters from request if available
        filters = {}
        if hasattr(self.request, "data") and "filters" in self.request.data:
            filters = self.request.data["filters"]

        # Calculate summary statistics
        raw_summary_stats = {
            "total_records": sum(len(data) for data in dataframes.values()),
            "modules_included": len(dataframes),
            "data_sources": len([name for name, data in dataframes.items() if data]),
        }

        # Format summary stats keys for display
        summary_stats = {}
        for key, value in raw_summary_stats.items():
            formatted_key = key.replace("_", " ").title()
            summary_stats[formatted_key] = value

        # Create context for template
        context = {
            "tables": tables,
            "module": module.title(),
            "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "user": self.request.user.get_full_name() or self.request.user.username,
            "total_records": raw_summary_stats["total_records"],
            "filters": filters,
            "summary_stats": summary_stats,
        }

        # Render HTML from template
        html_content = render_to_string("documents/pdfs/report/report_export.html", context)

        # Try to use WeasyPrint if available, otherwise return HTML
        try:
            from weasyprint import HTML, CSS

            # Create PDF
            pdf_file = HTML(string=html_content).write_pdf()

            # Return PDF response
            response = HttpResponse(pdf_file, content_type="application/pdf")
            response["Content-Disposition"] = f'attachment; filename="construction_report_{module}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf"'
            return response

        except ImportError:
            # WeasyPrint not available, return HTML
            response = HttpResponse(html_content, content_type="text/html")
            response["Content-Disposition"] = f'attachment; filename="construction_report_{module}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html"'
            return response


class WorkloadStatisticsView(APIView):
    """
    API view for workload statistics (for Chiefs)
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get workload statistics for staff under the current user
        """
        # Check if user is a chief/admin
        user_role = request.user.current_role.name if request.user.current_role else None
        chief_roles = ["dce", "tce", "roidce", "dhsce", "admin", "dhsadm"]

        if user_role not in chief_roles:
            return Response({"error": "Insufficient permissions to view workload statistics"}, status=status.HTTP_403_FORBIDDEN)

        # Get workload data (this would need to be implemented based on task_pools)
        workload_data = self._get_workload_data(request.user)

        serializer = WorkloadStatisticsSerializer(workload_data, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def _get_workload_data(self, user):
        """
        Calculate workload statistics for staff
        """
        # Calculate workload statistics for staff based on task_pools
        from common.models import TaskPool
        from django.db.models import Count, Avg, Q
        from django.utils import timezone
        from datetime import timedelta

        # Get user's jurisdiction for filtering staff
        profile = getattr(user, "profile", None)
        if not profile:
            return []

        # Define staff roles based on user's role
        user_role = user.current_role.name if user.current_role else None
        staff_roles = []

        if user_role in ["dce", "dzongda"]:
            staff_roles = ["dar", "dee", "dme", "dse", "dwse", "dbi", "dro"]
        elif user_role in ["tce", "thrompon"]:
            staff_roles = ["tar", "tee", "tme", "tse", "twse", "tbi", "hoi"]
        elif user_role in ["roidce"]:
            staff_roles = ["roidar", "roidee", "roidme", "roidse", "roidwse"]
        elif user_role in ["dhsce", "dhsadm"]:
            staff_roles = ["dce", "tce", "roidce", "dhsar", "dhsee", "dhsme", "dhswse"]
        else:
            staff_roles = ["user"]

        workload_data = []

        if user_role == "admin":
            staff_filter = Q()
        else:
            staff_filter = Q(current_role__name__in=staff_roles)

        # Apply jurisdiction filter
        if user_role in ["dce", "dzongda"] and profile.dzongkhag:
            staff_filter &= Q(profile__dzongkhag=profile.dzongkhag)
        elif user_role in ["tce", "thrompon"] and profile.thromde:
            staff_filter &= Q(profile__thromde=profile.thromde)
        elif user_role == "roidce" and profile.region:
            staff_filter &= Q(profile__region=profile.region)
        elif user_role == "dhsce":
            staff_filter &= Q()
        elif user_role == "gup" and profile.gewog:
            staff_filter &= Q(profile__gewog=profile.gewog)
        else:
            staff_filter &= Q(id=user.id)

        staff_users = User.objects.filter(staff_filter).select_related("current_role", "profile")

        for staff_user in staff_users:
            # Get task pool statistics for this user
            pending_tasks = TaskPool.objects.filter(user=staff_user, state="pending").count()

            completed_tasks = TaskPool.objects.filter(user=staff_user, state="completed", updated_at__gte=timezone.now() - timedelta(days=30)).count()  # Last 30 days

            # Calculate average TAT (Turn Around Time) in days
            completed_with_tat = (
                TaskPool.objects.filter(user=staff_user, state="completed", updated_at__gte=timezone.now() - timedelta(days=30))
                .exclude(created_at__isnull=True)
                .exclude(updated_at__isnull=True)
            )

            avg_tat = 0
            if completed_with_tat.exists():
                total_tat = sum([(task.updated_at - task.created_at).days for task in completed_with_tat if task.updated_at and task.created_at])
                avg_tat = total_tat / completed_with_tat.count() if completed_with_tat.count() > 0 else 0

            # Calculate workload percentage (pending vs capacity)
            total_tasks = pending_tasks + completed_tasks
            workload_percentage = min((pending_tasks / 10) * 100, 100) if pending_tasks > 0 else 0  # Assuming 10 is max capacity

            workload_data.append(
                {
                    "user_name": f"{staff_user.first_name} {staff_user.last_name}".strip() or staff_user.username,
                    "role": staff_user.current_role.name if staff_user.current_role else "Unknown",
                    "pending_tasks": pending_tasks,
                    "completed_tasks": completed_tasks,
                    "average_tat": round(avg_tat, 1),
                    "workload_percentage": round(workload_percentage, 1),
                }
            )

        return workload_data


class TourismStatisticsView(APIView):
    """
    API view for tourism-specific statistics (for Department of Tourism)
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get tourism/hotel construction statistics
        """
        # Check if user is from tourism department
        user_role = request.user.current_role.name if request.user.current_role else None

        if user_role not in ["tcb", "admin", "dhsadm"]:
            return Response({"error": "Insufficient permissions to view tourism statistics"}, status=status.HTTP_403_FORBIDDEN)

        # Get tourism-specific data
        tourism_data = self._get_tourism_data(request.user)

        serializer = TourismStatisticsSerializer(tourism_data)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def _get_tourism_data(self, user):
        """
        Get tourism/hotel specific statistics
        """
        from building.models import Permit as BuildingPermit
        from occupancy.models import Certificate
        from django.db.models import Count, Q
        from report.services import ReportDataService

        # Get jurisdiction filter for the user
        data_service = ReportDataService(user)
        jurisdiction_filter = data_service.get_user_jurisdiction_filter()

        # Filter for hotel/tourism related constructions
        hotel_filter = (
            Q(building_type__name__icontains="hotel")
            | Q(use__name__icontains="hotel")
            | Q(use__name__icontains="tourism")
            | Q(use__name__icontains="resort")
            | Q(use__name__icontains="guest")
            | Q(building_type__name__icontains="resort")
            | Q(building_type__name__icontains="guest")
        )

        hotel_buildings = BuildingPermit.objects.filter(hotel_filter & jurisdiction_filter)

        # Get occupancy certificates for hotels
        hotel_occupancy = Certificate.objects.filter(permit__in=hotel_buildings).filter(jurisdiction_filter)

        # Calculate statistics
        total_hotels = hotel_buildings.count()

        # Status distribution
        status_stats = dict(hotel_buildings.values("state").annotate(count=Count("id")).values_list("state", "count"))

        # Geographic distribution
        dzongkhag_stats = dict(hotel_buildings.values("dzongkhag__name").annotate(count=Count("id")).values_list("dzongkhag__name", "count"))

        # Star rating distribution (if available in the model)
        star_stats = {}
        try:
            # Check if star field exists
            if hasattr(BuildingPermit, "star_rating"):
                star_stats = dict(hotel_buildings.values("star_rating").annotate(count=Count("id")).values_list("star_rating", "count"))
            elif hasattr(BuildingPermit, "star"):
                star_stats = dict(hotel_buildings.values("star").annotate(count=Count("id")).values_list("star", "count"))
            else:
                # If no star rating field, create categories based on floors or units
                star_stats = {
                    "Budget (1-2 floors)": hotel_buildings.filter(no_of_floors__lte=2).count(),
                    "Standard (3-5 floors)": hotel_buildings.filter(no_of_floors__gte=3, no_of_floors__lte=5).count(),
                    "Premium (6+ floors)": hotel_buildings.filter(no_of_floors__gte=6).count(),
                }
        except Exception:
            star_stats = {"Not Available": total_hotels}

        # Occupancy statistics
        occupancy_stats = {
            "total_occupancy_certificates": hotel_occupancy.count(),
            "issued_certificates": hotel_occupancy.filter(state="approved").count(),
            "pending_certificates": hotel_occupancy.filter(state="pending").count(),
        }

        # Monthly trends (last 12 months)
        from django.utils import timezone
        from datetime import timedelta
        import calendar

        monthly_stats = {}
        current_date = timezone.now()

        for i in range(12):
            month_start = current_date.replace(day=1) - timedelta(days=30 * i)
            month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])

            month_count = hotel_buildings.filter(created_at__gte=month_start, created_at__lte=month_end).count()

            month_name = month_start.strftime("%B %Y")
            monthly_stats[month_name] = month_count

        return {
            "total_hotels": total_hotels,
            "by_status": status_stats,
            "by_dzongkhag": dzongkhag_stats,
            "by_star_rating": star_stats,
            "occupancy_statistics": occupancy_stats,
            "monthly_trends": monthly_stats,
            "summary": {
                "approved_hotels": status_stats.get("approved", 0),
                "pending_hotels": status_stats.get("pending", 0),
                "rejected_hotels": status_stats.get("rejected", 0),
                "completion_rate": round((occupancy_stats["issued_certificates"] / total_hotels * 100) if total_hotels > 0 else 0, 2),
            },
        }


class GeographicStatisticsView(APIView):
    """
    API view for geographic-based statistics
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get statistics grouped by geographic regions
        """
        filter_serializer = ReportFilterSerializer(data=request.query_params)
        if not filter_serializer.is_valid():
            return Response(filter_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        filters = filter_serializer.validated_data
        data_service = ReportDataService(request.user, filters)

        # Get geographic statistics
        geographic_data = self._get_geographic_statistics(data_service)

        return Response(geographic_data, status=status.HTTP_200_OK)

    def _get_geographic_statistics(self, data_service):
        """
        Calculate statistics by geographic regions
        """
        from address.models import Dzongkhag, Region
        from django.db.models import Count, Q

        jurisdiction_filter = data_service.get_user_jurisdiction_filter()

        # Get statistics by dzongkhag
        dzongkhag_stats = []

        # Get dzongkhags based on user's jurisdiction
        user_role = data_service.user.current_role.name if data_service.user.current_role else None
        profile = getattr(data_service.user, "profile", None)

        if user_role in ["admin", "dhsadm", "dhsce"]:
            dzongkhags = Dzongkhag.objects.all()
        elif user_role in ["dce", "dzongda"] and profile and profile.dzongkhag:
            dzongkhags = Dzongkhag.objects.filter(id=profile.dzongkhag.id)
        elif user_role in ["tce", "thrompon"] and profile and profile.thromde:
            dzongkhags = Dzongkhag.objects.filter(id=profile.thromde.dzongkhag.id)
        elif user_role == "roidce" and profile and profile.region:
            dzongkhags = Dzongkhag.objects.filter(region=profile.region)
        else:
            dzongkhags = Dzongkhag.objects.none()

        for dzongkhag in dzongkhags:
            dzongkhag_filter = Q(dzongkhag=dzongkhag)

            # Count constructions in this dzongkhag across all modules
            planning_total = Application.objects.filter(dzongkhag_filter & jurisdiction_filter).count()
            design_total = DesignPermit.objects.filter(dzongkhag_filter & jurisdiction_filter).count()
            building_total = BuildingPermit.objects.filter(dzongkhag_filter & jurisdiction_filter).count()
            technical_total = Clearance.objects.filter(dzongkhag_filter & jurisdiction_filter).count()

            # For occupancy, filter by permit's dzongkhag
            occupancy_total = Certificate.objects.filter(permit__dzongkhag=dzongkhag).count()

            total_constructions = planning_total + design_total + building_total + technical_total + occupancy_total

            # Count approved constructions
            planning_approved = Application.objects.filter(dzongkhag_filter & jurisdiction_filter, state="approved").count()
            design_approved = DesignPermit.objects.filter(dzongkhag_filter & jurisdiction_filter, state="approved").count()
            building_approved = BuildingPermit.objects.filter(dzongkhag_filter & jurisdiction_filter, state="approved").count()
            technical_approved = Clearance.objects.filter(dzongkhag_filter & jurisdiction_filter, state="approved").count()
            occupancy_approved = Certificate.objects.filter(permit__dzongkhag=dzongkhag, state="approved").count()

            approved_constructions = planning_approved + design_approved + building_approved + technical_approved + occupancy_approved

            # Count rejected constructions
            planning_rejected = Application.objects.filter(dzongkhag_filter & jurisdiction_filter, state="rejected").count()
            design_rejected = DesignPermit.objects.filter(dzongkhag_filter & jurisdiction_filter, state="rejected").count()
            building_rejected = BuildingPermit.objects.filter(dzongkhag_filter & jurisdiction_filter, state="rejected").count()
            technical_rejected = Clearance.objects.filter(dzongkhag_filter & jurisdiction_filter, state="rejected").count()
            occupancy_rejected = Certificate.objects.filter(permit__dzongkhag=dzongkhag, state="rejected").count()

            rejected_constructions = planning_rejected + design_rejected + building_rejected + technical_rejected + occupancy_rejected

            # Calculate pending (total - approved - rejected)
            pending_constructions = total_constructions - approved_constructions - rejected_constructions

            # Calculate approval rate
            approval_rate = round((approved_constructions / total_constructions * 100) if total_constructions > 0 else 0, 2)

            dzongkhag_stats.append(
                {
                    "region_name": dzongkhag.region.name if dzongkhag.region else "Unknown",
                    "dzongkhag_name": dzongkhag.name,
                    "total_constructions": total_constructions,
                    "approved_constructions": approved_constructions,
                    "pending_constructions": pending_constructions,
                    "rejected_constructions": rejected_constructions,
                    "approval_rate": approval_rate,
                    "module_breakdown": {
                        "planning": planning_total,
                        "design": design_total,
                        "building": building_total,
                        "technical": technical_total,
                        "occupancy": occupancy_total,
                    },
                    "status_breakdown": {"approved": approved_constructions, "pending": pending_constructions, "rejected": rejected_constructions},
                }
            )

        # Sort by total constructions (descending)
        dzongkhag_stats.sort(key=lambda x: x["total_constructions"], reverse=True)

        return dzongkhag_stats
