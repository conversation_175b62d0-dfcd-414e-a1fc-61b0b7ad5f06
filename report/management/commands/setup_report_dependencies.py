"""
Management command to setup report module dependencies
"""
from django.core.management.base import BaseCommand
import subprocess
import sys


class Command(BaseCommand):
    help = 'Setup report module dependencies (django-pandas, pandas, openpyxl)'

    def handle(self, *args, **options):
        """
        Install required dependencies for the report module
        """
        self.stdout.write(self.style.SUCCESS('Setting up report module dependencies...'))
        
        try:
            # Install dependencies using poetry
            self.stdout.write('Installing django-pandas, pandas, and openpyxl...')
            subprocess.check_call([
                'poetry', 'add', 
                'django-pandas==0.6.6',
                'pandas>=2.0.0',
                'openpyxl>=3.1.0'
            ])
            
            self.stdout.write(self.style.SUCCESS('Dependencies installed successfully!'))
            self.stdout.write(self.style.WARNING(
                'Please uncomment the pandas imports in report/services.py and report/views.py '
                'to enable full functionality.'
            ))
            
        except subprocess.CalledProcessError as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to install dependencies: {e}')
            )
            self.stdout.write(
                self.style.WARNING(
                    'You can manually install dependencies by running:\n'
                    'poetry add django-pandas pandas openpyxl'
                )
            )
        except FileNotFoundError:
            self.stdout.write(
                self.style.ERROR('Poetry not found. Please install poetry first.')
            )
            self.stdout.write(
                self.style.WARNING(
                    'Alternative: Install dependencies manually:\n'
                    'pip install django-pandas pandas openpyxl'
                )
            )
