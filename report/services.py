"""
Report services using django-pandas for data aggregation and analysis
"""

from django.db.models import Count, Q
from planning.models import Application
from design.models import Permit as DesignPermit
from building.models import Permit as BuildingPermit
from technical.models import Clearance
from ongoing_construction.models import Inspection
from occupancy.models import Certificate
from modification.models import Permit as ModificationPermit
from government.models import Structure
from information.models import Inquiry
from user.models import User


class ReportDataService:
    """
    Service class for aggregating data from all modules using django-pandas
    """

    def __init__(self, user, filters=None):
        self.user = user
        self.filters = filters or {}
        self.user_role = user.current_role.name if user.current_role else None

    def get_user_jurisdiction_filter(self):
        """
        Get jurisdiction filter based on user role and profile
        """
        profile = getattr(self.user, "profile", None)
        if not profile:
            return Q()

        filters = Q()

        # Apply jurisdiction filters based on user role
        if self.user_role in ["admin"]:
            return Q()
        elif self.user_role in ["dhsadm", "dhsce"]:
            return Q()
        elif self.user_role in ["thrompon", "tce"] and profile.thromde:
            filters |= Q(thromde=profile.thromde)
        elif self.user_role in ["dzongda", "dce"] and profile.dzongkhag:
            filters |= Q(dzongkhag=profile.dzongkhag)
        elif self.user_role in ["gup"] and profile.gewog:
            filters |= Q(gewog=profile.gewog)
        elif self.user_role == "roidce" and profile.region:
            filters |= Q(region=profile.region)
        return filters

    def _adapt_filter_for_ongoing_construction(self, jurisdiction_filter):
        """
        Adapt jurisdiction filter for ongoing construction (Inspection model)
        which accesses location through permit relationship
        """
        if not jurisdiction_filter:
            return Q()

        # Convert direct field references to permit__ prefixed ones
        adapted_filter = Q()
        for child in jurisdiction_filter.children:
            if isinstance(child, tuple) and len(child) == 2:
                field, value = child
                if field == "region":
                    adapted_filter |= Q(permit__region=value)
                elif field == "dzongkhag":
                    adapted_filter |= Q(permit__dzongkhag=value)
                elif field == "thromde":
                    adapted_filter |= Q(permit__thromde=value)
                elif field == "gewog":
                    adapted_filter |= Q(permit__gewog=value)
                elif field == "village":
                    adapted_filter |= Q(permit__village=value)
                else:
                    adapted_filter |= Q(**{field: value})
        return adapted_filter

    def _adapt_filter_for_occupancy(self, jurisdiction_filter):
        """
        Adapt jurisdiction filter for occupancy (Certificate model)
        which accesses location through permit relationship
        """
        if not jurisdiction_filter:
            return Q()

        # Convert direct field references to permit__ prefixed ones
        adapted_filter = Q()
        for child in jurisdiction_filter.children:
            if isinstance(child, tuple) and len(child) == 2:
                field, value = child
                if field == "dzongkhag":
                    adapted_filter |= Q(permit__dzongkhag=value)
                elif field == "thromde":
                    adapted_filter |= Q(permit__thromde=value)
                elif field == "gewog":
                    adapted_filter |= Q(permit__gewog=value)
                elif field == "village":
                    adapted_filter |= Q(permit__village=value)
                else:
                    adapted_filter |= Q(**{field: value})
        return adapted_filter

    def get_construction_statistics(self):
        """
        Get overall construction statistics across all modules
        """
        jurisdiction_filter = self.get_user_jurisdiction_filter()

        # Apply additional filters from request
        date_filter = Q()
        if self.filters.get("start_date"):
            date_filter &= Q(created_at__gte=self.filters["start_date"])
        if self.filters.get("end_date"):
            date_filter &= Q(created_at__lte=self.filters["end_date"])

        stats = {}

        # Information Service Statistics
        info_qs = Inquiry.objects.filter(jurisdiction_filter & date_filter)
        stats["information"] = {
            "total": info_qs.count(),
            "by_status": dict(info_qs.values("state").annotate(count=Count("id")).values_list("state", "count")),
            "by_dzongkhag": dict(info_qs.values("dzongkhag__name").annotate(count=Count("id")).values_list("dzongkhag__name", "count")),
        }

        # Planning Statistics
        planning_qs = Application.objects.filter(jurisdiction_filter & date_filter)
        stats["planning"] = {
            "total": planning_qs.count(),
            "by_status": dict(planning_qs.values("state").annotate(count=Count("id")).values_list("state", "count")),
            "by_dzongkhag": dict(planning_qs.values("dzongkhag__name").annotate(count=Count("id")).values_list("dzongkhag__name", "count")),
        }

        # Design Statistics
        design_qs = DesignPermit.objects.filter(jurisdiction_filter & date_filter)
        stats["design"] = {
            "total": design_qs.count(),
            "by_status": dict(design_qs.values("state").annotate(count=Count("id")).values_list("state", "count")),
            "by_dzongkhag": dict(design_qs.values("dzongkhag__name").annotate(count=Count("id")).values_list("dzongkhag__name", "count")),
        }

        # Building Statistics
        building_qs = BuildingPermit.objects.filter(jurisdiction_filter & date_filter)
        stats["building"] = {
            "total": building_qs.count(),
            "by_status": dict(building_qs.values("state").annotate(count=Count("id")).values_list("state", "count")),
            "by_dzongkhag": dict(building_qs.values("dzongkhag__name").annotate(count=Count("id")).values_list("dzongkhag__name", "count")),
        }

        # Technical Statistics
        technical_qs = Clearance.objects.filter(jurisdiction_filter & date_filter)
        stats["technical"] = {
            "total": technical_qs.count(),
            "by_status": dict(technical_qs.values("state").annotate(count=Count("id")).values_list("state", "count")),
            "by_dzongkhag": dict(technical_qs.values("dzongkhag__name").annotate(count=Count("id")).values_list("dzongkhag__name", "count")),
        }

        # Ongoing Construction Statistics (filter through permit relationship)
        ongoing_jurisdiction_filter = self._adapt_filter_for_ongoing_construction(jurisdiction_filter)
        ongoing_qs = Inspection.objects.filter(ongoing_jurisdiction_filter & date_filter)
        stats["ongoing_construction"] = {
            "total": ongoing_qs.count(),
            "by_status": dict(ongoing_qs.values("state").annotate(count=Count("id")).values_list("state", "count")),
            "by_dzongkhag": dict(ongoing_qs.values("permit__dzongkhag__name").annotate(count=Count("id")).values_list("permit__dzongkhag__name", "count")),
        }

        # Occupancy Statistics (filter through permit relationship)
        occupancy_jurisdiction_filter = self._adapt_filter_for_occupancy(jurisdiction_filter)
        occupancy_qs = Certificate.objects.filter(occupancy_jurisdiction_filter & date_filter)
        stats["occupancy"] = {
            "total": occupancy_qs.count(),
            "by_status": dict(occupancy_qs.values("state").annotate(count=Count("id")).values_list("state", "count")),
            "by_dzongkhag": dict(occupancy_qs.values("permit__dzongkhag__name").annotate(count=Count("id")).values_list("permit__dzongkhag__name", "count")),
        }

        # Modification Statistics
        modification_qs = ModificationPermit.objects.filter(jurisdiction_filter & date_filter)
        stats["modification"] = {
            "total": modification_qs.count(),
            "by_status": dict(modification_qs.values("state").annotate(count=Count("id")).values_list("state", "count")),
            "by_dzongkhag": dict(modification_qs.values("dzongkhag__name").annotate(count=Count("id")).values_list("dzongkhag__name", "count")),
        }

        # Government Structures Statistics (if user has access)
        if self.user_role in ["admin", "dhsadm", "dhsce", "dce", "dzongda"]:
            gov_qs = Structure.objects.filter(date_filter)
            stats["government"] = {
                "total": gov_qs.count(),
                "by_status": dict(gov_qs.values("status").annotate(count=Count("id")).values_list("status", "count")),
                "by_organization_type": dict(gov_qs.values("organization_type").annotate(count=Count("id")).values_list("organization_type", "count")),
            }

        return stats

    def get_user_statistics(self):
        """
        Get user statistics by role and location
        """
        if self.user_role not in ["admin", "dhsadm", "dce", "dadm", "tce", "thrompon", "roidce", "roidadm"]:
            return {"error": "Insufficient permissions"}

        user_filter = Q()

        if self.user_role in ["dce", "dzongda"]:
            user_filter &= Q(profile__dzongkhag=self.user.dzongkhag)
        elif self.user_role in ["tce", "thrompon"]:
            user_filter &= Q(profile__thromde=self.user.thromde)
        elif self.user_role in ["roidce", "roidadm"]:
            user_filter &= Q(profile__region=self.user.region)
        user_qs = User.objects.select_related("current_role", "profile__dzongkhag", "profile__thromde").filter(user_filter)

        # Exclude None keys from the stats dictionaries for dzongkhag and thromde
        def clean_none_keys(d):
            """Remove None keys from a dict."""
            return {k: v for k, v in d.items() if k is not None}

        stats = {
            "total_users": user_qs.count(),
            "by_role": clean_none_keys(dict(user_qs.values("current_role__name").annotate(count=Count("id")).values_list("current_role__description", "count"))),
            "by_dzongkhag": clean_none_keys(dict(user_qs.values("profile__dzongkhag__name").annotate(count=Count("id")).values_list("profile__dzongkhag__name", "count"))),
            "by_thromde": clean_none_keys(dict(user_qs.values("profile__thromde__name").annotate(count=Count("id")).values_list("profile__thromde__name", "count"))),
            "active_users": user_qs.filter(is_active=True).count(),
            "inactive_users": user_qs.filter(is_active=False).count(),
        }

        return stats

    def get_dashboard_data(self):
        """
        Get comprehensive dashboard data based on user role
        """
        dashboard_data = {"construction_statistics": self.get_construction_statistics(), "summary": self._get_summary_statistics()}

        # Add user statistics for admin roles
        if self.user_role in ["admin", "dhsadm", "dce", "dadm", "tce", "thrompon", "roidce", "roidadm"]:
            dashboard_data["user_statistics"] = self.get_user_statistics()

        return dashboard_data

    def _get_summary_statistics(self):
        """
        Get summary statistics across all modules
        """
        jurisdiction_filter = self.get_user_jurisdiction_filter()

        # Adapt filters for models that access location through relationships
        occupancy_filter = self._adapt_filter_for_occupancy(jurisdiction_filter)

        total_applications = (
            Inquiry.objects.filter(jurisdiction_filter).count()
            + Application.objects.filter(jurisdiction_filter).count()
            + DesignPermit.objects.filter(jurisdiction_filter).count()
            + BuildingPermit.objects.filter(jurisdiction_filter).count()
            + Clearance.objects.filter(jurisdiction_filter).count()
            + Certificate.objects.filter(occupancy_filter).count()
            + ModificationPermit.objects.filter(jurisdiction_filter).count()
        )

        approved_applications = (
            Inquiry.objects.filter(jurisdiction_filter, state="approved").count()
            + Application.objects.filter(jurisdiction_filter, state="approved").count()
            + DesignPermit.objects.filter(jurisdiction_filter, state="approved").count()
            + BuildingPermit.objects.filter(jurisdiction_filter, state="approved").count()
            + Clearance.objects.filter(jurisdiction_filter, state="approved").count()
            + Certificate.objects.filter(occupancy_filter, state="approved").count()
            + ModificationPermit.objects.filter(jurisdiction_filter, state="approved").count()
        )

        pending_applications = total_applications - approved_applications

        return {
            "total_applications": total_applications,
            "approved_applications": approved_applications,
            "pending_applications": pending_applications,
            "approval_rate": round((approved_applications / total_applications * 100) if total_applications > 0 else 0, 2),
        }


class ExportService:
    def __init__(self, user, export_type="excel"):
        self.user = user
        self.export_type = export_type
        self.data_service = ReportDataService(user)

    def export_construction_data(self, module="all"):
        """
        Export construction data to Excel/CSV
        """
        jurisdiction_filter = self.data_service.get_user_jurisdiction_filter()

        dataframes = {}

        if module == "all":
            # Information Service data
            try:
                information_data = list(
                    Inquiry.objects.filter(jurisdiction_filter)
                    .select_related("dzongkhag", "gewog", "user")
                    .values(
                        "id",
                        "serial_no",
                        "state",
                        "created_at",
                        "construction_id",
                        "dzongkhag__name",
                        "gewog__name",
                        "applicant_type",
                        "cid",
                    )
                )
                if information_data:
                    dataframes["information"] = information_data
            except Exception:
                pass

            # Planning data
            try:
                planning_data = list(
                    Application.objects.filter(jurisdiction_filter)
                    .select_related("dzongkhag", "gewog", "user")
                    .values(
                        "id",
                        "serial_no",
                        "state",
                        "created_at",
                        "construction_id",
                        "dzongkhag__name",
                        "gewog__name",
                        "nature",
                    )
                )
                if planning_data:
                    dataframes["planning"] = planning_data
            except Exception:
                pass

            # Design data
            try:
                design_data = list(
                    DesignPermit.objects.filter(jurisdiction_filter)
                    .select_related("dzongkhag", "gewog", "user")
                    .values(
                        "id",
                        "serial_no",
                        "state",
                        "created_at",
                        "construction_id",
                        "dzongkhag__name",
                        "gewog__name",
                    )
                )
                if design_data:
                    dataframes["design"] = design_data
            except Exception:
                pass

            # Building data
            try:
                building_data = list(
                    BuildingPermit.objects.filter(jurisdiction_filter)
                    .select_related("dzongkhag", "gewog", "user")
                    .values(
                        "id",
                        "serial_no",
                        "state",
                        "created_at",
                        "construction_id",
                        "dzongkhag__name",
                        "gewog__name",
                        "no_of_floors",
                        "no_of_units",
                    )
                )
                if building_data:
                    dataframes["building"] = building_data
            except Exception:
                pass

            # Technical data
            try:
                technical_data = list(
                    Clearance.objects.filter(jurisdiction_filter)
                    .select_related("dzongkhag", "gewog", "user")
                    .values(
                        "id",
                        "serial_no",
                        "state",
                        "created_at",
                        "construction_id",
                        "dzongkhag__name",
                        "gewog__name",
                        "hotel_type",
                    )
                )
                if technical_data:
                    dataframes["technical"] = technical_data
            except Exception:
                pass

            # Occupancy data
            try:
                occupancy_data = list(
                    Certificate.objects.filter(jurisdiction_filter)
                    .select_related("permit__dzongkhag", "permit__gewog", "user")
                    .values(
                        "id",
                        "serial_no",
                        "state",
                        "created_at",
                        "construction_id",
                        "permit__dzongkhag__name",
                        "permit__gewog__name",
                        "certificate_type",
                    )
                )
                # Normalize field names
                for record in occupancy_data:
                    record["dzongkhag__name"] = record.pop("permit__dzongkhag__name", None)
                    record["gewog__name"] = record.pop("permit__gewog__name", None)
                if occupancy_data:
                    dataframes["occupancy"] = occupancy_data
            except Exception:
                pass

            # Modification data
            try:
                modification_data = list(
                    ModificationPermit.objects.filter(jurisdiction_filter)
                    .select_related("dzongkhag", "gewog", "user")
                    .values(
                        "id",
                        "serial_no",
                        "state",
                        "created_at",
                        "construction_id",
                        "dzongkhag__name",
                        "gewog__name",
                    )
                )
                if modification_data:
                    dataframes["modification"] = modification_data
            except Exception:
                pass

            # Ongoing Construction data
            try:
                ongoing_data = list(
                    Inspection.objects.filter(jurisdiction_filter)
                    .select_related("permit__dzongkhag", "permit__gewog", "user")
                    .values(
                        "id",
                        "serial_no",
                        "state",
                        "created_at",
                        "construction_id",
                        "permit__dzongkhag__name",
                        "permit__gewog__name",
                    )
                )
                # Normalize field names
                for record in ongoing_data:
                    record["dzongkhag__name"] = record.pop("permit__dzongkhag__name", None)
                    record["gewog__name"] = record.pop("permit__gewog__name", None)
                if ongoing_data:
                    dataframes["ongoing_construction"] = ongoing_data
            except Exception:
                pass

        elif module == "information":
            information_data = list(
                Inquiry.objects.filter(jurisdiction_filter)
                .select_related("dzongkhag", "gewog", "user")
                .values(
                    "id",
                    "serial_no",
                    "state",
                    "created_at",
                    "construction_id",
                    "dzongkhag__name",
                    "gewog__name",
                    "applicant_type",
                    "cid",
                )
            )
            dataframes["information"] = information_data

        elif module == "planning":
            planning_data = list(
                Application.objects.filter(jurisdiction_filter)
                .select_related("dzongkhag", "gewog", "user")
                .values(
                    "id",
                    "serial_no",
                    "state",
                    "created_at",
                    "construction_id",
                    "dzongkhag__name",
                    "gewog__name",
                    "nature",
                )
            )
            dataframes["planning"] = planning_data

        elif module == "design":
            design_data = list(
                DesignPermit.objects.filter(jurisdiction_filter)
                .select_related("dzongkhag", "gewog", "user")
                .values(
                    "id",
                    "serial_no",
                    "state",
                    "created_at",
                    "construction_id",
                    "dzongkhag__name",
                    "gewog__name",
                )
            )
            dataframes["design"] = design_data

        elif module == "building":
            building_data = list(
                BuildingPermit.objects.filter(jurisdiction_filter)
                .select_related("dzongkhag", "gewog", "user")
                .values(
                    "id",
                    "serial_no",
                    "state",
                    "created_at",
                    "construction_id",
                    "dzongkhag__name",
                    "gewog__name",
                    "no_of_floors",
                    "no_of_units",
                )
            )
            dataframes["building"] = building_data

        elif module == "technical":
            technical_data = list(
                Clearance.objects.filter(jurisdiction_filter)
                .select_related("dzongkhag", "gewog", "user")
                .values(
                    "id",
                    "serial_no",
                    "state",
                    "created_at",
                    "construction_id",
                    "dzongkhag__name",
                    "gewog__name",
                    "hotel_type",
                )
            )
            dataframes["technical"] = technical_data

        elif module == "occupancy":
            occupancy_data = list(
                Certificate.objects.filter(jurisdiction_filter)
                .select_related("permit__dzongkhag", "permit__gewog", "user")
                .values(
                    "id",
                    "serial_no",
                    "state",
                    "created_at",
                    "construction_id",
                    "permit__dzongkhag__name",
                    "permit__gewog__name",
                    "certificate_type",
                )
            )
            # Normalize field names
            for record in occupancy_data:
                record["dzongkhag__name"] = record.pop("permit__dzongkhag__name", None)
                record["gewog__name"] = record.pop("permit__gewog__name", None)
            dataframes["occupancy"] = occupancy_data

        elif module == "modification":
            modification_data = list(
                ModificationPermit.objects.filter(jurisdiction_filter)
                .select_related("dzongkhag", "gewog", "user")
                .values(
                    "id",
                    "serial_no",
                    "state",
                    "created_at",
                    "construction_id",
                    "dzongkhag__name",
                    "gewog__name",
                )
            )
            dataframes["modification"] = modification_data

        elif module == "ongoing_construction":
            ongoing_data = list(
                Inspection.objects.filter(jurisdiction_filter)
                .select_related("permit__dzongkhag", "permit__gewog", "user")
                .values(
                    "id",
                    "serial_no",
                    "state",
                    "created_at",
                    "construction_id",
                    "permit__dzongkhag__name",
                    "permit__gewog__name",
                )
            )
            # Normalize field names
            for record in ongoing_data:
                record["dzongkhag__name"] = record.pop("permit__dzongkhag__name", None)
                record["gewog__name"] = record.pop("permit__gewog__name", None)
            dataframes["ongoing_construction"] = ongoing_data

        return dataframes
