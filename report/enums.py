from django.db import models
from django.utils.translation import gettext_lazy as _


class ReportType(models.TextChoices):
    DASHBOARD = "dashboard", _("Dashboard")
    EXPORT = "export", _("Export")
    STATISTICS = "statistics", _("Statistics")


class ExportType(models.TextChoices):
    EXCEL = "excel", _("Excel")
    CSV = "csv", _("CSV")
    PDF = "pdf", _("PDF")


class ExportStatus(models.TextChoices):
    PENDING = "pending", _("Pending")
    COMPLETED = "completed", _("Completed")
    FAILED = "failed", _("Failed")


class ReportModule(models.TextChoices):
    INFORMATION = "information", _("Information Service")
    PLANNING = "planning", _("Planning")
    DESIGN = "design", _("Design")
    BUILDING = "building", _("Building")
    TECHNICAL = "technical", _("Technical")
    ONGOING_CONSTRUCTION = "ongoing_construction", _("Ongoing Construction")
    OCCUPANCY = "occupancy", _("Occupancy")
    MODIFICATION = "modification", _("Modification")
    GOVERNMENT = "government", _("Government")
    ALL = "all", _("All Modules")


class ConstructionStatus(models.TextChoices):
    INITIATED = "initiated", _("Initiated")
    PENDING = "pending", _("Pending")
    APPROVED = "approved", _("Approved")
    REJECTED = "rejected", _("Rejected")
    COMPLETED = "completed", _("Completed")
    ONGOING = "ongoing", _("Ongoing")
    CANCELLED = "cancelled", _("Cancelled")


class UserRoleCategory(models.TextChoices):
    SUPER_ADMIN = "admin", _("Super Admin")
    AGENCY_ADMIN = "agency_admin", _("Agency Admin")
    DHS_DIRECTOR = "dhsadm", _("DHS Director")
    CHIEF_SRBD = "dhsce", _("Chief SRBD")
    THROMDE_THROMPON = "thrompon", _("Thromde Thrompon")
    EXECUTIVE_SECRETARY = "executive_secretary", _("Executive Secretary")
    DZONGDA = "dzongda", _("Dzongda")
    GUP = "gup", _("Gup")
    CHIEF_ENGINEER = "chief_engineer", _("Chief Engineer")
    AGENCY_USER = "agency_user", _("Agency User")
    TOURISM_DEPT = "tcb", _("Department of Tourism")
    OTHER_AGENCIES = "other_agencies", _("Other Agencies")
