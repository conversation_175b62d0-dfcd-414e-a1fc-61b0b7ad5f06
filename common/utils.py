from information.models import Inquiry
from planning.models import Application


def generate_construction_id(prefix, model):
    if model == "inquiry":
        existing_ids = Inquiry.objects.filter(construction_id__startswith=prefix).values_list("construction_id", flat=True)
    else:
        existing_ids = Application.objects.filter(construction_id__startswith=prefix).values_list("construction_id", flat=True)
    used_numbers = set()
    for cid in existing_ids:
        try:
            num_part = cid.replace(prefix, "")
            num = int(num_part)
            used_numbers.add(num)
        except (ValueError, AttributeError):
            continue
    number = 1
    while number in used_numbers:
        number += 1
    construction_id = f"{prefix}{number}"
    return construction_id
