import factory

from file.factories import AttachmentFactory
from user.factories import RoleFactory, UserFactory
from .models import *


class ServiceFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Service

    name = factory.Sequence(lambda n: f"Service {n}")
    code = factory.Sequence(lambda n: f"SVC{n}")
    service_type = "bp"  # Default to building permit
    description = factory.Faker("text")
    user = factory.SubFactory(UserFactory)


class DesignTeamFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = DesignTeam

    architect_cid = factory.Faker("ssn")
    architect_name = factory.Faker("name")
    architect_certificate = factory.SubFactory(AttachmentFactory)
    electrical_engineer_cid = factory.Faker("ssn")
    electrical_engineer_name = factory.Faker("name")
    electrical_engineer_certificate = factory.SubFactory(AttachmentFactory)
    structural_engineer_cid = factory.Faker("ssn")
    structural_engineer_name = factory.Faker("name")
    structural_engineer_certificate = factory.SubFactory(AttachmentFactory)
    design_team_certificate = factory.SubFactory(AttachmentFactory)


class TaskPoolFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = TaskPool

    by = factory.SubFactory(UserFactory)
    user = factory.SubFactory(UserFactory)
    role = factory.SubFactory(RoleFactory)
    remarks = factory.Faker("text")
    created_at = factory.Faker("date_time")
    updated_at = factory.Faker("date_time")


class ConstructionTypeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ConstructionType

    name = factory.Sequence(lambda n: f"Construction Type {n}")
    description = factory.Faker("text")
    user = factory.SubFactory(UserFactory)


class BuildingTypeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = BuildingType

    name = factory.Sequence(lambda n: f"Building Type {n}")
    description = factory.Faker("text")
    user = factory.SubFactory(UserFactory)


class UseFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Use

    name = factory.Sequence(lambda n: f"Use {n}")
    description = factory.Faker("text")
    rate = factory.Faker("pyfloat", positive=True)
    user = factory.SubFactory(UserFactory)


class ProposalTypeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ProposalType

    name = factory.Sequence(lambda n: f"Proposal Type {n}")
    description = factory.Faker("text")
    user = factory.SubFactory(UserFactory)


class FloorTypeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = FloorType

    name = factory.Sequence(lambda n: f"Floor Type {n}")
    description = factory.Faker("text")
