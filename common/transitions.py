from django_fsm import transition
from django_fsm_log.decorators import fsm_log_by
from rest_framework.exceptions import ValidationError
from django_fsm_log.decorators import fsm_log_description


class Transition:
    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="initiated", target="assigned")
    def initial(self, by=None, description=None):
        self.by = by
        description.set("Task initiated.")

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["pending_change", "completed"], target="assigned")
    def assign(self, data, by=None, description=None):
        self.by = by
        description.set(data.get("remarks", "Task re-assigned"))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="in_progress", target="completed")
    def approve(self, data, by=None, description=None):
        self.by = by
        self.data = data
        description.set(
            data.get(
                "remarks",
                f"Task approved successfully by {by.current_role.description}",
            )
        )
        self.remarks = data.get("remarks")

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="rejected")
    def reject(self, data, by=None, description=None):
        self.by = by
        self.data = data
        if not data.get("remarks"):
            raise ValidationError({"error": "You should provide rejection remarks."})
        self.remarks = f"Reason for rejection: {data.get('remarks')}"
        description.set(f"Reason for rejection: {data.get('remarks')}")

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="assigned", target="in_progress")
    def start(self, data, by=None, description=None):
        self.by = by
        description.set(data.get("remarks", "Task is processing"))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="pending_change")
    def change(self, data, by=None, description=None):
        self.by = by
        self.data = data
        description.set(data.get("remarks", "Change requested"))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="closed")
    def close(self, data, by=None, description=None):
        self.by = by
        self.data = data
        description.set(data.get("remarks", "Change requested"))
