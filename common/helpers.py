from cas_api.services.birms_autonomous_service import generate_autonomous_payment_advice
from cas_api.services.birms_service import generate_payment_advice
from payment.enums import PaymentStatus, PaymentMode
from user.models import Role
from user.models import Role, User
from rest_framework.exceptions import ValidationError
from django.db import transaction


class Helper:
    def can_approve(self, current_user):
        return self.poolable.can_approve(current_user)

    def can_reject(self, current_user):
        return True

    def can_start(self, current_user):
        if current_user.current_role.name == self.role.name:
            return True
        return False

    def can_change(self, current_user):
        return True


def fetch_address(objects, name):
    from Levenshtein import distance

    # First, try to match the whole name
    matches = [[obj, distance(name, obj.name)] for obj in objects]
    matches = [m for m in matches if m[1] <= 3]
    if matches:
        return min(matches, key=lambda x: x[1])[0]

    # If not found, try matching only the first word
    name_first = name.split()[0] if name.split() else name
    matches = [[obj, distance(name_first, obj.name.split()[0] if obj.name.split() else obj.name)] for obj in objects]
    matches = [m for m in matches if m[1] <= 3]
    if matches:
        return min(matches, key=lambda x: x[1])[0]

    # If still not found, return None
    return None


def determine_assignee_user(role_name, dzo=None, gwg=None, thrm=None, rgn=None, user_id=None):
    role = Role.objects.filter(name=role_name).first()
    if not role:
        raise ValidationError({"error": f"Invalid role name: {role_name}"})
    users = User.objects.filter(roles=role)
    if user_id:
        users = users.filter(id=user_id)
    if dzo:
        users = users.filter(profile__dzongkhag=dzo)
    if gwg:
        users = users.filter(profile__gewog=gwg)
    if thrm:
        users = users.filter(profile__thromde=thrm)
    if rgn:
        users = users.filter(profile__region=rgn)
    if users:
        user = min(users, key=lambda x: len(x.task_pools.exclude(state__in=["completed", "in_progress"])))
        return [user, role]
    else:
        if getattr((thrm or gwg or dzo), "name", None):
            msg = f"No '{role.description}' user found in {(thrm or gwg or dzo).name}"
        else:
            msg = f"No '{role.description}' user found."
        raise ValidationError({"error": msg})


def is_task_completed(instance, role_name):
    task = instance.task_pools.filter(role__name=role_name).first()
    return task and task.state == "completed"


def create_slabs_set_fee(instance, data):
    instance.land_pooling = data.get("land_pooling")
    instance.no_of_floors = data.get("no_of_floors")
    validate_slabs(instance, data)
    slabs = data.get("slabs")
    create_slabs(instance, slabs)
    instance.fee = sum(obj["fee"] for obj in slabs)
    instance.save()


def validate_slabs(instance, data):
    if "slabs" not in data:
        raise ValidationError({"error": "You should provide slabs informations."})
    elif "no_of_floors" not in data:
        raise ValidationError({"error": "You need to provide no_of_floors."})
    else:
        slabs = data.get("slabs")
        if len(slabs) != instance.no_of_floors:
            raise ValidationError({"error": f"You need to provide information for all {instance.no_of_floors} slabs."})
        else:
            for slab in slabs:
                slab["land_pooling"] = data.get("land_pooling")
                amount = calculate_amount(instance, slab)
                slab["fee"] = amount


def adm_user(instance):
    if instance.nature == "urban":
        tadms = User.objects.filter(roles__name="tadm", profile__dzongkhag=instance.dzongkhag, profile__thromde=instance.thromde)
        user = next((u for u in tadms if len(u.settings.all()) > 0), None)
        if not user:
            user = tadms.first()
    else:
        dadms = User.objects.filter(roles__name="dadm", profile__dzongkhag=instance.dzongkhag)
        user = next((u for u in dadms if len(u.settings.all()) > 0), None)
        if not user:
            user = dadms.first()
    if not user:
        raise ValidationError({"error": f"There is no admin for {instance.dzongkhag.name if instance.nature=='rural' else instance.thromde.name}"})
    return user


def calculate_amount(instance, data):
    from common.models import Use

    land_pooling = data.get("land_pooling")
    area = data.get("area")
    usage_id = data.get("usage_id")
    if not area or not usage_id or land_pooling in [None, ""]:
        raise ValidationError({"error": "You need to provide area, usage_id and land_pooling."})
    setting = adm_user(instance).settings.filter(key="rate").first()
    if not setting:
        msg = "You have not set rate in the setting, please contact your respective administration to set the rate in their setting."
        raise ValidationError({"error": msg})
    rate = float(setting.value) if setting.value not in (None, "") else 0.0
    scrutiny_fee = max([rate * area, 3000])
    if instance.nature == "urban":
        usage = Use.objects.get(id=usage_id)
        usage_rate = usage.rate or 0
        aminity_fee = usage_rate * area
        if land_pooling:
            aminity_fee -= aminity_fee * 0.75
        return scrutiny_fee + aminity_fee
    else:
        return scrutiny_fee


def create_slabs(instance, slabs):
    from common.models import Slab

    instances = []
    for slab in slabs:
        if "amount" in slab:
            slab.pop("amount")
            slab.pop("land_pooling")
        instances.append(Slab(slabable=instance, **slab))
    try:
        with transaction.atomic():
            Slab.objects.bulk_create(instances)
    except Exception as e:
        raise ValidationError({"error": str(e)})


def create_payment(instance, amount, payer, service, agency_code=None, counter_code=None, tenant_code=None):
    return instance.payments.create(
        amount=amount,
        status=PaymentStatus.INITIATED,
        mode=PaymentMode.BIRMS,
        reference_no=instance.serial_no,
        payer_cid=payer.cid,
        payer_name=payer.name,
        payer_email=payer.email,
        payer_phone=payer.phone,
        agency_code=agency_code,
        counter_code=counter_code,
        tenant_code=tenant_code,
        service_code=service.code,
        service_name=service.name,
    )


def generate_payment(service_type, amount, instance):
    from common.models import Service

    thromde = instance.thromde
    dzongkhag = instance.dzongkhag
    service = Service.objects.get(service_type=service_type)
    if not service:
        raise ValidationError({"error": "No service found."})
    tcodes = {"counter_code": thromde.counter_code, "tenant_code": thromde.tenant_code} if thromde else {}
    payment = create_payment(instance, amount, instance.user, service, agency_code=dzongkhag.agency_code, **tcodes)
    transaction.on_commit(lambda: generate_autonomous_payment_advice.delay(payment.id) if instance.nature == "urban" else generate_payment_advice.delay(payment.id))
