from address.serializers import *
from .models import *
from django_fsm_log.models import StateLog
import importlib
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer


class TaskPoolSerializer(serializers.ModelSerializer):
    role_name = serializers.CharField(source="role.name", read_only=True)
    role_description = serializers.CharField(source="role.description", read_only=True)
    user_name = serializers.CharField(source="user.name", read_only=True)
    user_cid = serializers.CharField(source="user.cid", read_only=True)
    by_name = serializers.CharField(source="by.name", read_only=True)
    by_role_description = serializers.CharField(source="by_role.description", read_only=True)
    content_type = serializers.StringRelatedField(source="content_type.model")
    state_display = serializers.Char<PERSON>ield(source="get_state_display", read_only=True)

    class Meta:
        model = TaskPool
        fields = [
            "id",
            "content_type",
            "object_id",
            "user_id",
            "user_cid",
            "user_name",
            "state",
            "state_display",
            "role_id",
            "role_name",
            "role_description",
            "remarks",
            "by_role_description",
            "by_name",
            "by_id",
        ]

    def get_poolable(self, obj):
        poolable_instance = obj.poolable
        if poolable_instance:
            content_type = ContentType.objects.get_for_model(poolable_instance)
            serializer_class = self.get_serializer_for_model(content_type.model_class())
            if serializer_class:
                return serializer_class(poolable_instance).data
        return None

    def get_serializer_for_model(self, instance):
        app_label = instance._meta.app_label  # Get the app label of the model instance
        try:
            module_path = f"{app_label}.serializers"
            module = importlib.import_module(module_path)
            serializer_class = getattr(module, f"{instance.__name__}Serializer", None)
            return serializer_class
        except (ModuleNotFoundError, AttributeError) as e:
            return None


class DesignTeamSearializer(serializers.ModelSerializer):
    certificate_id = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="certificate", required=False, allow_null=True)
    drawing_id = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="drawing", required=True)
    certificate_file = serializers.FileField(source="certificate.file", read_only=True)
    drawing_file = serializers.FileField(source="drawing.file", read_only=True)

    class Meta:
        model = DesignTeam
        fields = ["id", "cid", "name", "description", "certificate_id", "drawing_id", "certificate_file", "drawing_file", "bcta_no", "team_type", "created_at", "updated_at"]


class StateLogSerializer(serializers.ModelSerializer):
    by_id = serializers.IntegerField(source="by.id", read_only=True)
    by_name = serializers.CharField(source="by.name", read_only=True)
    content_type = serializers.StringRelatedField(source="content_type.model")

    class Meta:
        model = StateLog
        fields = ["id", "timestamp", "by_id", "by_name", "source_state", "state", "transition", "content_type", "object_id", "description"]


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        token["email"] = user.email
        token["cid"] = user.cid
        return token


class ConstructionTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ConstructionType
        fields = ["id", "name", "description", "user", "created_at", "updated_at"]
        read_only_fields = ["created_at", "updated_at", "id"]


class BuildingTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = BuildingType
        fields = ["id", "name", "description", "user", "created_at", "updated_at"]
        read_only_fields = ["created_at", "updated_at", "id"]


class UseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Use
        fields = ["id", "name", "description", "rate", "user", "created_at", "updated_at"]
        read_only_fields = ["created_at", "updated_at", "id"]


class ProposalTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProposalType
        fields = ["id", "name", "description", "user", "created_at", "updated_at"]
        read_only_fields = ["created_at", "updated_at", "id"]


class FloorTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = FloorType
        fields = ["id", "name", "description"]


class SlabSerializer(serializers.ModelSerializer):
    content_type = serializers.StringRelatedField(source="content_type.model")
    floor_type_name = serializers.CharField(source="floor_type.name", read_only=True)
    usage_name = serializers.CharField(source="usage.name", read_only=True)
    usage_id = serializers.PrimaryKeyRelatedField(queryset=Use.objects.all(), source="usage", required=True)
    floor_type_id = serializers.PrimaryKeyRelatedField(queryset=FloorType.objects.all(), source="floor_type", required=True)
    usage = UseSerializer(read_only=True)
    floor_type = FloorTypeSerializer(read_only=True)
    object_id = serializers.IntegerField(source="slabable.id", read_only=True)
    fee = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2)

    class Meta:
        model = Slab
        fields = ["id", "object_id", "content_type", "floor_type", "floor_type_id", "floor_type_name", "area", "usage", "usage_id", "usage_name", "fee"]
