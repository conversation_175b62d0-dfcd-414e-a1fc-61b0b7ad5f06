# Generated by Django 4.1.7 on 2025-03-27 22:23

from django.db import migrations
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0002_alter_taskpool_state'),
    ]

    operations = [
        migrations.AlterField(
            model_name='taskpool',
            name='state',
            field=django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('assigned', 'Assigned'), ('in_progress', 'In Progress'), ('forwarded_to_bpc', 'Forwareded to Bhutan Power Coperation'), ('forwarded_to_tcb', 'Forwareded to Tourism Counsil of Bhutan'), ('reviewing', 'Reviewing'), ('completed', 'Completed'), ('pending_change', 'Pending Change'), ('rejected', 'Rejected')], default='initiated', max_length=50),
        ),
    ]
