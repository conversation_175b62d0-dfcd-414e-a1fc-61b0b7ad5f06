# Generated by Django 4.1.7 on 2025-03-09 14:39

import common.helpers
import common.transitions
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('file', '0002_attachment_name_attachment_size_attachment_type'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('user', '0025_alter_user_username'),
    ]

    operations = [
        migrations.CreateModel(
            name='TaskPool',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveBigIntegerField()),
                ('state', django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('assigned', 'Assigned'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='initiated', max_length=50)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_task_pools', to=settings.AUTH_USER_MODEL)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('role', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='user.role')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='task_pools', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'task_pool',
                'verbose_name_plural': 'task_pools',
            },
            bases=(models.Model, common.helpers.Helper, common.transitions.Transition),
        ),
        migrations.CreateModel(
            name='DesignTeam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveBigIntegerField()),
                ('architect_cid', models.CharField(max_length=11)),
                ('architect_name', models.CharField(max_length=100)),
                ('electrical_engineer_cid', models.CharField(max_length=11)),
                ('electrical_engineer_name', models.CharField(max_length=100)),
                ('structural_engineer_cid', models.CharField(max_length=11)),
                ('structural_engineer_name', models.CharField(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('architect_certificate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='architect_certificate', to='file.attachment')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('design_team_certificate', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='design_team_certificate', to='file.attachment')),
                ('electrical_engineer_certificate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='electrical_engineer_certificate', to='file.attachment')),
                ('structural_engineer_certificate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='structural_engineer_certificate', to='file.attachment')),
            ],
        ),
    ]
