# Generated by Django 4.1.7 on 2025-06-25 22:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0027_profile_region'),
        ('common', '0015_alter_taskpool_state'),
    ]

    operations = [
        migrations.AddField(
            model_name='taskpool',
            name='by_role',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='assigned_task_pools', to='user.role'),
        ),
    ]
