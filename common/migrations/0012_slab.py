# Generated by Django 4.1.7 on 2025-04-06 10:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('common', '0011_alter_use_rate'),
    ]

    operations = [
        migrations.CreateModel(
            name='Slab',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveBigIntegerField()),
                ('area', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('fee', models.DecimalField(decimal_places=2, default=0, max_digits=20)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('floor_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='slabs', to='common.floortype')),
                ('usage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='slabs', to='common.use')),
            ],
            options={
                'verbose_name': 'slab',
                'verbose_name_plural': 'slabs',
            },
        ),
    ]
