# Generated by Django 4.1.7 on 2025-05-29 17:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0013_rename_cdb_no_designteam_bcta_no'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='taskpool',
            index=models.Index(fields=['state'], name='common_task_state_565fec_idx'),
        ),
        migrations.AddIndex(
            model_name='taskpool',
            index=models.Index(fields=['created_at'], name='common_task_created_033b1d_idx'),
        ),
        migrations.AddIndex(
            model_name='taskpool',
            index=models.Index(fields=['updated_at'], name='common_task_updated_5993a7_idx'),
        ),
        migrations.AddIndex(
            model_name='taskpool',
            index=models.Index(fields=['user', 'state'], name='common_task_user_id_6f051b_idx'),
        ),
        migrations.AddIndex(
            model_name='taskpool',
            index=models.Index(fields=['role', 'state'], name='common_task_role_id_da3314_idx'),
        ),
    ]
