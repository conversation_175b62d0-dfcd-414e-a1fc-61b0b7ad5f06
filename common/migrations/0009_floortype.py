# Generated by Django 4.1.7 on 2025-04-04 22:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0008_service_service_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='FloorType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'floor_type',
                'verbose_name_plural': 'floor_types',
            },
        ),
    ]
