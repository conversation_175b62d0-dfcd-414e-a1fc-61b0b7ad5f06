# Generated by Django 4.1.7 on 2025-04-01 19:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0004_rename_architect_cid_designteam_cid_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('agency_code', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('counter_code', models.CharField(blank=True, max_length=100, null=True)),
                ('tenant_code', models.CharField(blank=True, max_length=100, null=True)),
                ('code', models.CharField(max_length=100)),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('service_type', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'service',
                'verbose_name_plural': 'services',
            },
        ),
        migrations.AlterModelOptions(
            name='designteam',
            options={'verbose_name': 'design_team', 'verbose_name_plural': 'design_teams'},
        ),
    ]
