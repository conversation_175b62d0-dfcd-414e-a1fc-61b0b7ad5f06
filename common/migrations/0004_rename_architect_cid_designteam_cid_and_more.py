# Generated by Django 4.1.7 on 2025-03-28 11:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('file', '0002_attachment_name_attachment_size_attachment_type'),
        ('common', '0003_alter_taskpool_state'),
    ]

    operations = [
        migrations.RenameField(
            model_name='designteam',
            old_name='architect_cid',
            new_name='cid',
        ),
        migrations.RenameField(
            model_name='designteam',
            old_name='architect_name',
            new_name='name',
        ),
        migrations.RemoveField(
            model_name='designteam',
            name='architect_certificate',
        ),
        migrations.RemoveField(
            model_name='designteam',
            name='design_team_certificate',
        ),
        migrations.RemoveField(
            model_name='designteam',
            name='electrical_engineer_certificate',
        ),
        migrations.RemoveField(
            model_name='designteam',
            name='electrical_engineer_cid',
        ),
        migrations.Remove<PERSON>ield(
            model_name='designteam',
            name='electrical_engineer_name',
        ),
        migrations.RemoveField(
            model_name='designteam',
            name='structural_engineer_certificate',
        ),
        migrations.RemoveField(
            model_name='designteam',
            name='structural_engineer_cid',
        ),
        migrations.RemoveField(
            model_name='designteam',
            name='structural_engineer_name',
        ),
        migrations.AddField(
            model_name='designteam',
            name='cdb_no',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='designteam',
            name='certificate',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='certificate_design_teams', to='file.attachment'),
        ),
        migrations.AddField(
            model_name='designteam',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='designteam',
            name='drawing',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='drawing_design_teams', to='file.attachment'),
        ),
        migrations.AddField(
            model_name='designteam',
            name='team_type',
            field=models.CharField(choices=[('architect', 'Architect'), ('electrical_engineer', 'Electrical Engineer'), ('structural_engineer', 'Structural Engineer'), ('water_sanitation_engineer', 'Water Sanitation Engineer')], default='architect', max_length=50),
        ),
    ]
