# Generated by Django 4.1.7 on 2025-06-23 21:16

from django.db import migrations
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0014_taskpool_common_task_state_565fec_idx_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='taskpool',
            name='state',
            field=django_fsm.FSMField(choices=[('initiated', 'Initiated'), ('assigned', 'Assigned'), ('in_progress', 'In Progress'), ('forwarded_to_bpc', 'Forwareded to Bhutan Power Coperation'), ('forwarded_to_tcb', 'Forwareded to Tourism Counsil of Bhutan'), ('reviewing', 'Reviewing'), ('completed', 'Completed'), ('pending_change', 'Pending Change'), ('rejected', 'Rejected'), ('closed', 'Closed')], default='initiated', max_length=50),
        ),
    ]
