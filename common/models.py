from django.db import models
from django.contrib.contenttypes.fields import GenericF<PERSON><PERSON><PERSON><PERSON>
from django.contrib.contenttypes.models import ContentType
from common.enums import TaskPoolStatus
from common.helpers import Helper
from common.transitions import Transition
from django_fsm import <PERSON><PERSON><PERSON>ield
from django.utils.translation import gettext_lazy as _
from common.enums import TeamType
from file.models import Attachment


class TaskPool(models.Model, Helper, Transition):
    from user.models import Role, User

    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveBigIntegerField()
    poolable = GenericForeignKey("content_type", "object_id")
    by = models.ForeignKey(User, related_name="assigned_task_pools", null=True, blank=True, on_delete=models.SET_NULL)
    by_role = models.ForeignKey(Role, on_delete=models.CASCADE, null=True, blank=True, related_name="assigned_task_pools")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="task_pools")
    role = models.ForeignKey(Role, on_delete=models.CASCADE, null=True, blank=True)
    state = FSMField(max_length=50, null=False, choices=TaskPoolStatus.choices, default=TaskPoolStatus.INITIATED)
    remarks = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("task_pool")
        verbose_name_plural = _("task_pools")
        indexes = [
            models.Index(fields=["state"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["updated_at"]),
            models.Index(fields=["user", "state"]),
            models.Index(fields=["role", "state"]),
        ]

    def __str__(self):
        return str(self.__dict__)


class DesignTeam(models.Model):
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveBigIntegerField()
    teamable = GenericForeignKey("content_type", "object_id")
    cid = models.CharField(max_length=11, null=False)
    name = models.CharField(max_length=100, null=False)
    description = models.TextField(null=True, blank=True)
    certificate = models.ForeignKey(Attachment, related_name="certificate_design_teams", on_delete=models.CASCADE, null=True, blank=True)
    drawing = models.ForeignKey(Attachment, related_name="drawing_design_teams", on_delete=models.CASCADE, null=True, blank=True)
    bcta_no = models.CharField(max_length=100, null=True, blank=True)
    team_type = models.CharField(max_length=50, choices=TeamType.choices, default=TeamType.ARCHITECT)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        verbose_name = _("design_team")
        verbose_name_plural = _("design_teams")


class Service(models.Model):
    code = models.CharField(max_length=100, null=False, blank=False)
    name = models.CharField(max_length=200, null=False, blank=False)
    service_type = models.CharField(max_length=50, unique=True, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        verbose_name = _("service")
        verbose_name_plural = _("services")


class ConstructionType(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    user = models.ForeignKey("user.User", on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        verbose_name = _("construction_type")
        verbose_name_plural = _("construction_types")


class Use(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    user = models.ForeignKey("user.User", on_delete=models.SET_NULL, null=True)
    rate = models.FloatField(null=True, blank=True, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        verbose_name = _("use")
        verbose_name_plural = _("uses")


class BuildingType(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    user = models.ForeignKey("user.User", on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        verbose_name = _("building_type")
        verbose_name_plural = _("building_types")


class ProposalType(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    user = models.ForeignKey("user.User", on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        verbose_name = _("proposal_type")
        verbose_name_plural = _("proposal_types")


class FloorType(models.Model):
    name = models.CharField(max_length=50, null=False)
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        verbose_name = _("floor_type")
        verbose_name_plural = _("floor_types")


class Slab(models.Model):
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveBigIntegerField()
    slabable = GenericForeignKey("content_type", "object_id")
    floor_type = models.ForeignKey("common.FloorType", null=False, on_delete=models.CASCADE, related_name="slabs")
    area = models.DecimalField(decimal_places=2, max_digits=10, null=False, blank=False, default=0)
    usage = models.ForeignKey("common.Use", null=False, on_delete=models.CASCADE, related_name="slabs")
    fee = models.DecimalField(decimal_places=2, max_digits=20, null=False, blank=False, default=0)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        verbose_name = _("slab")
        verbose_name_plural = _("slabs")
