from cas_api.services.govtech_service import bcta_by_cid, bcta_by_arn, fetch_plot_coordinates, land_by_plot_no
from .serializers import *
from rest_framework.filters import SearchFilter
from rest_framework import generics, viewsets, response, status, views, exceptions
from rest_framework.decorators import action
from django.shortcuts import get_object_or_404
from django_fsm import can_proceed
from .populators import TaskPoolPopulator
import inflect
from rest_framework.response import Response
from django.db import transaction
from django.apps import apps
from common.helpers import calculate_amount


class TaskPoolViewSet(viewsets.ModelViewSet):
    queryset = TaskPool.objects.all()
    serializer_class = TaskPoolSerializer

    @action(detail=True, methods=["put"])
    def transition(self, request, **kwargs):
        with transaction.atomic():
            try:
                instance = get_object_or_404(TaskPool, pk=kwargs.get("pk"))
                action = request.data.pop("action")
                if instance.user.id != request.user.id:
                    msg = f"{request.user.current_role.description} is trying to perform action on behalf of other {instance.role.description}"
                    return response.Response({"error": msg}, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
                if not hasattr(instance, action) or not can_proceed(getattr(instance, action)):
                    return response.Response({"error": "Invalid transition"}, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
                if getattr(instance, "can_" + action)(request.user):
                    if not action == "start" and instance.state == "assigned":
                        raise exceptions.ValidationError({"error": "You need to start your task first."})
                    getattr(instance, action)(request.data, by=request.user)
                    instance.save()
                    serializer = TaskPoolSerializer(instance)
                    return response.Response(serializer.data, status=status.HTTP_200_OK)
                else:
                    return response.Response({"error": "You are not permited to perform this action."}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                transaction.set_rollback(True)
                raise e

    @action(detail=False, methods=["get"])
    def activity_logs(self, request, **kwargs):
        obj = get_object_or_404(TaskPool, pk=kwargs.get("pk"))
        logs = StateLog.objects.for_(obj)
        serializer = StateLogSerializer(logs, many=True)
        return response.Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"])
    def lands(self, request, **kwargs):
        res = land_by_plot_no(kwargs.get("plot_no"))
        if res["status"] == 200:
            try:
                datas = res["landDetails"].get("landDetail", [])
                result = next((land for land in datas if land["ownerCid"] == kwargs.get("cid")), None)
                if datas and result:
                    return Response(datas, status=res["status"] if res["status"] == 200 else status.HTTP_400_BAD_REQUEST)
                else:
                    return response.Response({"error": "No information found."}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return response.Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["get"])
    def bcta_by_cid(self, request, **kwargs):
        res = bcta_by_cid(kwargs.get("cid"))
        if res["status"] == 200:
            try:
                datas = res["engineerdetails"]
                if datas:
                    return Response(datas, status=res["status"] if res["status"] == 200 else status.HTTP_400_BAD_REQUEST)
                else:
                    return response.Response({"error": "No information found."}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return response.Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["get"])
    def bcta_by_arn(self, request, **kwargs):
        res = bcta_by_arn(kwargs.get("arn"))
        if res["status"] == 200:
            try:
                datas = res["engineerdetails"]
                if datas:
                    return Response(datas, status=res["status"] if res["status"] == 200 else status.HTTP_400_BAD_REQUEST)
                else:
                    return response.Response({"error": "No information found."}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return response.Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST) @ action(detail=True, methods=["get"])

    @action(detail=True, methods=["get"])
    def plot_coordinates(self, request, **kwargs):
        res = fetch_plot_coordinates(kwargs.get("plot_no"))
        if res["status"] == 200:
            try:
                if res:
                    return Response(res, status=res["status"] if res["status"] == 200 else status.HTTP_400_BAD_REQUEST)
                else:
                    return response.Response({"error": "No information found."}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return response.Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class TaskPoolView(generics.ListAPIView):
    serializer_class = TaskPoolSerializer
    filter_backends = [SearchFilter]
    search_fields = ("user__first_name", "user__last_name", "user__email", "role__name", "role__description")

    def get_queryset(self):
        return TaskPoolPopulator(self.request.user, self.request.query_params, self.kwargs).populate().distinct()


class ConstructionTypeView(generics.ListCreateAPIView):
    serializer_class = ConstructionTypeSerializer
    queryset = ConstructionType.objects.all()


class ConstructionTypePKView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ConstructionTypeSerializer
    queryset = ConstructionType.objects.all()


class BuildingTypeView(generics.ListCreateAPIView):
    serializer_class = BuildingTypeSerializer
    queryset = BuildingType.objects.all()


class BuildingTypePKView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = BuildingTypeSerializer
    queryset = BuildingType.objects.all()


class UseView(generics.ListCreateAPIView):
    serializer_class = UseSerializer
    queryset = Use.objects.all()


class UsePKView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = UseSerializer
    queryset = Use.objects.all()


class ProposalTypeView(generics.ListCreateAPIView):
    serializer_class = ProposalTypeSerializer
    queryset = ProposalType.objects.all()


class ProposalTypePKView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ProposalTypeSerializer
    queryset = ProposalType.objects.all()


class FloorTypeView(generics.ListCreateAPIView):
    serializer_class = FloorTypeSerializer
    queryset = FloorType.objects.all()


class FloorTypePKView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = FloorTypeSerializer
    queryset = FloorType.objects.all()


class FeeCalculationView(views.APIView):
    def post(self, request, *args, **kwargs):
        model_name = "".join(word.capitalize() for word in inflect.engine().singular_noun(kwargs.get("model_name")).split("_"))
        app_name = kwargs.get("app_name")
        app_label = inflect.engine().singular_noun(app_name.lower())
        try:
            model_class = apps.get_model(app_label, model_name)
        except LookupError:
            raise LookupError(f"Could not find model {model_name} in {app_label or app_name}")
        instance = model_class.objects.get(id=kwargs.get("object_id"))
        amount = calculate_amount(instance, request.data)
        return response.Response({"amount": amount}, status=status.HTTP_200_OK)
