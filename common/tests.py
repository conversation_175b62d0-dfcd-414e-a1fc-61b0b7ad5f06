from django.test import TestCase
from django.test import RequestFactory
from django.urls import path, include
from rest_framework.test import force_authenticate
from cas_api.middleware.current_request import _thread_locals
from django.core.management import call_command

# Create your tests here.


class BaseTest(TestCase):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("information.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")

        # Set up request context
        self.factory = RequestFactory()
        request = self.factory.get("/")
        force_authenticate(request, user=self.user)
        _thread_locals.request = request

    def tearDown(self):
        if hasattr(_thread_locals, "request"):
            del _thread_locals.request
        super().tearDown()
