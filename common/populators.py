import inflect
from django.apps import apps


class TaskPoolPopulator:
    def __init__(self, current_user, params, kwargs):
        model_name = "".join(word.capitalize() for word in inflect.engine().singular_noun(kwargs.get("model_name")).split("_"))
        app_name = kwargs.get("app_name")
        if app_name:
            app_label = inflect.engine().singular_noun(app_name.lower())
            try:
                model_class = apps.get_model(app_label, model_name)
            except LookupError:
                raise LookupError(f"Could not find model {model_name} in app {app_label}")
        else:
            for app_config in apps.get_app_configs():
                try:
                    model_class = apps.get_model(app_config.label, model_name)
                    break
                except LookupError:
                    continue
        model = model_class.objects.get(id=kwargs.get("object_id"))
        self.records = model.task_pools
        self.current_user = current_user
        self.params = params

    def populate(self):
        return self.records.order_by("-created_at")
