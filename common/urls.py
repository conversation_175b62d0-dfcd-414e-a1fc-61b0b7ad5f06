from django.urls import path
from common import views

urlpatterns = [
    path("<str:app_name>/<str:model_name>/<int:object_id>/task_pools/", views.TaskPoolView.as_view()),
    path("<str:model_name>/<int:object_id>/task_pools/", views.TaskPoolView.as_view()),
    path("task_pools/<pk>/transition/", views.TaskPoolViewSet.as_view({"put": "transition"}), name="task_pool_transitions"),
    path("task_pools/<pk>/activity_logs/", views.TaskPoolViewSet.as_view({"get": "activity_logs"}), name="activity_logs"),
    path("lands/<cid>/<plot_no>", views.TaskPoolViewSet.as_view({"get": "lands"}), name="lands"),
    path("bcta_by_cid/<cid>/", views.TaskPoolViewSet.as_view({"get": "bcta_by_cid"}), name="bcta_by_cid"),
    path("bcta_by_arn/<arn>/", views.TaskPoolViewSet.as_view({"get": "bcta_by_arn"}), name="bcta_by_arn"),
    path("plot_coordinates/<plot_no>/", views.TaskPoolViewSet.as_view({"get": "plot_coordinates"}), name="plot_coordinates"),
    path("construction_types/", views.ConstructionTypeView.as_view(), name="construction_types"),
    path("construction_types/<pk>/", views.ConstructionTypePKView.as_view(), name="construction_types"),
    path("building_types/", views.BuildingTypeView.as_view(), name="building_types"),
    path("building_types/<pk>/", views.BuildingTypePKView.as_view(), name="building_types"),
    path("uses/", views.UseView.as_view(), name="uses"),
    path("uses/<pk>/", views.UsePKView.as_view(), name="uses"),
    path("proposal_types/", views.ProposalTypeView.as_view(), name="proposal_types"),
    path("proposal_types/<pk>/", views.ProposalTypePKView.as_view(), name="proposal_types"),
    path("floor_types/", views.FloorTypeView.as_view(), name="floor_types"),
    path("floor_types/<pk>/", views.FloorTypePKView.as_view(), name="floor_types"),
    path("<str:app_name>/<str:model_name>/<int:object_id>/fee_calculation/", views.FeeCalculationView.as_view(), name="fee_calculation"),
]
