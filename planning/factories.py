import factory
from .models import *
from address.factories import *


class ApplicationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Application

    applicant_type = "own"
    dzongkhag = factory.SubFactory(DzongkhagFactory)
    gewog = factory.SubFactory(GewogFactory)
    village = factory.SubFactory(VillageFactory)
    land_certificate = factory.Faker("file_path", category="image")
    site_condition_plan = factory.Faker("file_path", category="image")
    construction_clearance = factory.Faker("file_path", category="image")


class ApplicantFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Applicant

    cid = factory.Faker("ssn")
    thram_no = factory.Faker("ssn")
    plot_no = factory.Faker("ssn")
    plot_area_unit = factory.Faker("ssn")
    plot_net_area = factory.Faker("pyint")
    ownership_type = factory.Faker("ssn")
    owner_name = factory.Faker("name")
    land_type = factory.Faker("name")
    prescient_code = factory.Faker("ssn")
    land_location_flag = "R"
    application = factory.SubFactory(ApplicationFactory)
    demkhong_name = "Mongar"
    gewog_thromde = "Mongar"
    dzongkhag_thromde = "Mongar"
