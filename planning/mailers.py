from django.template.loader import render_to_string
from cas_api.celery import app
from planning.models import Applicant, Application
import os
from cas_api.services.mailer_service import send_mail
from dotenv import load_dotenv
from user.models import User
from django_fsm_log.models import StateLog

load_dotenv()


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_requested(self, application_id):
    try:
        application = Application.objects.get(id=application_id)
        user = application.task_pools.filter(state="assigned").first().user
        url = f"{os.getenv('HOST_URL', '')}/services/planning-permit/{application_id}"
        html_content = render_to_string(
            "mailers/plannings/notify_requested.html",
            {"name": user.name, "serial_no": application.serial_no, "url": url},
        )
        send_mail("New Task Assigned", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_requested")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_forwarded(self, application_id):
    try:
        application = Application.objects.get(id=application_id)
        user = application.task_pools.filter(state="assigned").first().user
        url = f"{os.getenv('HOST_URL', '')}/services/planning-permit/{application_id}"
        html_content = render_to_string(
            "mailers/plannings/notify_forwarded.html",
            {"name": user.name, "serial_no": application.serial_no, "url": url},
        )
        send_mail("Planning permit forwarded for approval", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_forwarded")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_scheduled(self, application_id):
    try:
        application = Application.objects.get(id=application_id)
        user = application.user
        url = f"{os.getenv('HOST_URL', '')}/services/planning-permit/{application_id}"
        html_content = render_to_string(
            "mailers/plannings/notify_scheduled.html",
            {"name": user.name, "serial_no": application.serial_no, "url": url, "start_date": application.schedule_start_date, "end_date": application.schedule_end_date},
        )
        send_mail("Planning permit scheduled for inspection", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_scheduled")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_approved(self, application_id):
    try:
        application = Application.objects.get(id=application_id)
        user = application.user
        url = f"{os.getenv('HOST_URL', '')}/services/planning-permit/{application_id}"
        html_content = render_to_string("mailers/plannings/notify_approved.html", {"name": user.name, "url": url})
        send_mail("Planning permit is approved", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_approved")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_rejected(self, application_id):
    try:
        application = Application.objects.get(id=application_id)
        user = application.user
        url = f"{os.getenv('HOST_URL', '')}/services/planning-permit/{application_id}"
        html_content = render_to_string(
            "mailers/plannings/notify_rejected.html",
            {"name": user.name, "reason": application.rejection_remarks, "url": url},
        )
        send_mail("Planning permit is rejected", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_rejected")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_resubmitted(self, application_id, assignee_id):
    try:
        user = User.objects.get(id=assignee_id)
        url = f"{os.getenv('HOST_URL', '')}/services/planning-permit/{application_id}"
        html_content = render_to_string(
            "mailers/plannings/notify_resubmitted.html",
            {"name": user.name, "url": url},
        )
        send_mail("Planning permit is resubmitted", html_content, [user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_resubmitted")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_consent_request(self, id):
    try:
        applicant = Applicant.objects.get(id=id)
        application = applicant.application
        url = f"{os.getenv('HOST_URL', '')}/services/planning-permit/{application.id}/ndi-consent"
        html_content = render_to_string("mailers/plannings/notify_consent_request.html", {"url": url, "reference": applicant.application.serial_no})
        send_mail("Consent request", html_content, [applicant.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_consent_request")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_consent_provided(self, id):
    try:
        applicant = Applicant.objects.get(id=id)
        application = applicant.application
        url = f"{os.getenv('HOST_URL', '')}/services/planning-permit/{application.id}"
        html_content = render_to_string(
            "mailers/plannings/notify_consent_provided.html",
            {"url": url, "reference": application.serial_no, "name": application.user.name, "applicant_name": applicant.owner_name},
        )
        send_mail("Consent provided", html_content, [application.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_consent_provided")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_change(self, id):
    try:
        application = Application.objects.get(id=id)
        log = StateLog.objects.for_(application).last
        url = f"{os.getenv('HOST_URL', '')}/services/planning-permit/{application.id}"
        html_content = render_to_string(
            "mailers/plannings/notify_change.html",
            {"url": url, "reference": application.serial_no, "name": application.user.name, "remarks": getattr(log, "description", "Request for change")},
        )
        send_mail("Change request", html_content, [application.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_change")


@app.task(bind=True, max_retries=2, default_retry_delay=60)
def notify_payment(self, id):
    try:
        application = Application.objects.get(id=id)
        url = f"{os.getenv('HOST_URL', '')}/services/planning-permit/{application.id}"
        html_content = render_to_string(
            "mailers/plannings/notify_payment.html",
            {"url": url, "reference": application.serial_no, "name": application.user.name},
        )
        send_mail("Payment request", html_content, [application.user.email])
    except Exception as e:
        print(f"Unexpected error: {e}")
        try:
            self.retry(exc=e)  # Retry for unexpected errors
        except Exception:
            print("Max retries exceeded for task notify_payment")
