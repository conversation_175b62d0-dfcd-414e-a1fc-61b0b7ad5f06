from django.urls import path, include
from . import views

urlpatterns = [
    path("applications/<pk>/transition/", views.StateMachineViewSet.as_view({"put": "transition"}), name="transitions"),
    path("applications/", views.ApplicationView.as_view(), name="applications"),
    path("applications/<pk>/", views.ApplicationPKView.as_view(), name="applications"),
    path("review_questions/", views.ReviewQuestionViewSet.as_view(), name="review_questions"),
    path("applications/<pk>/review_answers/", views.ReviewAnswerViewSet.as_view(), name="review_answers"),
    path("applications/<pk>/save_review_answers/", views.ReviewAnswerViewSet.as_view(), name="save_review_answers"),
    path("site_questions/", views.SiteQuestionViewSet.as_view(), name="site_questions"),
    path("applications/<pk>/save_site_answers/", views.SiteAnswerViewSet.as_view(), name="save_site_answers"),
    path("applications/<pk>/site_answers/", views.SiteAnswerViewSet.as_view(), name="site_answers"),
    path("applications/<pk>/activity_logs/", views.PlanningViewSet.as_view({"get": "activity_logs"}), name="activity_logs"),
    path("applications/draft", views.PlanningViewSet.as_view({"get": "draft"}), name="draft"),
    path("applications/<pk>/certificate/", views.PdfView.as_view(), name="certificate"),
    path("applications/<pk>/consent/", views.PlanningViewSet.as_view({"post": "consent"}, authentication_classes=[], permission_classes=[])),
]
