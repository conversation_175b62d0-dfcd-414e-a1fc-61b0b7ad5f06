from django_fsm import transition
from django_fsm_log.decorators import fsm_log_by
from rest_framework.exceptions import ValidationError
from django_fsm_log.decorators import fsm_log_description


class Transition:
    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source=["initiated", "draft", "pending_consent"], target="requested")
    def initial(self, by=None, description=None):
        description.set("Planning permit initiated.")
        self.by = by

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="initiated", target="pending_consent")
    def consent(self, by=None, description=None):
        description.set("Planning permit waiting for consent.")
        self.by = by

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="forwarded", target="reviewed")
    def review(self, data, by=None, description=None):
        self.by = by
        self.data = data
        description.set(data.get("remarks", "Application reviewed."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="reviewed", target="scheduled")
    def schedule(self, data, by=None, description=None):
        self.by = by
        if not data.get("schedule_start_date") or not data.get("schedule_end_date"):
            raise ValidationError({"error": "Schedule start and end date are required."})
        self.schedule_start_date = data.get("schedule_start_date")
        self.schedule_end_date = data.get("schedule_end_date")
        self.schedule_remarks = data.get("schedule_remarks")
        self.save()
        description.set(data.get("remarks", "Application scheduled for site visit."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="scheduled", target="visited")
    def visit(self, data, by=None, description=None):
        self.by = by
        self.data = data
        description.set(data.get("visit_remarks", "Planning site visited."))
        self.visit_remarks = data.get("visit_remarks")

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="pending_payment", target="approved")
    def approve(self, data, by=None, description=None):
        self.by = by
        description.set(data.get("approval_remarks", "Planning permit approved."))
        self.approval_remarks = data.get("approval_remarks")
        self.save()
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="pending_change", target="resubmitted")
    def resubmit(self, data, by=None, description=None):
        description.set(data.get("remarks", "Application re-submitted"))
        self.by = by
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="rejected")
    def reject(self, data, by=None, description=None):
        self.by = by
        description.set(data.get("remarks", "Application rejected."))
        if not data.get("remarks"):
            raise ValidationError({"error": "You should provide rejection remarks."})
        self.rejection_remarks = data.get("remarks")
        self.save()
        self.data = data

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="closed")
    def close(self, data, by=None, description=None):
        self.by = by
        description.set(data.get("remarks", "Planning permit is closed."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="forwarded")
    def forward(self, data, by=None, description=None):
        self.by = by
        self.data = data
        description.set(data.get("remarks", "Application forwarded."))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="pending_change")
    def change(self, data, by=None, description=None):
        self.by = by
        self.data = data
        description.set(data.get("remarks", "Request for change"))

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="*", target="pending_payment")
    def payment(self, data, by=None, description=None):
        self.by = by
        self.data = data
        description.set(data.get("remarks", "Pending payment for planning permit."))
