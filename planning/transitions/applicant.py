from django_fsm import transition
from django_fsm_log.decorators import fsm_log_by
from django_fsm_log.decorators import fsm_log_description


class Transition:
    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="initiated", target="pending")
    def initial(self, by=None, description=None):
        description.set("Consent pending.")
        self.by = by

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="pending", target="approved")
    def approve(self, by=None, description=None):
        self.by = by
        description.set("Consent provided.")

    @fsm_log_by
    @fsm_log_description(allow_inline=True)
    @transition(field="state", source="pending", target="rejected")
    def reject(self, data, by=None, description=None):
        self.by = by
        description.set(data.get("rejection_remarks", "Consent rejected."))
