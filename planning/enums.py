from django.db import models
from django.utils.translation import gettext_lazy as _


class ApplicantType(models.TextChoices):
    OWN = "own", _("Own")
    OTHER = "other", _("Other")


class ApplicationStatus(models.TextChoices):
    DRAFT = "draft", _("Draft")
    PENDING_CONSENT = "pending_consent", _("Pending Consent")
    INITIATED = "initiated", _("Initiated")
    REQUESTED = "requested", _("Requested")
    REVIEWED = "reviewed", _("Reviewed")
    SCHEDULED = "scheduled", _("Scheduled")
    VISITED = "visited", _("Visited")
    FORWARDED = "forwarded", _("Forwarded")
    APPROVED = "approved", _("Approved")
    REJECTED = "rejected", _("Rejected")
    PENDING_CHANGE = "pending_change", _("Pending Change")
    RESUBMITTED = "resubmitted", _("Re-submitted")
    PENDING_PAYMENT = "pending_payment", _("Pending Payment")
    CLOSED = "closed", _("Closed")


class ApplicantStatus(models.TextChoices):
    INITIATED = "initiated", _("Initiated")
    PENDING = "pending", _("Pending")
    APPROVED = "approved", _("Approved")
    REJECTED = "rejected", _("Rejected")


class ChecklistType(models.TextChoices):
    TEXT = "text", _("Text")
    FILE = "file", _("File")
