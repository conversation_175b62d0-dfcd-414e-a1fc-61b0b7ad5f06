from django.db import models
from common.enums import LandNature, DevelopmentType, Purpose
from payment.models import Payment
from planning.enums import ApplicantType, ApplicationStatus, ApplicantStatus
from user.models import User
from django_fsm import FSMField
from address.models import Dzongkhag, Gewog, Region, Thromde, Village
from django.contrib.postgres.fields import ArrayField
from planning.transitions import application, applicant
from planning.helpers.application import Helper
from django.contrib.contenttypes.fields import GenericRelation
from common.models import TaskPool


class Application(models.Model, Helper, application.Transition):
    applicant_type = models.CharField(max_length=50, null=False, choices=ApplicantType.choices)
    nature = models.CharField(max_length=50, null=True, choices=LandNature.choices, default=LandNature.RURAL)
    user = models.ForeignKey(User, null=False, related_name="applications", on_delete=models.CASCADE)
    inquiry = models.ForeignKey("information.Inquiry", related_name="application", on_delete=models.CASCADE, null=True, blank=True)
    serial_no = models.CharField(max_length=50)
    construction_id = models.CharField(max_length=100, null=True, blank=True, help_text="Unique identifier for tracking construction across all modules")
    state = FSMField(max_length=20, null=False, choices=ApplicationStatus.choices, default=ApplicationStatus.INITIATED)
    land_certificate = models.ForeignKey("file.Attachment", related_name="land_certificate_application", on_delete=models.SET_NULL, null=True, blank=True)
    site_condition_plan = models.ForeignKey("file.Attachment", related_name="site_condition_plan_application", on_delete=models.SET_NULL, null=True, blank=True)
    construction_clearance = models.ForeignKey("file.Attachment", related_name="construction_clearance_application", on_delete=models.SET_NULL, null=True, blank=True)
    approval_certificate = models.ForeignKey("file.Attachment", related_name="approval_certificate_application", on_delete=models.SET_NULL, null=True, blank=True)
    region = models.ForeignKey(Region, null=True, on_delete=models.SET_NULL, blank=True, unique=False)
    dzongkhag = models.ForeignKey(Dzongkhag, null=True, on_delete=models.SET_NULL, blank=True, unique=False)
    thromde = models.ForeignKey(Thromde, null=True, on_delete=models.SET_NULL, blank=True, unique=False)
    gewog = models.ForeignKey(Gewog, null=True, on_delete=models.SET_NULL, blank=True, unique=False)
    village = models.ForeignKey(Village, null=True, on_delete=models.SET_NULL, blank=True, unique=False)
    purpose = models.CharField(max_length=100, null=True, blank=True, choices=Purpose.choices)
    development_type = models.CharField(max_length=100, null=True, blank=True, choices=DevelopmentType.choices)
    task_pools = GenericRelation(TaskPool, related_query_name="application")
    schedule_start_date = models.DateField(null=True)
    schedule_end_date = models.DateField(null=True)
    schedule_remarks = models.TextField(null=True)
    approval_remarks = models.TextField(null=True)
    rejection_remarks = models.TextField(null=True)
    visit_remarks = models.TextField(null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    consent_required = models.BooleanField(default=False)
    payments = GenericRelation(Payment, related_query_name="application")
    site_photos = models.ManyToManyField("file.Attachment", related_name="site_photos", blank=True)
    other_files = models.ManyToManyField("file.Attachment", related_name="other_file_application", blank=True)

    def __str__(self):
        return str(self.__dict__)

    class Meta:
        indexes = [
            models.Index(fields=["created_at"]),
            models.Index(fields=["state"]),
            models.Index(fields=["dzongkhag"]),
            models.Index(fields=["gewog"]),
            models.Index(fields=["construction_id"]),
        ]


class Applicant(models.Model, applicant.Transition):
    cid = models.CharField(null=True, max_length=20)
    thram_no = models.CharField(null=True, max_length=20)
    plot_no = models.CharField(null=True, max_length=20)
    plot_area_unit = models.CharField(null=True, max_length=20)
    plot_net_area = models.FloatField(null=True, max_length=20)
    plot_status = models.CharField(null=True, blank=True, max_length=30)
    plot_status_reason = models.TextField(null=True, blank=True)
    ownership_type = models.CharField(null=True, max_length=50)
    owner_name = models.CharField(null=True, max_length=50)
    land_type = models.CharField(null=True, max_length=50)
    prescient_code = models.CharField(null=True, max_length=50)
    land_location_flag = models.CharField(null=True, max_length=5)
    demkhong_name = models.CharField(null=True, max_length=50, blank=True)
    gewog_thromde = models.CharField(null=True, max_length=50)
    dzongkhag_thromde = models.CharField(null=True, max_length=50)
    application = models.ForeignKey(Application, related_name="applicants", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    state = FSMField(max_length=20, null=False, choices=ApplicantStatus.choices, default=ApplicantStatus.INITIATED)
    email = models.EmailField(null=True, blank=True)
    phone_no = models.CharField(null=True, max_length=20)
    plot_map = models.JSONField(default=list, blank=True)

    def __str__(self):
        return str(self.__dict__)


class ReviewQuestion(models.Model):
    text = models.TextField(null=False)
    user = models.ForeignKey(User, null=True, blank=True, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)


class ReviewAnswer(models.Model):
    text = models.TextField(null=True, blank=True)
    user = models.ForeignKey(User, null=False, on_delete=models.CASCADE)
    review_question = models.ForeignKey(ReviewQuestion, related_name="review_answers", on_delete=models.CASCADE)
    application = models.ForeignKey(Application, related_name="review_answers", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)


class SiteQuestion(models.Model):
    text = models.TextField(null=False)
    options = ArrayField(models.CharField(max_length=100), null=True)
    user = models.ForeignKey(User, null=True, blank=True, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)


class SiteAnswer(models.Model):
    text = models.TextField(null=False)
    user = models.ForeignKey(User, null=False, on_delete=models.CASCADE)
    remarks = models.TextField(null=True, blank=True)
    site_question = models.ForeignKey(SiteQuestion, related_name="site_answers", on_delete=models.CASCADE)
    application = models.ForeignKey(Application, related_name="site_answers", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.__dict__)


class PermitChecklist(models.Model):
    application = models.ForeignKey(Application, related_name="permit_checklists", on_delete=models.CASCADE)
    checklist = models.ForeignKey("information.Checklist", related_name="permit_checklists", on_delete=models.CASCADE)
    attachment = models.ForeignKey("file.Attachment", related_name="permit_checklists", on_delete=models.CASCADE, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
