from os import write
from rest_framework import serializers
from file.models import Attachment
from file.serializers import AttachmentSerializer
from .models import Application, Applicant, PermitChecklist, ReviewQuestion, ReviewAnswer, SiteQuestion, SiteAnswer
from drf_writable_nested.serializers import WritableNestedModelSerializer
from payment.serializers import PaymentSerializer
from information.models import Inquiry, Checklist


class ApplicantSerializer(serializers.ModelSerializer):
    plot_no = serializers.CharField(required=True)
    cid = serializers.CharField(required=True)
    thram_no = serializers.CharField(required=True)

    class Meta:
        model = Applicant
        fields = [
            "id",
            "cid",
            "thram_no",
            "plot_no",
            "plot_area_unit",
            "plot_net_area",
            "plot_status",
            "plot_status_reason",
            "ownership_type",
            "owner_name",
            "land_type",
            "prescient_code",
            "land_location_flag",
            "demkhong_name",
            "gewog_thromde",
            "dzongkhag_thromde",
            "application_id",
            "created_at",
            "email",
            "phone_no",
            "state",
            "plot_map",
        ]


class ApplicantListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Applicant
        fields = ["id", "owner_name", "thram_no", "plot_no", "ownership_type", "created_at"]


class PermitChecklistSerializer(serializers.ModelSerializer):
    attachment_file = serializers.FileField(source="attachment.file", read_only=True)
    checklist_id = serializers.PrimaryKeyRelatedField(queryset=Checklist.objects.all(), source="checklist", required=True)
    attachment_id = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="attachment", required=False)

    class Meta:
        model = PermitChecklist
        fields = ["id", "checklist_id", "attachment_id", "attachment_file", "description", "created_at", "updated_at"]

    def validate(self, attrs):
        if attrs["checklist"].type == "file" and not attrs.get("attachment"):
            raise serializers.ValidationError({"error": "You need to provide attachment_id for this checklist."})
        return super().validate(attrs)


class ApplicationSerializer(WritableNestedModelSerializer):
    applicants = ApplicantSerializer(many=True, required=True)
    serial_no = serializers.CharField(required=False)
    inquiry_id = serializers.PrimaryKeyRelatedField(queryset=Inquiry.objects.all(), source="inquiry", required=False)
    land_certificate_id = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="land_certificate", required=True)
    site_condition_plan_id = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="site_condition_plan", required=True)
    construction_clearance_id = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="construction_clearance", required=False)
    approval_certificate_id = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="approval_certificate", required=False)
    land_certificate_file = serializers.FileField(source="land_certificate.file", read_only=True)
    site_condition_plan_file = serializers.FileField(source="site_condition_plan.file", read_only=True)
    construction_clearance_file = serializers.FileField(source="construction_clearance.file", read_only=True)
    approval_certificate_file = serializers.FileField(source="approval_certificate.file", read_only=True)
    payments = PaymentSerializer(many=True, read_only=True)
    permit_checklists = PermitChecklistSerializer(many=True, required=False)
    site_photo_ids = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="site_photos", many=True, required=False, write_only=True)
    site_photos = AttachmentSerializer(many=True, read_only=True, allow_empty=True)
    other_file_ids = serializers.PrimaryKeyRelatedField(queryset=Attachment.objects.all(), source="other_files", many=True, required=False, write_only=True)
    other_files = AttachmentSerializer(many=True, read_only=True, allow_empty=True)
    region_name = serializers.CharField(source="region.name", read_only=True)
    dzongkhag_name = serializers.CharField(source="dzongkhag.name", read_only=True)
    thromde_name = serializers.CharField(source="thromde.name", read_only=True)
    gewog_name = serializers.CharField(source="gewog.name", read_only=True)
    village_name = serializers.CharField(source="village.name", read_only=True)
    state_display = serializers.CharField(source="get_state_display", read_only=True)
    schedule_remarks = serializers.CharField(required=False)

    class Meta:
        model = Application
        fields = [
            "id",
            "applicant_type",
            "user_id",
            "serial_no",
            "inquiry_id",
            "state",
            "state_display",
            "land_certificate_id",
            "land_certificate_file",
            "schedule_remarks",
            "site_condition_plan_id",
            "site_condition_plan_file",
            "construction_clearance_id",
            "construction_clearance_file",
            "created_at",
            "schedule_start_date",
            "approval_remarks",
            "approval_certificate_id",
            "approval_certificate_file",
            "region_id",
            "region_name",
            "dzongkhag_id",
            "dzongkhag_name",
            "thromde_id",
            "thromde_name",
            "gewog_id",
            "gewog_name",
            "village_id",
            "village_name",
            "purpose",
            "development_type",
            "schedule_end_date",
            "rejection_remarks",
            "turn_around_time",
            "construction_id",
            "applicants",
            "payments",
            "permit_checklists",
            "site_photo_ids",
            "site_photos",
            "other_file_ids",
            "other_files",
        ]


class ApplicationListSerializer(WritableNestedModelSerializer):
    applicants = ApplicantListSerializer(many=True)
    state_display = serializers.CharField(source="get_state_display", read_only=True)

    class Meta:
        model = Application
        fields = ["id", "serial_no", "inquiry_id", "created_at", "state", "state_display", "turn_around_time", "construction_id", "applicants"]


class ReviewQuestionSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewQuestion
        fields = ["id", "text", "user_id", "created_at"]


class ReviewAnswerSerializer(serializers.ModelSerializer):
    review_question_text = serializers.CharField(source="review_question.text", read_only=True)
    review_question_id = serializers.PrimaryKeyRelatedField(queryset=ReviewQuestion.objects.all(), source="review_question", required=True)

    class Meta:
        model = ReviewAnswer
        fields = ["id", "text", "review_question_id", "review_question_text", "user_id", "created_at"]


class SiteQuestionSerializer(serializers.ModelSerializer):
    class Meta:
        model = SiteQuestion
        fields = ["id", "text", "user_id", "created_at", "options"]


class SiteAnswerSerializer(serializers.ModelSerializer):
    site_question_text = serializers.CharField(source="site_question.text", read_only=True)
    site_question_id = serializers.PrimaryKeyRelatedField(queryset=SiteQuestion.objects.all(), source="site_question", required=True)

    class Meta:
        model = SiteAnswer
        fields = ["id", "text", "remarks", "site_question_id", "site_question_text", "user_id", "created_at"]


class ApplicationRASerializer(WritableNestedModelSerializer):
    review_answers = ReviewAnswerSerializer(many=True)

    class Meta:
        model = Application
        fields = ["id", "review_answers"]

    def update(self, instance, validated_data):
        answers_data = validated_data.pop("review_answers", None)
        if answers_data is not None:
            for answer_data in answers_data:
                answer_data["user_id"] = self.context["request"].user.id
                ReviewAnswer.objects.create(application=instance, **answer_data)
        return instance


class ApplicationSASerializer(WritableNestedModelSerializer):
    site_answers = SiteAnswerSerializer(many=True)

    class Meta:
        model = Application
        fields = ["id", "site_answers"]

    def update(self, instance, validated_data):
        answers_data = validated_data.pop("site_answers", None)
        if answers_data is not None:
            for answer_data in answers_data:
                answer_data["user_id"] = self.context["request"].user.id
                SiteAnswer.objects.create(application=instance, **answer_data)
        return instance
