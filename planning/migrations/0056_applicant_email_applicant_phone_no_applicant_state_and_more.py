# Generated by Django 4.1.7 on 2025-03-17 08:22

from django.db import migrations, models
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('planning', '0055_alter_applicant_cid_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='applicant',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='applicant',
            name='phone_no',
            field=models.CharField(max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='applicant',
            name='state',
            field=django_fsm.FSMField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='application',
            name='state',
            field=django_fsm.FSMField(choices=[('draft', 'Draft'), ('pending_consent', 'Pending Consent'), ('initiated', 'Initiated'), ('requested', 'Requested'), ('reviewed', 'Reviewed'), ('scheduled', 'Scheduled'), ('visited', 'Visited'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('resubmitted', 'Re-submitted'), ('pending_payment', 'Pending Payment'), ('closed', 'Closed')], default='initiated', max_length=20),
        ),
    ]
