# Generated by Django 4.1.7 on 2025-03-14 09:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('planning', '0054_application_approval_certificate_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='applicant',
            name='cid',
            field=models.CharField(max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='applicant',
            name='dzongkhag_thromde',
            field=models.Char<PERSON>ield(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='applicant',
            name='gewog_thromde',
            field=models.Char<PERSON>ield(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='applicant',
            name='land_location_flag',
            field=models.CharField(max_length=5, null=True),
        ),
        migrations.AlterField(
            model_name='applicant',
            name='land_type',
            field=models.Char<PERSON><PERSON>(max_length=50, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='applicant',
            name='owner_name',
            field=models.Char<PERSON>ield(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='applicant',
            name='ownership_type',
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='applicant',
            name='plot_area_unit',
            field=models.CharField(max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='applicant',
            name='plot_net_area',
            field=models.FloatField(max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='applicant',
            name='plot_no',
            field=models.CharField(max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='applicant',
            name='prescient_code',
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='applicant',
            name='thram_no',
            field=models.CharField(max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='application',
            name='nature',
            field=models.CharField(choices=[('urban', 'Urban'), ('rural', 'Rural')], default='rural', max_length=50, null=True),
        ),
    ]
