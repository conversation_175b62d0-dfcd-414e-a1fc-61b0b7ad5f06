# Generated by Django 4.1.7 on 2025-03-13 11:20

from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('information', '0002_alter_inquiry_state'),
        ('planning', '0052_delete_planningtaskpool'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='application',
            name='approval_certificate',
        ),
        migrations.RemoveField(
            model_name='application',
            name='construction_clearance',
        ),
        migrations.RemoveField(
            model_name='application',
            name='land_certificate',
        ),
        migrations.RemoveField(
            model_name='application',
            name='site_condition_plan',
        ),
        migrations.AddField(
            model_name='applicant',
            name='plot_status',
            field=models.CharField(blank=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name='applicant',
            name='plot_status_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='application',
            name='inquiry',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='application', to='information.inquiry'),
        ),
        migrations.AlterField(
            model_name='applicant',
            name='application',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applicants', to='planning.application'),
        ),
        migrations.AlterField(
            model_name='application',
            name='state',
            field=django_fsm.FSMField(choices=[('draft', 'Draft'), ('requested', 'Requested'), ('reviewed', 'Reviewed'), ('scheduled', 'Scheduled'), ('visited', 'Visited'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('resubmitted', 'Re-submitted'), ('pending_payment', 'Pending Payment'), ('closed', 'Closed')], default='requested', max_length=20),
        ),
    ]
