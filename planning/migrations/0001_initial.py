# Generated by Django 4.1 on 2023-01-26 11:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Application",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "applicant_type",
                    models.CharField(
                        choices=[("own", "Own"), ("other", "Other")], max_length=50
                    ),
                ),
                ("serial_no", models.CharField(default=1674732127923, max_length=50)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("requested", "Requested"),
                            ("visited", "Visited"),
                            ("approved", "Approved"),
                        ],
                        default="requested",
                        max_length=20,
                    ),
                ),
                ("land_certificate", models.FileField(null=True, upload_to="")),
                ("site_condition_plan", models.FileField(null=True, upload_to="")),
                ("construction_clearance", models.FileField(null=True, upload_to="")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="applications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Applicant",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("cid", models.CharField(max_length=20)),
                ("thram_no", models.CharField(max_length=20)),
                ("plot_no", models.CharField(max_length=20)),
                ("plot_area_unit", models.CharField(max_length=20)),
                ("plot_net_area", models.FloatField(max_length=20)),
                ("ownership_type", models.CharField(max_length=50)),
                ("owner_name", models.CharField(max_length=50)),
                ("land_type", models.CharField(max_length=50)),
                ("prescient_code", models.CharField(max_length=50)),
                ("land_location_flag", models.CharField(max_length=5)),
                ("demkhong_name", models.CharField(max_length=50)),
                ("gewog_thromde", models.CharField(max_length=50)),
                ("dzongkhag_thromde", models.CharField(max_length=50)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "application",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="planning.application",
                    ),
                ),
            ],
        ),
    ]
