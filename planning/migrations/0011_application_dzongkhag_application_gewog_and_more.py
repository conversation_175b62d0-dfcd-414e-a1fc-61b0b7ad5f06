# Generated by Django 4.1.7 on 2023-03-13 12:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("address", "0001_initial"),
        ("planning", "0010_alter_application_serial_no"),
    ]

    operations = [
        migrations.AddField(
            model_name="application",
            name="dzongkhag",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="address.dzongkhag",
            ),
        ),
        migrations.AddField(
            model_name="application",
            name="gewog",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="address.gewog",
            ),
        ),
        migrations.AddField(
            model_name="application",
            name="village",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="address.village",
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="application",
            name="serial_no",
            field=models.Char<PERSON><PERSON>(default=1678709254472, max_length=50),
        ),
    ]
