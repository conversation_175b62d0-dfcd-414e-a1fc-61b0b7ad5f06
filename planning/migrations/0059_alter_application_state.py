# Generated by Django 4.1.7 on 2025-03-25 20:00

from django.db import migrations
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('planning', '0058_alter_applicant_state'),
    ]

    operations = [
        migrations.AlterField(
            model_name='application',
            name='state',
            field=django_fsm.FSMField(choices=[('draft', 'Draft'), ('pending_consent', 'Pending Consent'), ('initiated', 'Initiated'), ('requested', 'Requested'), ('reviewed', 'Reviewed'), ('scheduled', 'Scheduled'), ('visited', 'Visited'), ('forwarded', 'Forwarded'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('pending_change', 'Pending Change'), ('resubmitted', 'Re-submitted'), ('pending_payment', 'Pending Payment'), ('closed', 'Closed')], default='initiated', max_length=20),
        ),
    ]
