# Generated by Django 4.1.7 on 2025-03-13 11:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('file', '0002_attachment_name_attachment_size_attachment_type'),
        ('planning', '0053_remove_application_approval_certificate_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='application',
            name='approval_certificate',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approval_certificate_application', to='file.attachment'),
        ),
        migrations.AddField(
            model_name='application',
            name='construction_clearance',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='construction_clearance_application', to='file.attachment'),
        ),
        migrations.AddField(
            model_name='application',
            name='land_certificate',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='land_certificate_application', to='file.attachment'),
        ),
        migrations.AddField(
            model_name='application',
            name='site_condition_plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='site_condition_plan_application', to='file.attachment'),
        ),
    ]
