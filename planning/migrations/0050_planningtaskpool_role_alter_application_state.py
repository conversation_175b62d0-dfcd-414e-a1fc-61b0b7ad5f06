# Generated by Django 4.1.7 on 2024-02-03 11:46

from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0025_alter_user_username'),
        ('planning', '0049_alter_reviewquestion_user'),
    ]

    operations = [
        migrations.AddField(
            model_name='planningtaskpool',
            name='role',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='user.role'),
        ),
        migrations.AlterField(
            model_name='application',
            name='state',
            field=django_fsm.FSMField(choices=[('requested', 'Requested'), ('reviewed', 'Reviewed'), ('scheduled', 'Scheduled'), ('visited', 'Visited'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('resubmitted', 'Re-submitted'), ('closed', 'Closed')], default='requested', max_length=20),
        ),
    ]
