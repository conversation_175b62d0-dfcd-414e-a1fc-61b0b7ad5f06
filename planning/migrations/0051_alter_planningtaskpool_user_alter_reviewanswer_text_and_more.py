# Generated by Django 5.0.3 on 2024-05-15 19:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('planning', '0050_planningtaskpool_role_alter_application_state'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='planningtaskpool',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='planning_task_pools', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='reviewanswer',
            name='text',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='siteanswer',
            name='remarks',
            field=models.TextField(blank=True, null=True),
        ),
    ]
