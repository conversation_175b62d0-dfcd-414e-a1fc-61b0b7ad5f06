# Generated by Django 4.1.7 on 2023-09-18 13:35

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('planning', '0043_application_thromde_alter_application_serial_no'),
    ]

    operations = [
        migrations.AddField(
            model_name='planningtaskpool',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='planningtaskpool',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='application',
            name='serial_no',
            field=models.CharField(default=1695044121279, max_length=50),
        ),
    ]
