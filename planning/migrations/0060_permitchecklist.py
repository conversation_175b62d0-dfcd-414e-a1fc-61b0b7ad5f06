# Generated by Django 4.1.7 on 2025-04-22 13:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('file', '0002_attachment_name_attachment_size_attachment_type'),
        ('information', '0011_alter_inquiry_checklists'),
        ('planning', '0059_alter_application_state'),
    ]

    operations = [
        migrations.CreateModel(
            name='PermitChecklist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('application', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='permit_checklists', to='planning.application')),
                ('attachment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='permit_checklists', to='file.attachment')),
                ('checklist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='permit_checklists', to='information.checklist')),
            ],
        ),
    ]
