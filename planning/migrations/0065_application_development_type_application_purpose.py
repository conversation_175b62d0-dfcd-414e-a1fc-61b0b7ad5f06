# Generated by Django 4.1.7 on 2025-06-20 08:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('planning', '0064_application_region'),
    ]

    operations = [
        migrations.AddField(
            model_name='application',
            name='development_type',
            field=models.CharField(blank=True, choices=[('small', 'Small'), ('standard', 'Standard'), ('comprehensive', 'Comprehensive')], default='small', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='application',
            name='purpose',
            field=models.CharField(blank=True, choices=[('building_construction', 'Building Construction'), ('reuse_building', 'Reuse Building'), ('demolition_of_building', 'Demolition of Building'), ('other', 'Other')], default='building_construction', max_length=100, null=True),
        ),
    ]
