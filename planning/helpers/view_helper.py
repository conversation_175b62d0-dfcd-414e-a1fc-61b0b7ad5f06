from address.models import Dzongkhag, Thromde, Village
from common.helpers import fetch_address
import time
from django.db import transaction
from rest_framework.exceptions import ValidationError
from common.utils import generate_construction_id
from planning.models import Application


def fetch_thromde(dzo_thrm):
    return fetch_address(Thromde.objects.all(), dzo_thrm)


def fetch_dzo(dzo_thrm):
    return fetch_address(Dzongkhag.objects.all(), dzo_thrm)


def perform_save(self, serializer):
    with transaction.atomic():
        applicants = serializer.validated_data.get("applicants", [])
        inquiry = serializer.validated_data.get("inquiry", None)
        applicant = applicants[0]
        llflag, dzo_thrm, gewog_thrm = [applicant["land_location_flag"], applicant["dzongkhag_thromde"], applicant["gewog_thromde"]]
        application_id = self.kwargs.get("pk")
        application = Application.objects.get(id=application_id) if application_id else None
        if not getattr(application, "dzongkhag", None):
            dzo = fetch_dzo(dzo_thrm)
            thromde = (dzo.thromdes.first() if dzo else fetch_thromde(gewog_thrm) or fetch_thromde(dzo_thrm)) if llflag == "U" else None
            if not dzo:
                if thromde:
                    dzo = thromde.dzongkhag
                if not dzo:
                    raise ValidationError({"error": f"Couldn't find dzongkhag with name {dzo_thrm}"})
            gewog = fetch_address(dzo.gewogs.all(), gewog_thrm)
            village = Village.objects.filter(name=gewog_thrm, gewog_id=getattr(gewog, "id", None)).first()
            serializer.validated_data["region_id"] = getattr(dzo.region, "id", None)
            serializer.validated_data["dzongkhag_id"] = dzo.id
            serializer.validated_data["thromde_id"] = getattr(thromde, "id", None)
            serializer.validated_data["gewog_id"] = getattr(gewog, "id", None)
            serializer.validated_data["village_id"] = getattr(village, "id", None)
        if not getattr(application, "serial_no", None):
            serializer.validated_data["serial_no"] = f"CASP{int(time.time() * 1000)}"
        if not getattr(application, "user_id", None):
            serializer.validated_data["user_id"] = self.request.user.id
        if not getattr(application, "construction_id", None):
            if inquiry and inquiry.construction_id:
                serializer.validated_data["construction_id"] = inquiry.construction_id
            else:
                prefix = f"{dzo_thrm[:3].upper()}_{applicant['plot_no'].upper()}_"
                serializer.validated_data["construction_id"] = generate_construction_id(prefix, "application")
        consent_required = True if serializer.validated_data["applicant_type"] == "other" or len(applicants) > 1 else False
        serializer.validated_data["consent_required"] = consent_required
