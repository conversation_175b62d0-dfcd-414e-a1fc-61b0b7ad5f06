from rest_framework.exceptions import ValidationError
from notifications.signals import notify
from cas_api.middleware.current_request import get_current_request
from planning.mailers import notify_requested
from common.helpers import determine_assignee_user


def create_notification(application, recipient, action):
    url = f"/services/planning-permit/{application.id}"
    notify.send(getattr(application, "by", application.user), recipient=recipient, verb=action, action_object=application, target=application, url=url)
    notify.send(getattr(application, "by", application.user), recipient=application.user, verb=action, action_object=application, target=application, url=url)


def assign_reviewer(application):
    by = getattr(get_current_request(), "user", None)
    if application.thromde:
        user, role = determine_assignee_user("cup", dzo=application.dzongkhag, thrm=application.thromde)
        application.nature = "urban"
    else:
        user, role = determine_assignee_user("dce", dzo=application.dzongkhag)
        application.nature = "rural"
    application.save()
    application.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
    notify_requested.delay(application.id)
    create_notification(application, user, "assigned")
