from rest_framework.exceptions import ValidationError
from common.enums import LandNature


class Helper:
    def can_schedule(self, current_user):
        current_role = current_user.current_role
        if (current_role.name == "up" and self.nature == "urban") or (current_role.name == "dro" and self.nature == "rural"):
            return True
        else:
            return False

    def can_visit(self, current_user):
        current_role = current_user.current_role
        if (current_role.name == "up" and self.nature == "urban") or (current_role.name == "dro" and self.nature == "rural"):
            return True
        else:
            return False

    def can_approve(self, current_user):
        current_role = current_user.current_role
        if (current_role.name in ["cup", "up"] and self.nature == "urban") or (current_role.name in ["dce", "dro"] and self.nature == "rural"):
            return True
        else:
            return False

    def can_reject(self, current_user):
        return True

    def can_review(self, current_user):
        return self.can_visit(current_user)

    def can_close(self, current_user):
        return True

    def can_change(self, current_user):
        return True

    def can_forward(self, current_user):
        current_role = current_user.current_role
        if (current_role.name == "cup" and self.nature == "urban") or (current_role.name == "dce" and self.nature == "rural"):
            return True
        else:
            raise ValidationError({"error": "Not permitted to perform this action."})

    @property
    def turn_around_time(self):
        from django.db import models
        import datetime

        turnaround_time = self.task_pools.aggregate(earliest_created_at=models.Min("created_at"), latest_created_at=models.Max("created_at"))
        return str((turnaround_time["latest_created_at"] or datetime.date.today()) - (turnaround_time["earliest_created_at"] or datetime.date.today()))

    @property
    def nature_text(self):
        return dict(LandNature.choices)[self.nature]

    def adm_user(self):
        from user.models import User

        user = None
        if self.nature == "urban":
            user = User.objects.filter(roles__name="tadm", profile__dzongkhag=self.dzongkhag, profile__thromde=self.thromde).first()
        elif self.nature == "rural":
            user = User.objects.filter(roles__name="dadm", profile__dzongkhag=self.dzongkhag).first()

        if not user:
            name = self.thromde.name if self.nature == "urban" else self.dzongkhag.name
            raise ValidationError({"error": f"No administrator user found for {name}"})
        else:
            return user

    def pay(self, payment=None):
        self.approve({}, by=self.user)
        self.save()
