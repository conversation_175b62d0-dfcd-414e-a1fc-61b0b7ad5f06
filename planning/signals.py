from django.db.models.signals import post_save
from django.dispatch import receiver
from django_fsm.signals import post_transition
from cas_api.middleware.current_request import get_current_request
from common.helpers import adm_user, determine_assignee_user, generate_payment, is_task_completed
from common.models import TaskPool
from planning.helpers.transition_helper import assign_reviewer, create_notification
from planning.models import Applicant, Application
from planning.mailers import (
    notify_forwarded,
    notify_scheduled,
    notify_approved,
    notify_rejected,
    notify_resubmitted,
    notify_consent_request,
    notify_consent_provided,
    notify_change,
    notify_payment,
)
from rest_framework.exceptions import ValidationError


@receiver(post_save, sender=Application)
def applicantion_post_save(sender, instance, created, **kwargs):
    if created:
        if instance.consent_required:
            instance.consent(by=instance.user)
        else:
            instance.initial(by=instance.user)
            assign_reviewer(instance)
        instance.save()
    elif not created:
        by = getattr(instance, "by", None) or getattr(get_current_request(), "user", None)
        if getattr(by, "id", None) == instance.user_id and instance.state == "pending_change":
            change_tasks = instance.task_pools.filter(state="pending_change")
            instance.resubmit({}, by=by)
            instance.save()
            for task in change_tasks:
                task.assign({}, by=by)
                task.save()
                notify_resubmitted.delay(instance.id, task.user.id)
                create_notification(instance, task.user, "resubmited")


@receiver(post_transition, sender=Application)
def application_post_transition(sender, instance, name, source, target, **kwargs):
    by = getattr(get_current_request(), "user", None)
    if name == "visit":
        notify_forwarded.delay(instance.id)
    if name == "approve":
        notify_approved.delay(instance.id)
    if name == "reject":
        notify_rejected.delay(instance.id)
    if name == "schedule":
        notify_scheduled.delay(instance.id)
    if name == "forward":
        task = instance.task_pools.filter(user=by, state__in=["in_progress"]).first()
        if task:
            task.approve({}, by=by)
            task.save()
        planner_id = getattr(instance, 'data', {}).get("planner_id")
        if planner_id:
            user, role = (
                determine_assignee_user("up", user_id=planner_id, thrm=instance.thromde)
                if instance.nature == "urban"
                else determine_assignee_user("dro", user_id=planner_id, dzo=instance.dzongkhag)
            )
        else:
            user, role = determine_assignee_user("up", thrm=instance.thromde) if instance.nature == "urban" else determine_assignee_user("dro", dzo=instance.dzongkhag)
        instance.task_pools.create(user=user, role=role, by=by, by_role=getattr(by, "current_role", None))
        notify_forwarded.delay(instance.id)
    if name == "change":
        notify_change.delay(instance.id)
    if name == "payment":
        setting = adm_user(instance).settings.filter(key="pp_fee").first()
        if not setting:
            raise ValidationError({"error": "The fee for planning permit is not set. Please contact your respective admin to set the fee."})
        amount = float(setting.value) if setting.value not in (None, "") else 0.0
        if amount > 0:
            generate_payment("pp", amount, instance)
            notify_payment.delay(instance.id)
        else:
            instance.pay()
    create_notification(instance, instance.user, target)


@receiver(post_save, sender=Applicant)
def applicant_post_save(sender, instance, created, **kwargs):
    by = getattr(instance, "by", None) or getattr(get_current_request(), "user", None)
    application = instance.application
    if created:
        instance.initial(by=by)
        instance.save()
        if application.consent_required:
            notify_consent_request.delay(instance.id)
        else:
            instance.approve(by=by)
            instance.save()
    if all(task.state == "approved" for task in application.applicants.all()):
        if application.consent_required:
            application.initial(by=by)
            assign_reviewer(application)
            application.save()


@receiver(post_transition, sender=Applicant)
def applicant_post_transition(sender, instance, name, source, target, **kwargs):
    application = instance.application
    if name == "approve":
        if application.consent_required:
            notify_consent_provided.delay(instance.id)
    create_notification(application, application.user, target)


@receiver(post_transition, sender=TaskPool)
def task_pool_transition(sender, instance, name, source, target, **kwargs):
    if not isinstance(instance.poolable, Application):
        return
    application = instance.poolable
    data = getattr(instance, "data", {})
    if name == "approve":
        if instance.role.name in ["dro", "up"]:
            if instance.role.name == "up":
                task = application.task_pools.filter(role__name="cup").first()
            else:
                task = application.task_pools.filter(role__name="dce").first()
            task.assign(data, by=instance.user)
            task.save()
            application.site_photos.set(data.get("site_photo_ids", []))
            application.save()
            create_notification(application, task.user, target)
        if instance.role.name == "dce" and is_task_completed(application, "dro"):
            application.payment(instance.data, instance.user)
            application.save()
            create_notification(application, application.user, target)
        if instance.role.name == "cup" and is_task_completed(application, "up"):
            application.payment(instance.data, instance.user)
            application.save()
            create_notification(application, application.user, target)
    elif name == "change":
        application.change(instance.data, instance.user)
        application.save()
    elif name == "reject":
        application.reject({"remarks": instance.remarks}, instance.user)
        application.save()
    create_notification(application, instance.user, target)
