import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase
from django.core.management import call_command
from user.factories import UserFactory, ProfileFactory
from address.factories import DzongkhagFactory, GewogFactory, ThromdeFactory, VillageFactory
from django.urls import path, include
from user.models import Role
from planning.models import Application, Applicant
from planning.factories import ApplicationFactory, ApplicantFactory
from information.factories import InquiryFactory


class ApplicationTransitionTest(APITestCase, URLPatternsTestCase):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("planning.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.dzo = DzongkhagFactory(name="Mongar")
        self.gewog = GewogFactory(name="Mongar", dzongkhag=self.dzo)
        self.thromde = ThromdeFactory(name="Mongar", dzongkhag=self.dzo)
        self.village = VillageFactory(name="Mongar", gewog=self.gewog)

        # Create regular user
        self.user = UserFactory(username="user", roles=Role.objects.filter(name__in=["user"]).values_list("id", flat=True))
        ProfileFactory(user=self.user, dzongkhag=self.dzo)

        # Create municipal engineer
        self.me = UserFactory(username="dro", roles=Role.objects.filter(name__in=["dro"]).values_list("id", flat=True))
        ProfileFactory(user=self.me, dzongkhag=self.dzo)

        # Create urban planner
        self.up = UserFactory(username="up", roles=Role.objects.filter(name__in=["up"]).values_list("id", flat=True))
        ProfileFactory(user=self.up, dzongkhag=self.dzo, thromde=self.thromde)

        # Get token for ME
        res = self.client.post(reverse("token_obtain_pair"), {"username": self.me.username, "password": "Dcpl@123"})
        self.me_token = json.loads(res.content)["data"]["access"]

        # Get token for UP
        res = self.client.post(reverse("token_obtain_pair"), {"username": self.up.username, "password": "Dcpl@123"})
        self.up_token = json.loads(res.content)["data"]["access"]

        # Create inquiry
        self.inquiry = InquiryFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog, village=self.village, land_location_flag="R", nature="rural")

        # Create rural application
        self.rural_application = ApplicationFactory(user=self.user, dzongkhag=self.dzo, gewog=self.gewog, village=self.village, nature="rural", inquiry=self.inquiry)
        self.rural_applicant = ApplicantFactory(application=self.rural_application)

        # Create urban application
        self.urban_application = ApplicationFactory(user=self.user, dzongkhag=self.dzo, thromde=self.thromde, nature="urban")
        self.urban_applicant = ApplicantFactory(application=self.urban_application)

    def test_invalid_transition(self):
        """Test that invalid transitions return appropriate error"""
        data = {"action": "invalid_action"}
        url = reverse("planning-transitions", kwargs={"pk": self.rural_application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.me_token}"
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertEqual(json.loads(response.content)["error"], "Invalid transition")

    def test_rural_application_flow(self):
        """Test complete flow for rural application"""
        url = reverse("planning-transitions", kwargs={"pk": self.rural_application.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.me_token}"

        # Test scheduling
        data = {"action": "schedule", "schedule_start_date": "2025-04-10", "schedule_end_date": "2025-04-11", "schedule_remarks": "Site visit scheduled"}
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "scheduled")

        # Test visiting
        data = {"action": "visit", "visit_remarks": "Site visited and verified"}
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "visited")

        # Test approving
        data = {"action": "approve", "approval_remarks": "Application approved"}
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "approved")

    def test_urban_application_flow(self):
        """Test complete flow for urban application"""
        url = reverse("planning-transitions", kwargs={"pk": self.urban_application.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.up_token}"

        # Test scheduling
        data = {"action": "schedule", "schedule_start_date": "2025-04-10", "schedule_end_date": "2025-04-11", "schedule_remarks": "Site visit scheduled"}
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "scheduled")

        # Test visiting
        data = {"action": "visit", "visit_remarks": "Site visited and verified"}
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "visited")

        # Test approving
        data = {"action": "approve", "approval_remarks": "Application approved"}
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "approved")

    def test_rejection_flow(self):
        """Test rejection flow"""
        url = reverse("planning-transitions", kwargs={"pk": self.rural_application.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.me_token}"

        # Test rejection without remarks
        data = {"action": "reject"}
        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test rejection with remarks
        data = {"action": "reject", "rejection_remarks": "Missing documents"}
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_data["data"]["state"], "rejected")
