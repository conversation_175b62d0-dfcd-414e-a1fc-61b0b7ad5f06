import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase
from django.core.management import call_command
from planning.models import *
from user.factories import UserFactory, ProfileFactory
from planning.factories import ApplicationFactory, ApplicantFactory
from address.factories import DzongkhagFactory, GewogFactory
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import path, include

from user.models import Role


class TransitionTest(APITestCase, URLPatternsTestCase):
    urlpatterns = [
        path("api/v1/", include("user.urls"), name="users"),
        path("api/v1/", include("planning.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        dzo = DzongkhagFactory(name="Mongar")
        gewog = GewogFactory(dzongkhag=dzo, name="Mongar")

        self.user = UserFactory.create(
            username="user",
            roles=Role.objects.filter(name__in=["thrmwsf"]).values_list(
                "id", flat=True
            ),
        )
        ProfileFactory(user=self.user, dzongkhag=dzo)

        self.planner = UserFactory.create(
            username="planner",
            roles=Role.objects.filter(name__in=["dpd", "upd"]).values_list(
                "id", flat=True
            ),
        )
        ProfileFactory(user=self.planner, dzongkhag=dzo)

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": self.planner.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.application = ApplicationFactory(
            user=self.user, dzongkhag=dzo, gewog=gewog
        )
        ApplicantFactory(application=self.application)

    def test_invalid_transition(self):
        data = {"action": "schedule"}
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertTrue("error" in response_data)
        self.assertEqual(response_data["error"], "Invalid transition")

    def test_review_not_permited(self):
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": self.user.username, "password": "Dcpl@123"},
        )
        token = json.loads(res.content)["data"]["access"]
        data = {"action": "review"}
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertTrue("error" in response_data)
        self.assertEqual(
            response_data["error"], "Not permitted to perform this action."
        )

    def test_review(self):
        data = {"action": "review"}
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "reviewed")

    def test_schedule_not_permited(self):
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": self.user.username, "password": "Dcpl@123"},
        )
        token = json.loads(res.content)["data"]["access"]
        data = {"action": "schedule"}
        self.application.state = "reviewed"
        self.application.save()
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertTrue("error" in response_data)
        self.assertEqual(
            response_data["error"], "Not permitted to perform this action."
        )

    def test_schedule_date_required(self):
        data = {
            "action": "schedule",
            "schedule_remarks": "This is my final schedules for the application.",
        }
        self.application.state = "reviewed"
        self.application.save()
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertTrue("error" in response_data)
        self.assertEqual(
            response_data["error"], "Schedule start and end date are required."
        )

    def test_schedule(self):
        data = {
            "action": "schedule",
            "schedule_start_date": "2022-05-12",
            "schedule_end_date": "2022-05-13",
            "schedule_remarks": "This is my final schedules for the application.",
        }
        self.application.state = "reviewed"
        self.application.save()
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "scheduled")

    def test_visit_not_permited(self):
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": self.user.username, "password": "Dcpl@123"},
        )
        token = json.loads(res.content)["data"]["access"]
        data = {"action": "visit"}
        self.application.state = "scheduled"
        self.application.save()
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertTrue("error" in response_data)
        self.assertEqual(
            response_data["error"], "Not permitted to perform this action."
        )

    def test_visit(self):
        data = {
            "action": "visit",
            "visit_remarks": "This is my final visit for the application.",
        }
        self.application.state = "scheduled"
        self.application.save()
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "visited")

    def test_approve_not_permited(self):
        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": self.user.username, "password": "Dcpl@123"},
        )
        token = json.loads(res.content)["data"]["access"]
        data = {"action": "approve"}
        self.application.state = "visited"
        self.application.save()
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {token}"
        response = self.client.put(url, data, format="json")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertTrue("error" in response_data)
        self.assertEqual(
            response_data["error"], "Not permitted to perform this action."
        )

    def test_approve(self):
        data = {
            "action": "approve",
            "approval_certificate": SimpleUploadedFile(
                "test.pdf", b"test file.", content_type="application/pdf"
            ),
        }
        self.application.state = "visited"
        self.application.save()
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "approved")

    def test_reject_remarks(self):
        data = {"action": "reject"}
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertTrue("error" in response_data)
        self.assertEqual(
            response_data["error"], "You should provide rejection remarks."
        )

    def test_reject(self):
        data = {"action": "reject", "rejection_remarks": "Some documents are missing"}
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "rejected")

    def test_close(self):
        data = {"action": "close"}
        url = reverse("transitions", kwargs={"pk": self.application.id})

        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(response_data["data"]["state"], "closed")
