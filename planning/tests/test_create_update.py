import json, time
from django.urls import include, path, reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient, URLPatternsTestCase
from building.factories import PermitFactory
from user.models import Role, User, Profile
from address.models import Dzongkhag
from django.core.management import call_command
from planning.models import *
from user.factories import UserFactory, ProfileFactory
from planning.factories import ApplicantFactory, ApplicationFactory
from address.factories import DzongkhagFactory, GewogFactory


class CreateUpdateTest(APITestCase, URLPatternsTestCase):
    urlpatterns = [
        path("api/v1/", include("planning.urls")),
        path("api/v1/", include("user.urls")),
    ]

    def setUp(self):
        call_command("loaddata", "seed/000_role.json")
        self.dzo = DzongkhagFactory(name="Mongar")
        self.gewog = GewogFactory(dzongkhag=self.dzo, name="Mongar")
        self.user = UserFactory.create(
            username="user",
            roles=Role.objects.filter(name__in=["thrmwsf"]).values_list(
                "id", flat=True
            ),
        )
        ProfileFactory(user=self.user, dzongkhag=self.dzo)

        self.planner = UserFactory.create(
            username="planner",
            roles=Role.objects.filter(name__in=["dpd", "upd"]).values_list(
                "id", flat=True
            ),
        )
        ProfileFactory(user=self.planner, dzongkhag=self.dzo)

        res = self.client.post(
            reverse("token_obtain_pair"),
            {"username": self.user.username, "password": "Dcpl@123"},
        )
        self.token = json.loads(res.content)["data"]["access"]
        self.data = {
            "applicant_type": "own",
            "applicant[cid]": "10709000999",
            "applicant[thram_no]": "TBA2383",
            "applicant[plot_no]": "2343",
            "applicant[plot_area_unit]": "sq.area",
            "applicant[plot_net_area]": "2",
            "applicant[ownership_type]": "family",
            "applicant[owner_name]": "Tashi Dendup",
            "applicant[land_type]": "Dry Land",
            "applicant[prescient_code]": "MD",
            "applicant[land_location_flag]": "R",
            "applicant[demkhong_name]": "Mongar",
            "applicant[gewog_thromde]": "Mongar",
            "applicant[dzongkhag_thromde]": "Mongar",
        }

    def test_planning_create(self):
        url = reverse("applications")
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.post(url, self.data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue("data" in response_data)

    def test_no_planner(self):
        url = reverse("applications")
        self.planner.delete()
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.post(url, self.data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertTrue("error" in response_data)

    def test_planning_udpate(self):
        application = ApplicationFactory(
            user=self.user,
            dzongkhag=self.dzo,
            gewog=self.gewog,
            serial_no=int(time.time() * 1000),
        )
        ApplicantFactory(application=application)
        url = reverse("applications", kwargs={"pk": application.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.put(url, self.data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)

    def test_planning_list(self):
        application = ApplicationFactory(
            user=self.user, dzongkhag=self.dzo, gewog=self.gewog
        )
        ApplicantFactory(application=application)
        application1 = ApplicationFactory(
            user=self.user, dzongkhag=self.dzo, gewog=self.gewog
        )
        ApplicantFactory(application=application1)
        application2 = ApplicationFactory(
            user=self.user, dzongkhag=self.dzo, gewog=self.gewog
        )
        ApplicantFactory(application=application2)
        url = reverse("applications")
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.get(url, self.data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertEqual(len(response_data["data"]), 3)

    def test_planning_detail(self):
        application = ApplicationFactory(
            user=self.user, dzongkhag=self.dzo, gewog=self.gewog
        )
        ApplicantFactory(application=application)
        PermitFactory(application=application, user=self.user)
        url = reverse("applications", kwargs={"pk": application.id})
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {self.token}"
        response = self.client.get(url, self.data, format="multipart")
        response_data = json.loads(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("data" in response_data)
        self.assertTrue("permit_id" in response_data["data"])
