from rest_framework import viewsets, generics
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from cas_api.services.ndi_service import process_ndi_info
from common.serializers import StateLogSerializer
from planning.helpers.view_helper import perform_save
from .serializers import *
from .models import Application, ReviewQuestion, ReviewAnswer, SiteQuestion, SiteAnswer
from rest_framework import generics
from rest_framework.decorators import action
from django_fsm import can_proceed
from planning.populators import Populator
from django_fsm_log.models import StateLog
from rest_framework.exceptions import ValidationError
from rest_framework.filters import SearchFilter
from django.db import transaction
from django_weasyprint import WeasyTemplateResponseMixin
import os
from common.helpers import adm_user
from datetime import timedelta


class ApplicationView(generics.ListCreateAPIView):
    filter_backends = [SearchFilter]
    search_fields = (
        "serial_no",
        "state",
        "applicant_type",
        "user__first_name",
        "user__last_name",
        "user__email",
        "user__current_role__description",
        "dzongkhag__name",
        "thromde__name",
        "gewog__name",
        "village__name",
        "applicants__cid",
        "applicants__thram_no",
        "applicants__plot_no",
        "applicants__owner_name",
        "applicants__land_type",
        "applicants__demkhong_name",
        "applicants__gewog_thromde",
        "applicants__dzongkhag_thromde",
    )

    def get_serializer_class(self):
        if self.request.method == "GET":
            return ApplicationListSerializer
        elif self.request.method == "POST" or self.request.method == "PUT":
            return ApplicationSerializer

    def create(self, request, *args, **kwargs):
        data = request.data
        if data.get("state") == "draft":
            applicants_data = data.pop("applicants", [])
            permit_checklists_data = data.pop("permit_checklists", [])
            site_photo_ids = data.pop("site_photo_ids", [])
            application_data = Application(**data, user=request.user)
            application = Application.objects.bulk_create([application_data])[0]
            application.site_photos.set(site_photo_ids)
            application.save()
            instances = []
            pcl = []
            for applicant_data in applicants_data:
                instances.append(Applicant(application=application, **applicant_data))
            for permit_checklist_data in permit_checklists_data:
                pcl.append(PermitChecklist(application=application, **permit_checklist_data))
            Applicant.objects.bulk_create(instances)
            PermitChecklist.objects.bulk_create(pcl)
            return Response(ApplicationSerializer(application).data, status=status.HTTP_201_CREATED)
        else:
            return super().create(request, *args, **kwargs)

    def perform_create(self, serializer):
        with transaction.atomic():
            try:
                plot_nos = [obj.get("plot_no") for obj in serializer.validated_data.get("applicants", [])]
                application = Application.objects.filter(applicants__plot_no__in=plot_nos).first()
                if application and application.state not in ["approved", "rejected"]:
                    raise ValidationError({"error": "The application with the same plot no already active and in the process."})
                perform_save(self, serializer)
                super().perform_create(serializer)
            except Exception as e:
                transaction.set_rollback(True)
                raise e

    def get_queryset(self):
        return Populator(self.request.user, self.request.query_params).populate()


class ApplicationPKView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ApplicationSerializer
    queryset = Application.objects.all()

    def perform_update(self, serializer):
        perform_save(self, serializer)
        super().perform_update(serializer)

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        data = request.data

        if data.get("state") == "draft":
            applicants_data = data.pop("applicants", [])
            Application.objects.filter(id=instance.id).update(**data)
            existing_applicants = {a.id: a for a in instance.applicants.filter(id__in=[a["id"] for a in applicants_data if "id" in a])}
            applicants_to_update = []
            applicants_to_create = []
            for applicant_data in applicants_data:
                applicant_id = applicant_data.get("id")
                if applicant_id and applicant_id in existing_applicants:
                    applicant = existing_applicants[applicant_id]
                    for field, value in applicant_data.items():
                        setattr(applicant, field, value)
                    applicants_to_update.append(applicant)
                else:
                    applicants_to_create.append(Applicant(application=instance, **applicant_data))
            if applicants_to_create:
                Applicant.objects.bulk_create(applicants_to_create)
            if applicants_to_update:
                Applicant.objects.bulk_update(applicants_to_update, fields=[f.name for f in Applicant._meta.fields if f.name != "id"])
            return Response(ApplicationSerializer(instance).data, status=status.HTTP_200_OK)
        else:
            return super().update(request, *args, **kwargs)


class StateMachineViewSet(viewsets.ModelViewSet):
    queryset = Application.objects.all()
    serializer_class = ApplicationSerializer

    @action(detail=True, methods=["put"])
    def transition(self, request, *args, **kwargs):
        with transaction.atomic():
            try:
                instance = get_object_or_404(Application, pk=kwargs.get("pk"))
                action = request.data.pop("action")
                if not hasattr(instance, action) or not can_proceed(getattr(instance, action)):
                    return Response({"error": "Invalid transition"}, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
                if getattr(instance, "can_" + action)(request.user):
                    if not action == "start" and instance.task_pools.filter(user=request.user, state="assigned").exists():
                        raise ValidationError({"error": "You need to start your task first."})
                    getattr(instance, action)(request.data, by=request.user)
                    instance.save()
                    serializer = ApplicationSerializer(instance)
                    return Response(serializer.data, status=status.HTTP_200_OK)
                else:
                    return Response({"error": "You are not permited to perform this action."}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                transaction.set_rollback(True)
                raise e


class PlanningViewSet(viewsets.ModelViewSet):
    queryset = Application.objects.all()
    serializer_class = ApplicationSerializer

    @action(detail=True, methods=["get"])
    def activity_logs(self, request, *args, **kwargs):
        application = get_object_or_404(Application, pk=kwargs.get("pk"))
        logs = StateLog.objects.for_(application)
        serializer = StateLogSerializer(logs, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"])
    def draft(self, request, *args, **kwargs):
        user = request.user
        application = user.applications.filter(state="draft").first()
        if not application:
            return Response({"error": "No draft application found."}, status=status.HTTP_404_NOT_FOUND)
        serializer = ApplicationSerializer(application)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["post"])
    def consent(self, request, *args, **kwargs):
        application = get_object_or_404(Application, pk=kwargs.get("pk"))
        if application.consent_required:
            try:
                jdata = request.data
                cid_data = jdata["data"]["requested_presentation"]["revealed_attrs"]
            except Exception as e:
                raise serializers.ValidationError({"error": "Invalid params."}, status.HTTP_400_BAD_REQUEST)
            data = {"cid": cid_data[os.getenv("NDI_IDENTITY")][0]["value"], "thread_id": jdata["data"]["thid"]}
            try:
                user = process_ndi_info(data)
            except Exception as e:
                raise serializers.ValidationError({"error": str(e)}, status.HTTP_400_BAD_REQUEST)
            if user is not None:
                applicant = application.applicants.filter(cid=user.cid).first()
                if applicant:
                    applicant.approve(by=user)
                    applicant.save()
                    return Response({"message": "You have successfully given your consent."}, status=status.HTTP_200_OK)
                else:
                    raise serializers.ValidationError({"error": "Applicant doesn't exist."}, status.HTTP_400_BAD_REQUEST)
            else:
                raise serializers.ValidationError({"error": "Invalid user information."}, status.HTTP_400_BAD_REQUEST)
        else:
            return Response({"message": "Consent not required for this application."}, status=status.HTTP_200_OK)


class ReviewQuestionViewSet(generics.ListAPIView):
    queryset = ReviewQuestion.objects.all()
    serializer_class = ReviewQuestionSerializer


class SiteQuestionViewSet(generics.ListAPIView):
    queryset = SiteQuestion.objects.all()
    serializer_class = SiteQuestionSerializer


class ReviewAnswerViewSet(generics.ListAPIView):
    serializer_class = ReviewAnswerSerializer

    def get_queryset(self):
        id = self.kwargs.get("pk")
        lists = ReviewAnswer.objects.filter(application_id=id)
        return lists

    def put(self, request, *args, **kwargs):
        application = get_object_or_404(Application, pk=kwargs.get("pk"))
        application.review_answers.all().delete()
        serializer = ApplicationRASerializer(application, data=request.data, context={"request": request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)


class SiteAnswerViewSet(generics.ListAPIView):
    serializer_class = SiteAnswerSerializer

    def get_queryset(self):
        id = self.kwargs.get("pk")
        lists = SiteAnswer.objects.filter(application_id=id)
        return lists

    def put(self, request, *args, **kwargs):
        application = get_object_or_404(Application, pk=kwargs.get("pk"))
        application.site_answers.all().delete()
        serializer = ApplicationSASerializer(application, data=request.data, context={"request": request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)


class PdfView(WeasyTemplateResponseMixin, generics.RetrieveAPIView):
    queryset = Application.objects.all()
    serializer_class = ApplicationSerializer
    template_name = "documents/pdfs/plannings/certificate.html"

    def get(self, request, *args, **kwargs):
        application = get_object_or_404(Application, pk=kwargs["pk"])
        if application.state != "approved":
            raise ValidationError({"error": "Application not approved."})
        finyear = application.created_at - timedelta(days=365)
        setting = adm_user(application).settings.filter(key="seal").first()
        seal = setting.file.file.url if setting else None
        context = {"application": application, "user": application.user, "finyear": finyear, "seal": seal}
        return self.render_to_response(context)
