.header {
    width: 100%;
}

.img {
    width: 85%;
}

.container {
    margin: 0 10% 0 10%;
}

.title-container {
    display: flex;
    justify-content: space-between;
}

.space-between {
    display: flex;
    width: 300px;
}

.titles {
    margin-right: 4%;
}

.font-700 {
    font-weight: 700;
}

.font-size-18 {
    font-size: 18px;
}

.sub-title {
    margin: 2% 0 2% 0;
}

.divider {
    width: 100%;
    height: 1px;
    background-color: #393535af;
}

.mt10 {
    margin-top: 10px;
}

.mb110 {
    margin-bottom: 110px;
}

.seal {
    width: 180px;
}

.col-md-4 {
    width: 25%;
}

.col-md-6 {
    width: 50%;
}

.col-md-12 {
    width: 100%;
}

tr {
    width: 100%;
}

.left0 {
    left: 0;
}

.right0 {
    right: 0;
}

.justify {
    text-align: justify;
}

.page-break-after {
    page-break-after: always;
}

.page-break-before {
    page-break-before: always;
}

.font-14-3 {
    font-size: 14.3px;
}

.text-center {
    text-align: center !important;
}

.header-dzo {
    margin-top: -27px;
    text-align: center;
    font-size: 14.3px;
}