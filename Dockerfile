# syntax=docker/dockerfile:1
# Comments are provided throughout this file to help you get started.
# If you need more help, visit the Dockerfile reference guide at
# https://docs.docker.com/engine/reference/builder/

ARG PYTHON_VERSION=3.11.5
FROM python:${PYTHON_VERSION}-slim as base

# Prevents Python from writing pyc files.
ENV PYTHONDONTWRITEBYTECODE=1

# Keeps Python from buffering stdout and stderr to avoid situations where
# the application crashes without emitting any logs due to buffering.
ENV PYTHONUNBUFFERED=1

RUN apt-get update -qq && apt-get install -y gcc musl-dev nodejs \
    postgresql-client libpq-dev python3-dev libpq-dev curl vim \
    python3-pip python3-cffi python3-brotli libpango-1.0-0 libharfbuzz0b libpangoft2-1.0-0

RUN curl -sSL https://install.python-poetry.org | python3 -
WORKDIR /api

# Create a non-privileged user that the app will run under.
# See https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user
ARG UID=10001
# RUN adduser \
#     --disabled-password \
#     --gecos "" \
#     --home "/nonexistent" \
#     --shell "/sbin/nologin" \
#     --no-create-home \
#     --uid "${UID}" \
#     appuser

# Switch to the non-privileged user to run the application.
# USER appuser

COPY poetry.lock pyproject.toml /api/

# Add a script to be executed every time the container starts.
COPY entrypoint.sh /usr/bin/
RUN chmod +x /usr/bin/entrypoint.sh

ENV PATH="${PATH}:/root/.local/bin"
RUN export VERIFY_SSL=false
RUN poetry install

COPY . .

ENTRYPOINT ["entrypoint.sh"]
EXPOSE 8000

# Run the application.
CMD gunicorn 'cas_api.wsgi' --bind=0.0.0.0:8000 --workers=3
